import { Component, OnInit, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-dashboard-flow-menu',
  templateUrl: './dashboard-flow-menu.component.html',
  styleUrls: ['./dashboard-flow-menu.component.scss']
})
export class DashboardFlowMenuComponent implements OnInit {

  @Output() onDelete : EventEmitter<string> = new EventEmitter<string>();
  @Output() onClone : EventEmitter<string> = new EventEmitter<string>();
  @Output() onUpload : EventEmitter<string> = new EventEmitter<string>();
  @Output() onDownload : EventEmitter<string> = new EventEmitter<string>();
  @Output() onPublish : EventEmitter<string> = new EventEmitter<string>();

  constructor() { }

  ngOnInit() {
  }

}
