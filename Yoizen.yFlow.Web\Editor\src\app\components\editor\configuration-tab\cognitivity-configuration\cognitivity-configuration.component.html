<div class="configurable-item" [ngStyle]="{'min-height': (isCognitivityEnabled)? '200px' : '150px'}">
  <div class="title">{{ 'COGNITIVITY_CONFIGURATION' | translate }}</div>
  <div class="description">{{'COGNITIVITY_CONFIGURATION_DESCRIPTION' | translate}}</div>
  <div class="cognitivity-configuration">
    <div class="input-group">
      <span class="title">{{'ENABLE_COGNITIVITY' | translate}}</span>
      <ui-switch [(ngModel)]="isCognitivityEnabled" (change)="onCognitivityEnabledChange($event)" color="#45c195" size="small" defaultBgColor="#e0e0e0"
            switchColor="#ffffff" [disabled]="readOnly"></ui-switch>
    </div>
    <div class="input-group" *ngIf="isCognitivityEnabled">
      <span class="title">{{'CONFIG_PRIORITIZECOGNITIVITY' | translate}}</span>
      <ui-switch [(ngModel)]="prioritizeCognitivity" (change)="onGlobalCognitivityPriorityChange($event)" color="#45c195" size="small" defaultBgColor="#e0e0e0"
            switchColor="#ffffff" [disabled]="readOnly"></ui-switch>
    </div>
    <div class="project-list" *ngIf="isCognitivityEnabled">
      <div class="next project">
        <div [ngClass]="{'hide': hasProject()}" *ngIf="!readOnly">
          <input class="input" [ngClass]="{'invalid-state': !isValid()}" type="text"
                 autocomplete="nomecompletesnadaaca"
                 placeholder="{{'PROJECT' | translate}}" [(ngModel)]="searchProjectString" LimitLength
                 (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
          <div #projectPicker class="hide project-selection-anchor">
            <div class="selector-container">
              <div class="scroll-area">
                <div class="project-container" *ngFor="let proj of Projects">
                  <div class="project-name" (click)="selectProject(proj); $event.stopPropagation();">{{proj.name}} - {{'CONFIDENCE' | translate}}: {{proj?.confidence*100}}%</div>
                </div>
                <div class="project-container" *ngIf="EmptyProjectSet">
                  <div class="empty-project-set">{{'EMPTY_PROJECT_SET' | translate}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="project-selected" [ngClass]="{'hide': !hasProject()}">
          <div class="project-display">{{project?.name}} - {{'CONFIDENCE' | translate}}: {{project?.confidence*100}}%</div>
          <div class="fa fa-unlink trash" (click)="deleteProject()" *ngIf="!readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'PROJECT_REMOVESELECTED' | translate }}"
               placement="top" container="body" tooltipClass="tooltip-trash"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="overlay" *ngIf="loading">
  <action-spinner class="spinner"></action-spinner>
</div>