@import "_variables";
@import "_mixins";

.attach {
  width: 500px;

  .definition {
    padding: 10px;

    .name, .mimetype, .url, .publicurl {
      display: flex;
      flex-direction: row;
      margin-top: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      .input-variable-area {
        flex-grow: 1;
      }
    }

    .url, .name {
      margin-top: 0;
    }
  }

  .twitter {
    .empty {
      margin-top: 5px;
      .alert {
        margin-bottom: 0;
      }
    }

    .text {
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      border-bottom: $cardSeparator;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-bottom: 5px;
        justify-self: center;
        align-self: flex-start;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .addQuick {
    @include addPieceButton;
    border-top: $cardSeparator;
  }
}
