<div class="configurable-item" >
  <div class="title">{{ 'HSM_JUMP_CONFIGURATION' | translate }}</div>
  <div class="description">
    <span [innerHTML]="'HSM_JUMP_CONFIGURATION_DESCRIPTION' | translate"></span>
  </div>
  <div class="cognitivity-configuration">
    <span class="title">{{'ENABLE_HSM_JUMP' | translate}}</span>
      <ui-switch [(ngModel)]="isHsmJumpEnabled" (change)="onHsmJumpEnabledChange($event)" color="#45c195" size="small" defaultBgColor="#e0e0e0"
            switchColor="#ffffff"></ui-switch>
  </div>
</div>
