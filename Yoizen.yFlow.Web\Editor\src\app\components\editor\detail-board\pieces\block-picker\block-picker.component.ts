import { Component, OnInit, ViewChild, ElementRef, Renderer2, Output, EventEmitter, Input } from '@angular/core';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { EditorService } from '../../../../../services/editor.service';
import {SectionType} from "../../../../../models/MenuType";
import {PlacementArray} from "@ng-bootstrap/ng-bootstrap/util/positioning";

@Component({
  selector: 'app-block-picker',
  templateUrl: './block-picker.component.html',
  styleUrls: ['./block-picker.component.scss']
})
export class BlockPickerComponent implements OnInit {

  searchBlockString : String;
  @Input() blockId: string;
  @Input() isInvalid: boolean;
  @Input() readOnly : boolean = false;
  @Input() ShowVisibleBlocks : boolean = false;
  @Input() tooltipPlacement : PlacementArray = "bottom";
  @Input() tooltipClass : string = "tooltip-gotoblock";
  blockData : BlockDefinition;

  @ViewChild('blockPicker', { static: false }) BlockPicker : ElementRef;
  @Output() onSelectNewBlock = new EventEmitter<BlockDefinition>();
  @Output() onDeleteBlock = new EventEmitter<BlockDefinition>();

  showBlockPicker: boolean = false;

  constructor(private editorService : EditorService) { }

  ngOnInit() {
  }

  hasBlock() {
    this.blockData = this.getBlockInfo();
    if(!this.blockData) {
      return false;
    }

    const mCurrentModule = this.editorService.getCurrentModule();
    if(this.blockData.ModuleId !== mCurrentModule.id && !this.blockData.IsPublic) {
      return false;
    }

    return true;
  }

  getBlockInfo() {
    if(this.blockData == null || this.blockData.Id != this.blockId) {
      if(this.blockId != null) {
        this.blockData = this.editorService.findBlockWithId(this.blockId);
      }
      else {
        return null;
      }
    }
    return this.blockData;
  }

  onBlockSelect(blockData) {
    let block = this.editorService.findBlockWithId(blockData.Id);
    this.onSelectNewBlock.emit(block);
    this.searchBlockString = blockData.Name;
  }

  deleteBlock() {
    this.onDeleteBlock.emit(this.blockData);
    this.blockData = null;
    this.searchBlockString = "";
  }

  onInputFocusIn() {
    //this.renderer.removeClass(this.BlockPicker.nativeElement, 'hide');
    this.showBlockPicker = true;
  }

  onInputFocusOut() {
    setTimeout(() => {
      //this.renderer.addClass(this.BlockPicker.nativeElement, 'hide');
      this.showBlockPicker = false;
    }, 500);
  }

  goToBlock() {
    if (this.readOnly) {
      return;
    }

    let block = this.editorService.findBlockWithId(this.blockId);
    if (typeof(block) !== 'undefined' &&
      block !== null) {
      this.editorService.selectedBlock(block);

      let state = this.editorService.getEditorState();
      if (state.SelectedTab !== SectionType.Blocks) {
        state.SelectedTab = SectionType.Blocks;
      }
    }
  }
}
