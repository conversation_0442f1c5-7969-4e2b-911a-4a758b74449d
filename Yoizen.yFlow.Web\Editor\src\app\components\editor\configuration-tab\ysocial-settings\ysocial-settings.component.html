<div class="configurable-item" [ngClass]="{ 'invalid': !settings.isValid(editorService) }">
  <div class="title">
    {{ 'CONFIGURATION_YSOCIAL_SETTINGS_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_YSOCIAL_SETTINGS_DESCRIPTION' | translate }}
  </div>
  <div class="data">
    <span class="title">{{ 'CONFIGURATION_YSOCIAL_SETTINGS_URL' | translate }}</span>
    <input type="text"
           class="input"
           [(ngModel)]="settings.Url"
           spellcheck="false"
           [placeholder]="'CONFIGURATION_YSOCIAL_SETTINGS_URL' | translate"
           [disabled]="readOnly || !isYoizenAdmin" />
  </div>
  <div class="returns-from-agent" *ngIf="showReturnsFromAgent">
    <div class="title">
      {{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_TITLE' | translate }}
    </div>
    <div class="more-info">
      {{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_MORE_INFO' | translate }}
    </div>
    <div class="returns-from-agent-table" *ngIf="settings.ReturnsFromAgent !== null && settings.ReturnsFromAgent.length > 0">
      <div class="header">
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_HEADER_NAME' | translate }}</div>
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_HEADER_DESCRIPTION' | translate }}</div>
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_BLOCK' | translate }}</div>
        <div></div>
      </div>
      <div class="row" *ngFor="let returnFromAgent of settings.ReturnsFromAgent let i = index">
        <div class="name">
          <input class="input" type="text" [(ngModel)]="returnFromAgent.Id"
                 [disabled]="readOnly"
                 spellcheck="false" autocomplete="off"/>
        </div>
        <div class="description">
          <input class="input" type="text" [(ngModel)]="returnFromAgent.Description"
                 [disabled]="readOnly"
                 spellcheck="false" autocomplete="off"/>
        </div>
        <div class="block">
          <app-block-picker class="input"
                            [blockId]="returnFromAgent.BlockId"
                            (onSelectNewBlock)="onSelectBlock($event, returnFromAgent)"
                            [readOnly]="readOnly"
                            (onDeleteBlock)="onDeleteBlock($event, returnFromAgent)"
                            [isInvalid]="!returnFromAgent.isBlockValid(editorService)"></app-block-picker>
        </div>
        <div class="trash">
          <div (click)="delete(returnFromAgent, i)" *ngIf="!readOnly"
               tooltipClass="tooltip-trash-left"
               data-toggle="tooltip" ngbTooltip="{{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_REMOVE' | translate }}" placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="settings.ReturnsFromAgent === null || settings.ReturnsFromAgent.length === 0" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="add()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'CONFIGURATION_YSOCIAL_SETTINGS_RETURNSFROMAGENT_ADD' | translate }}
    </div>
  </div>
  <div class="whatsapp-interactive-messages" *ngIf="showWhatsappHSMs">
    <div class="title">
      {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE' | translate }}
    </div>
    <div class="more-info">
      {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE_MORE_INFO' | translate }}
    </div>
    <div class="controls">
      <div class="input-group">
        <label class="label">{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE_USE' | translate }}:</label>
        <ui-switch [(ngModel)]="settings.WhatsappUseInteractive" [disabled]="readOnly"
                   color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      </div>
    </div>
    <div class="whatsapp-interactive-messages-catalog" *ngIf="settings.WhatsappUseInteractive">
      <div class="subtitle">
        {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE_CATALOG' | translate }}
      </div>
      <div class="more-info">
        {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE_CATALOG_MORE_INFO' | translate }}
      </div>
      <div class="controls">
        <div class="input-group">
          <label class="label">{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_INTERACTIVE_USE' | translate }}:</label>
          <ui-switch [(ngModel)]="settings.WhatsappUseInteractiveCatalog" [disabled]="readOnly"
                     color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
        </div>
      </div>
    </div>
  </div>
  <div class="whatsapp-hsms" *ngIf="showWhatsappHSMs">
    <div class="title">
      {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_TITLE' | translate }}
    </div>
    <div class="whatsapp-hsms-table" *ngIf="settings.WhatsappHSMTemplatesServices !== null && settings.WhatsappHSMTemplatesServices.length > 0">
      <div class="header">
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_HEADER_SERVICE_NAME' | translate }}</div>
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_HEADER_SERVICE_NUMBER' | translate }}</div>
        <div>{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_HEADER_HSM_DESCRIPTION' | translate }}</div>
        <div class="center">{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_HEADER_HSM_MEDIA' | translate }}</div>
        <div class="center">{{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_HEADER_HSM_WITHBUTTONS' | translate }}</div>
      </div>
      <div class="row" *ngFor="let service of settings.WhatsappHSMTemplatesServices let i = index">
        <div>
          <span>{{ service.Name }}</span>
        </div>
        <div>
          <span>{{ service.FullPhoneNumber }}</span>
        </div>
        <div class="description">
          <span class="bold">{{ service.Description }}</span><br />
          <span class="italic">{{ service.Namespace }} - {{ service.ElementName }} - {{ service.Language }}</span>
        </div>
        <div class="center">
          <span class="fa fa-check-circle icon-green circle-button" *ngIf="service.HeaderType === hsmTemplateHeaderTypes.Media"></span>
          <span class="fa fa-times-circle icon-red circle-button" *ngIf="service.HeaderType !== hsmTemplateHeaderTypes.Media"></span>
        </div>
        <div class="center">
          <span *ngIf="service.ButtonsType === hsmButtonsType.CallToAction">
            {{ service.Buttons.length }} x <span class="fa fa-bolt"></span>
          </span>
          <span *ngIf="service.ButtonsType === hsmButtonsType.QuickReply">
            {{ service.Buttons.length }} x <span class="fa fa-external-link"></span>
          </span>
          <span *ngIf="service.ButtonsType === hsmButtonsType.Mixed">
            {{ service.Buttons.length }} x <span class="fa fa-random"></span>
          </span>
          <span class="fa fa-times-circle icon-red circle-button" *ngIf="service.ButtonsType === hsmButtonsType.None"></span>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="settings.WhatsappHSMTemplatesServices === null || settings.WhatsappHSMTemplatesServices.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_EMPTY' | translate }}
      </div>
    </div>
    <div class="reload" (click)="refreshWhatsappHSM()" *ngIf="!readOnly">
      <button type="button" class="action-button action-button-default" (click)="refreshWhatsappHSM()">{{'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_RELOAD' | translate}}</button>
    </div>
  </div>
</div>
<div class="overlay" *ngIf="loading">
  <action-spinner class="spinner"></action-spinner>
</div>
