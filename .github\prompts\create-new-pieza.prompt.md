Tu objetivo es crear una nueva pieza para el bot de yFlow. Para ello, deberás seguir los siguientes pasos:
1. Preguntar las siguientes caracteristicas de la pieza a crear si no fueron proporcionadas
1.1. Nombre de la pieza
1.2. Descripción de la pieza
1.3. Objetivo de la pieza
1.4. Comportamiento de la pieza
1.5. Validaciones de la pieza
1.6. Bloques de errores que va a tener
2. Explicar detalladamente lo que entiendiste de cada una de las caracteristicas de la pieza a crear. Pregunta si es correcta la interpretación. En caso afirmativo, continuar. En caso negativo, volver a preguntar el punto 1.
3. Crear la pieza en el front-end. Para ello deberás tener en cuenta que en los archivos a modificar, no debe editarse ni corregirse el resto del código, unicamente agregar las líneas correspondientes a la nueva pieza. 
4. Crear la pieza en el back-end. Para ello deberás tener en cuenta que en los archivos a modificar, no debe editarse ni corregirse el resto del código, unicamente agregar las líneas correspondientes a la nueva pieza. 

Para crear la pieza en el front-end, deberás seguir los siguientes pasos:

1. Para los archivos a modificar, no debe editarse ni corregirse el resto del código, unicamente agregar las líneas correspondientes a la nueva pieza. 
2. Tener en cuenta la forma en la que se crean las piezas en el front-end analizando las piezas ya creadas. El componente de la pieza se podrá ver en [detail-board/pieces/](../../Yoizen.yFlow.Web/Editor/src/app/components/editor/detail-board/pieces/) y el modelo de datos de la pieza se podrá ver en [models/pieces/](../../Yoizen.yFlow.Web/Editor/src/app/models/pieces/)
3. Situarse en [detail-board/pieces/](../../Yoizen.yFlow.Web/Editor/src/app/components/editor/detail-board/pieces/)
4. Crear una nueva carpeta con el nombre de la pieza, seguir la nomenclatura de las piezas que se encuentran en esa carpeta, por ejemplo: `message-piece`, `jump-piece`, `get-element-from-array-piece`, etc.
5. Crear la plantilla html dentro de la carpeta creada. La plantilla debe tener el nombre `nombre-de-la-pieza.component.html`.
- Si te indican que necesitan evaluar o guardar el valor en una variable de yFlow, debés usar algo similar a esto, ya que permite seleccionar las variables de yFlow
```html
  <app-variable-selector-input
      [VariableData]="VariableData"
      [includeImplicit]="true"
      (setVariable)="setVariable($event)"
      [readOnly]="readOnly">
    </app-variable-selector-input>
    
```
- Si te indican que necesitan ingresar un input de texto, o un text area, debés usar algo similar a esto, ya que permite ingresar texto y comandos claves
```html
    <app-input-with-variables [placeholder]="'CONDITION_EXPRESSION' | translate"
                              [(value)]="model.FirstValue"
                              [disabled]="readOnly"
                              class="input"
                              [wideInput]="true"></app-input-with-variables>
```
- Si te indican que debe saltar o ir a un bloque, debés usar algo similar a esto, ya que permite seleccionar el bloque al que se debe ir
```html
    <app-block-picker class="input"
                      [blockId]="model.BlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isBlockValid(editorService)"></app-block-picker>
```
La plantilla debe crearse a partir de lo analizado en el paso 1 y de este ejemplo
```html
<div class="condition card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
       container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div *ngIf="!model.UsesCognitiveEntities" class="card-title">
    <span class="fa fa-check"></span> {{ 'CARD_VARIABLECONDITION_TITLE' | translate }}
  </div>
  <div *ngIf="model.UsesCognitiveEntities" class="card-title">
    <span class="fa fa-check"></span> {{ 'CARD_ENTITYCONDITION_TITLE' | translate }}
  </div>
  <div class="variable-entity-switch" *ngIf="cognitivityEnabled">
    <span class="title">{{'VARIABLECONDITION_SOURCE' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UsesCognitiveEntities" [disabled]="readOnly" (change)="clearCache()"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="source">
    <span class="title" *ngIf="!model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_VARIABLE' | translate}}:</span>
    <span class="title" *ngIf="model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_ENTITY' | translate}}:</span>
    <app-variable-selector-input *ngIf="!model.UsesCognitiveEntities"
      [VariableData]="VariableData"
      [includeImplicit]="true"
      (setVariable)="setVariable($event)"
      [readOnly]="readOnly">
    </app-variable-selector-input>
    <app-entity-selector-input *ngIf="model.UsesCognitiveEntities"
      [readOnly]="readOnly"
      [entity]="selectedEntity"
      (setEntity)="setEntity($event)">
    </app-entity-selector-input>
  </div>
  <div *ngIf="CanAddOperators">
    <div class="operator">
      <span class="title">{{'VARIABLECONDITION_OPERATOR' | translate}}:</span>
      <select class="select" name="" id="" [(ngModel)]="model.Operator" [disabled]="readOnly">
        <option *ngFor="let operator of operators" [ngValue]="operator.value">{{ operator.localized | translate }}
        </option>
      </select>
    </div>
    <div class="value" *ngIf="showOperand()">
      <span class="title">{{'VARIABLECONDITION_VALUETOCOMPARE' | translate}}:</span>
      <app-input-with-variables [placeholder]="'CONDITION_OPERAND' | translate"
                                [(value)]="model.SecondValue"
                                [disabled]="readOnly"
                                class="input-with-variable"
                                [wideInput]="true"
                                [validator]="getSecondInputValidator()"></app-input-with-variables>
    </div>
    <div class="commands" *ngIf="showCommands()">
      <span class="title">{{'VARIABLECONDITION_COMMANDS' | translate}}:</span>
      <div class="commands-selector-container">
        <div class="empty" *ngIf="Commands.length === 0" role="alert">
          <div class="alert alert-info">
            {{ 'COMMANDS_EMPTY' | translate }}
          </div>
        </div>
        <div *ngIf="Commands.length > 0">
          <div class="commands-table">
            <div class="commands-table-header">
              <div></div>
              <div>{{'NAME' | translate}}</div>
            </div>
            <div class="commands-table-row" *ngFor="let command of Commands">
              <div class="commands-table-cell">
                <input type="checkbox"
                       [checked]="isCommandSelected(command)"
                       (change)="changeCommand(command)" />
              </div>
              <div class="commands-table-cell">{{ command.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="normalize" *ngIf="showStringOptions()">
      <span class="title">{{'VARIABLECONDITION_NORMALIZETEXT' | translate}}:</span>
      <ui-switch [(ngModel)]="model.NormalizeText" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      <div class="info" ngbTooltip="{{ 'VARIABLECONDITION_NORMALIZETEXT_INFO' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="next">
      <select class="select" [(ngModel)]="model.ContinueOnTrue" [disabled]="readOnly">
        <option [ngValue]="true">{{ 'CONDITION_CONTINUEIF_TRUE' | translate }}</option>
        <option [ngValue]="false">{{ 'CONDITION_CONTINUEIF_FALSE' | translate }}</option>
      </select>
      <span class="title">{{'CONDITION_CONTINUEON' | translate}}</span>
      <app-block-picker class="input"
                        [blockId]="model.ErrorBlockId"
                        (onSelectNewBlock)="onSelectBlock($event)"
                        (onDeleteBlock)="onDeleteBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
    </div>
  </div>
</div>

```
6. Crear el archivo de estilos css dentro de la carpeta creada. El archivo de estilos debe tener el nombre `nombre-de-la-pieza.component.scss`
7. Crear el archivo de la pieza en angular. El archivo debe tener el nombre `nombre-de-la-pieza.component.ts`
El archivo debe crearse a partir de lo analizado en el paso 1 y de este ejemplo
- Si te indican que necesitan evaluar o guardar el valor en una variable de yFlow, debés usar algo similar a esto, ya que permite seleccionar las variables de yFlow
```typescript
  ...
  get VariableData(): VariableDefinition {
    if (this.model.VariableId < 1000) {
      return this.editorService.findImplicitVariable(this.model.VariableName);
    }
    else {
      return this.editorService.getVariableWithId(this.model.VariableId);
    }
  }
  ...
   setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.operators = OperatorDefinitions.Operators.filter(op => {
        if (
          op.types.findIndex(operand => {
            return operand === variable.Type || operand === TypeDefinition.Any
          }) === -1) {
          return false;
        }

        return true;
      });

      if (variable.Type === TypeDefinition.Text) {
        this.operators.push(OperatorDefinitions.MeetsAnyCommandConditionOperator);
      }

      if (this.operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = this.operators[0].value;
      }

      this.model.VariableId = variable.Id;
      this.model.VariableName = variable.Name;
    }
    else {
      this.model.VariableId = -1;
      this.model.VariableName = null;
      this.operators = null;
    }
  }
  ... 
```
- Si te indican que debe saltar o ir a un bloque, debés usar algo similar a esto, ya que permite seleccionar el bloque al que se debe ir
```typescript
    onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.ErrorBlockId = null;
  }
```
La plantilla debe crearse a partir de lo analizado en el paso y de este ejemplo
```typescript
import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {OperandTypeDefinition, OperatorDefinitions, OperatorType} from '../../../../../models/OperatorType';
import { ConditionPiece } from '../../../../../models/pieces/ConditionPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {VariableConditionPiece} from "../../../../../models/pieces/VariableConditionPiece";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {CommandDefinition} from "../../../../../models/commands/CommandDefinition";
import { Entity } from 'src/app/models/cognitivity/Entity';

@Component({
  selector: 'app-variable-condition-piece',
  templateUrl: './variable-condition-piece.component.html',
  styleUrls: ['./variable-condition-piece.component.scss']
})
export class VariableConditionPieceComponent extends BasePieceVM implements OnInit {
  model: VariableConditionPiece;
  operators = [];
  cognitivityEnabled: boolean = false;
  selectedEntity: Entity;

  get VariableData(): VariableDefinition {
    if (this.model.VariableId < 1000) {
      return this.editorService.findImplicitVariable(this.model.VariableName);
    }
    else {
      return this.editorService.getVariableWithId(this.model.VariableId);
    }
  }

  get Commands(): CommandDefinition[] {
    return this.editorService.getCommands();
  }

  get CanAddOperators(): boolean {
    if (!this.model.UsesCognitiveEntities) {
      return this.model.VariableId !== -1 && this.VariableData !== null;
    }
    return this.model.EntityId !== null;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as VariableConditionPiece;

    if (this.model.VariableId !== -1) {
      this.setVariable(this.VariableData);
    }

    this.cognitivityEnabled = this.editorService.isCognitivityEnabled();
    if (this.cognitivityEnabled) {
      let availableEntities = this.editorService.getEntities();
      let entity = availableEntities.find(e => e.cognitiveServiceId === this.model.EntityId);
      if (entity === undefined) {
        this.model.EntityId = null;
        this.model.EntityName = null;
      } else {
        this.setEntity(entity);
      }
    } else {
      this.model.UsesCognitiveEntities = false;
    }
  }

  setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.operators = OperatorDefinitions.Operators.filter(op => {
        if (
          op.types.findIndex(operand => {
            return operand === variable.Type || operand === TypeDefinition.Any
          }) === -1) {
          return false;
        }

        return true;
      });

      if (variable.Type === TypeDefinition.Text) {
        this.operators.push(OperatorDefinitions.MeetsAnyCommandConditionOperator);
      }

      if (this.operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = this.operators[0].value;
      }

      this.model.VariableId = variable.Id;
      this.model.VariableName = variable.Name;
    }
    else {
      this.model.VariableId = -1;
      this.model.VariableName = null;
      this.operators = null;
    }
  }

  setEntity(entity: Entity) {
    if (typeof(entity) !== 'undefined' && entity != null) {
      this.operators = OperatorDefinitions.EntityOperators;
      if (this.operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = this.operators[0].value;
      }

      this.model.EntityId = entity.cognitiveServiceId;
      this.model.EntityName = entity.name;
      this.selectedEntity = entity;
    } else {
      this.model.EntityId = null;
      this.model.EntityName = null;
      this.selectedEntity = null;
    } 
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.ErrorBlockId = null;
  }

  showOperand(): boolean {
    for (let i = 0; i < OperatorDefinitions.AllOperators.length; i++) {
      if (OperatorDefinitions.AllOperators[i].value === this.model.Operator) {
        return OperatorDefinitions.AllOperators[i].requiresOperand;
      }
    }

    return true;
  }

  showCommands(): boolean {
    if (this.model.Operator === OperatorType.MeetsAnyCommandCondition) {
      return true;
    }

    return false;
  }

  showStringOptions(): boolean {
    return (this.model.EntityId !== null && 
              (this.model.Operator === OperatorType.Equals ||
               this.model.Operator === OperatorType.NotEquals)
            ) || 
           (this.VariableData != null &&
              this.VariableData.Type === TypeDefinition.Text);
  }

  getSecondInputValidator() {
    if (!this.model.UsesCognitiveEntities) {
      return str => {
        return this.model.isSecondValueValid(this.editorService);
      };
    }
    return str => {
      return this.model.isEntityValueValid(this.editorService);
    }
  }

  isCommandSelected(command: CommandDefinition) : boolean {
    if (this.model.Commands === null) {
      return false;
    }

    return this.model.Commands.findIndex(v => v === command.id) >= 0;
  }

  changeCommand(command: CommandDefinition) {
    let index = this.model.Commands.findIndex(v => v === command.id);
    if (index === -1) {
      this.model.Commands.push(command.id);
    }
    else {
      this.model.Commands.splice(index, 1);
    }
  }

  clearCache() {
    this.model.VariableId = -1;
    this.model.VariableName = null;
    this.model.EntityName = null;
    this.model.EntityId = null;
    this.selectedEntity = null;
  }
}
```
8. En el archivo [detail-board/detail-board.component.ts](../../Yoizen.yFlow.Web/Editor/src/app/components/editor/detail-board/detail-board.component.ts) agregar la referencia a la pieza creada anteriormente. Por ejemplo así:
```typescript
    import { NombreDeLaPiezaComponent } from './pieces/nombre-de-la-pieza/nombre-de-la-pieza.component';
```
9. En el archivo [detail-board/detail-board.component.ts](../../Yoizen.yFlow.Web/Editor/src/app/components/editor/detail-board/detail-board.component.ts) registrar el componente creado. Por ejemplo así:
```typescript
    componentHolderService.registerComponent("nombre-de-la-pieza-piece", NombreDeLaPiezaComponent);
```
10. Situarse en [models/pieces/](../../Yoizen.yFlow.Web/Editor/src/app/models/pieces/)

11. Crear el modelo de datos de la pieza en angular. El archivo debe tener el nombre `nombre-de-la-pieza.ts`
El archivo debe crearse a partir de lo analizado en el paso 1 y de este ejemplo
```typescript
import {OperatorType, OperatorDefinitions} from "../OperatorType";
import {BasePiece} from "./BasePiece";
import {jsonMember, jsonObject, jsonArrayMember } from "typedjson";
import {EditorService} from "src/app/services/editor.service";
import {VariableInputValidator} from "src/app/Utils/variable-input-validator";
import {VariableDefinition} from "../VariableDefinition";

@jsonObject
export class VariableConditionPiece extends BasePiece {
  @jsonMember(Boolean)
  UsesCognitiveEntities: boolean = false;
  @jsonMember(Number)
  VariableId: number = -1;
  @jsonMember(String)
  VariableName: string = null;
  @jsonMember(String)
  EntityId: string = null;
  @jsonMember(String)
  EntityName: string = null;
  @jsonMember(String)
  Operator: OperatorType = OperatorType.Equals;
  @jsonMember(String)
  SecondValue: string;
  @jsonMember(String, { deserializer: id => {
    if (typeof(id) === 'number') {
          id = id.toString();
    }
    return id;
  }})
  ErrorBlockId: string = "-1";
  @jsonMember(Boolean)
  NormalizeText: boolean = true;
  @jsonArrayMember(Number)
  Commands: number[] = [];
  @jsonMember(Boolean)
  ContinueOnTrue: boolean = true;

  constructor() {
    super();

    this.ContinueOnTrue = true;
  }

  isValid(editorService: EditorService): boolean {
    if (!this.isVariableIdValid(editorService) && this.EntityId === null) {
      return false;
    }

    return this.Operator !== null &&
      this.isSecondValueValid(editorService) &&
      this.isErrorBlockValid(editorService);
  }

  isVariableIdValid(editorService: EditorService) {
    if (this.VariableId <= 0) {
      return false;
    }

    let variable: VariableDefinition;
    if (this.VariableId < 1000) {
      variable = editorService.findImplicitVariable(this.VariableName);
      if (typeof(variable) === 'undefined' ||
        variable === null) {
        return false;
      }

      return true;
    }
    else {
      variable = editorService.findVariableWithId(this.VariableId, true);
      if (typeof(variable) === 'undefined' ||
        variable === null) {
        return false;
      }

      return true;
    }
  }

  isSecondValueValid(editorService: EditorService): boolean {
    let op = OperatorDefinitions.AllOperators.find(op => op.value == this.Operator);
    if (op.label === OperatorType.MeetsAnyCommandCondition) {
      if (this.Commands === null || this.Commands.length === 0) {
        return false;
      }

      let allCommands = editorService.getCommands();
      for (let i = 0; i < this.Commands.length; i++) {
        let commandIndex = allCommands.findIndex(c => c.id === this.Commands[i]);
        if (commandIndex === -1) {
          return false;
        }

        return true;
      }
    }
    else if (!op.requiresOperand) {
      return true;
    }
    return VariableInputValidator.validateStringWithScripts(this.SecondValue, editorService);
  }

  isEntityValueValid(editorService: EditorService): boolean {
    let op = OperatorDefinitions.AllOperators.find(op => op.value == this.Operator);
    if (op.label === OperatorType.GreaterThan || op.label === OperatorType.LessThan) {
      return VariableInputValidator.validateStringNumberWithScripts(this.SecondValue, editorService);
    }
    return VariableInputValidator.validateStringWithScripts(this.SecondValue, editorService);
  }
  
  isErrorBlockValid(editorService: EditorService) : boolean {
    let block = editorService.findBlockWithId(this.ErrorBlockId);
    return this.ErrorBlockId !== "-1" && block != null && block.validateCurrentModuleBlock(editorService);
  }

  clearBlockDefinition(blockId: string) {
    if (this.ErrorBlockId === blockId) {
      this.ErrorBlockId = "-1";
    }
  }

  referencesBlock(blockId: string) : boolean {
    return this.ErrorBlockId === blockId;
  }

  updateBlockReferences(blockId: string, newBlockId: string) {
    if (this.ErrorBlockId === blockId) {
      this.ErrorBlockId = newBlockId;
    }
  }

  referencesVariable(varDef: VariableDefinition, editorService: EditorService): boolean {
    if (varDef.Id < 1000) {
      if (varDef.Name === this.VariableName) {
        return true;
      }
    }
    else {
      if (varDef.Id === this.VariableId) {
        return true;
      }
    }
    return false;
  }
}
```
12. Agregar la nueva pieza en el factory [pieces/BasePiece.ts](../../Yoizen.yFlow.Web/Editor/src/app/models/pieces/BasePiece.ts). Por ejemplo así:
```typescript
      'nombre-de-la-pieza': 'NombreDeLaPiezaPiece',
```
13. Agregar la nueva pieza en la definición de los bloques [models/BlockDefinition.ts](../../Yoizen.yFlow.Web/Editor/src/app/models/BlockDefinition.ts) Por ejemplo así:
```typescript
      ...
      import { NombreDeLaPiezaPiece } from './pieces/NombreDeLaPieza';
      ....
      knownTypes: [BasePiece
    , ActionsPiece
    , AttachmentPiece
    , ShortenUrlPiece
    , VideoEmbedPiece
    , FormPiece
    ....
    , NombreDeLaPiezaPiece]
    ...
    containsDerivationPiece(editorService: EditorService, inspectedBlocks: string[]): boolean {
    if (this.Pieces === null || this.Pieces.length === 0) {
      return false;
    }

    for (let i = 0; i < this.Pieces.length; i++) {
      let piece = this.Pieces[i];
      if (piece === null) {
        continue;
      }

      if (piece.type === 'derive-piece') {
        return true;
      }

      switch (piece.type) {
        ...
        case 'nombre-de-la-pieza':
          let nombreDeLaPieza = <NombreDeLaPieza>piece;
          if (nombreDeLaPieza.isValid(editorService)) {
            return false;
          }
        }
          break;
```
14. Agregar la pieza en el editor services [services/editor.service.ts](../../Yoizen.yFlow.Web/Editor/src/app/services/editor.service.ts) Por ejemplo así:
```typescript
    import { NombreDeLaPiezaPiece } from '../models/pieces/NombreDeLaPieza';
    ...
    public cloneOnePiece(piece: BasePiece): BasePiece {
    ...
    public createNombreDeLaPieza(): BasePiece {
      return new NombreDeLaPiezaPiece(); // Fix returned class to NombreDeLaPiezaPiece
    }
  ...
    case 'nombre-de-la-pieza':
        newPiece = TypedJSON.parse(json, NombreDeLaPiezaPiece);
        break;
        }

    // Ultima Pieza
    return newPiece;
  }
    ...
      public createPieceFromUntypedObject(piece: any): BasePiece {
    ...
    case 'nombre-de-la-pieza':
        newPiece = TypedJSON.parse<NombreDeLaPiezaPiece>(json, NombreDeLaPiezaPiece);
        break;
    }
    // Ultima pieza

    return newPiece;
  }
    ...
      public isPieceSupportedByType(piece: any, type: string) {
    ...
    case 'nombre-de-la-pieza':
        if (type === FlowTypes.Lite) {
          return false;
        }
        break;
    }

    return true;
  }
    ...
      public isPieceSupportedByChannel(piece: any, channel: string): boolean {
    ...
    case 'nombre-de-la-pieza':
        return true;
    }

    return false;
  }
```
15. Agregar la pieza en el app.module [app/app.module.ts](../../Yoizen.yFlow.Web/Editor/src/app/app.module.ts). Por ejemplo así:
```typescript
    import { NombreDeLaPiezaComponent } from './components/editor/detail-board/pieces/nombre-de-la-pieza/nombre-de-la-pieza.component';
    ...
    declarations: [
    ...
    NombreDeLaPiezaComponent
    ...
    ],
    ...
    entryComponents: [
    ...
    NombreDeLaPiezaComponent
    ...
    ],
```
16. Agregar la pieza en el menú de piezas disponibles [menu-pieces/menu-pieces.component.ts](../../Yoizen.yFlow.Web/Editor/src/app/components/editor/detail-board/menu-pieces/menu-pieces.component.ts). Por ejemplo así
```typescript
    ...
  ngOnInit() {
    ...
    this.Pieces.push(this.editorService.createPiece('PIECE_NOMBRE-DE-LA-PIEZA', 'Icono de font awesome que sea representativo ', 'nombre-de-la-pieza-piece', this.editorService.createNombreDeLaPieza, null, true));
    ...
```
17. No se deben modificar los archivos de es.json, ni pt.json ni en.json.

Para crear la pieza en el back-end, deberás seguir los siguientes pasos:
1. Para los archivos a modificar, no debe editarse ni corregirse el resto del código, unicamente agregar las líneas correspondientes a la nueva pieza. 
2. Tener en cuenta la forma en la que se crean las piezas en el back-end analizando las piezas ya creadas. El componente de la pieza se podrá ver en [executor/pieces/](../../Yoizen.yFlow.WebExecutor/src/domain/models/executor/pieces/) y la implementación de cada una  se podrá ver en [executor/pieces/implementations/](../../Yoizen.yFlow.WebExecutor/src/domain/models/executor/pieces/implementations/)
3. Tener en cuenta que [application/utils/](../../Yoizen.yFlow.WebExecutor/src/application/utils/) es el lugar donde se encuentran las funciones utilitarias que se pueden usar en las piezas. Por ejemplo: `replaceVariablesInText`, `replaceVariablesInTextWithRegex`, `replaceVariablesInTextWithRegexAndReplace`, etc.
4. Si vas a necesitar utilizar una librería externa, deberás preguntar si está de acuerdo con agregarla. En caso de que esté de acuerdo realizar el npm install y en caso de que no, preguntar como quiere resolverlo.
5. Situarse en [executor/pieces/implementations/](../../Yoizen.yFlow.WebExecutor/src/domain/models/executor/pieces/implementations/)
6. Crear una nueva pieza, seguir la nomenclatura de las piezas que se encuentran en esa carpeta, por ejemplo: `MessagePiece`, `JumpToBlockPiece`, `GetElementFromArrayPiece`, etc. Deberá extender de la clase `BasePiece`.
Las propiedades de la clase creada deberán ser iguales a las que fueroon creadas en [pieces/BasePiece.ts](../../Yoizen.yFlow.Web/Editor/src/app/models/pieces/BasePiece.ts) respetando mayúsculas y minúsculas.
- Si te indican que quieren guardar o obtener una variable, se deberá realizar lo siguiente
```typescript
  import { BasePiece } from "../BasePiece";
  import { Control } from "../../Control";
  import { FlowExecutor } from "../../../../../application/executors/FlowExecutorBase";
  import { replaceVariablesInText } from "../../../../../application/utils/textUtils";
  ...
  async execute(control: Control, executor: FlowExecutor, definition: any, jumpedToAnotherBlock: { value: boolean }, block: any, pieceIndex: number): Promise<void> {
    ...
    let variableDef = definition.VariableList.find(v => v.Id === variableId); // Se busca la variable en la lista de variables
    ...
    let text = replaceVariablesInText(valueToSave, control);
    control.runtimeVariables[variableDef.Name] = text; // Se guarda el valor en la variable
    ...
    let value = control.runtimeVariables[variableDef.Name];// Se obtiene el valor de la variable
    ...
  }
```
- Si te indican que debe saltar o ir a un bloque, debés usar algo similar a esto, ya que permite seleccionar el bloque al que se debe ir
```typescript
  import { BasePiece } from "../BasePiece";
  import { Control } from "../../Control";
  import { FlowExecutor } from "../../../../../application/executors/FlowExecutorBase";

  async execute(control: Control, executor: FlowExecutor, definition: any, jumpedToAnotherBlock: { value: boolean }, block: any, pieceIndex: number): Promise<void> {
    ...
    let errorBlock = definition.findBlockById(blockId);
    console.log(`[${control.body.message.id}] Se continuará la ejecución en el bloque ${errorBlock.Name}`);
    await executor.execute(definition, errorBlock, control);

    jumpedToAnotherBlock.value = true;
    ...
  }
```
La plantilla debe crearse a partir de lo analizado en el paso y de este ejemplo
```typescript
import { BasePiece } from "../BasePiece";
import { Control } from "../../Control";
import { FlowExecutor } from "../../../../../application/executors/FlowExecutorBase";
import { replaceVariablesInText } from "../../../../../application/utils/textUtils";

export class MessagePiece extends BasePiece {
    //Estás propiedades son las que se definen en el front-end, respetando mayúsculas y minúsculas
    //[pieces/BasePiece.ts](../../Yoizen.yFlow.Web/Editor/src/app/models/pieces/BasePiece.ts)
    TextList: Array<{ text: string } | string>;
    Buttons?: Array<any>; 

    constructor(piece: any) {
        super(piece);
        Object.assign(this, piece);
    }


    async execute(control: Control, executor: FlowExecutor, definition: any, jumpedToAnotherBlock: { value: boolean }, block: any, pieceIndex: number): Promise<void> {
        let text: boolean | number | string | Date | any;
        if (this.TextList.length > 1) {
            text = this.TextList[Math.floor(Math.random() * this.TextList.length)];
        } else {
            text = this.TextList[0];
        }

        if (typeof (text) === 'object' && typeof (text.text) !== 'undefined') {
            text = text.text;
        }

        text = replaceVariablesInText(text, control);
        let pieceMessage;

        if (typeof (this.Buttons) !== 'undefined' &&
            this.Buttons !== null &&
            this.Buttons.length > 0) {
            pieceMessage = executor.createMessageWithButtons(this, text, control);
        } else {
            pieceMessage = executor.createTextMessage(text, control);
        }

        if (pieceMessage !== null) {
            if (!Array.isArray(control.messages)) {
                control.messages = [];
            }
            control.messages.push(pieceMessage);
            control.conversationMessagesToSave.push(text);
        }
    }
}
```