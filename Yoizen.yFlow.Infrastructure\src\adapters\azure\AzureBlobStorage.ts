import { BlobServiceClient, BlockBlobClient, ContainerClient } from "@azure/storage-blob";
import { IFilePort } from "../../ports/IFilePort";
import { configFileStorage } from "../../../../Yoizen.yFlow.Helpers/src/ConfigFileStorage";
import { config } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import { v4 } from 'uuid';
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class AzureBlobStorageAdapter implements IFilePort {
    private blobServiceClient: BlobServiceClient;
    private containerClient: ContainerClient;

    constructor() {
    }

    /// <summary>
    /// Inicializa el servicio de almacenamiento de blobs
    /// </summary>
    async init(): Promise<void> {
        try {
            const containerName = `yflow-${config.client}`.toLowerCase();
            this.blobServiceClient = BlobServiceClient.fromConnectionString(configFileStorage.storageConnectionString);
            const containerClient = this.blobServiceClient.getContainerClient(containerName);
            const createContainerResponse = await containerClient.createIfNotExists();

            if (createContainerResponse.succeeded) {
                logger.info(`yFlow.Infrastructure Azure Blob has been established successfully - Container "${containerName}" was created successfully.`);
            } else {
                logger.info(`yFlow.Infrastructure Azure Blob has been established successfully - Container "${containerName}" already exists.`);
            }

            this.containerClient = containerClient;
        }
        catch (error) {
            logger.error({ error: error }, `yFlow.Infrastructure Azure Blob error:`);
        }
    }

    /**
     * Sube un archivo al blob
     * @param blobName Nombre del blob
     * @param base64 Archivo en base64
     * @param filename Tipo de contenido del archivo
     * @param contentType Tipo de contenido del archivo
     * @returns Nombre temporal del archivo
     */
    async uploadFile(blobName: string, base64: string, filename: string, contentType: string): Promise<string> {
        try {
            if (!this.containerClient) {
                await this.init();
            }

            const tempName = v4();
            const filePath = `${blobName}\\${tempName}`
            const blobData = Buffer.from(base64, 'base64');
            const blobClient = this.containerClient.getBlockBlobClient(filePath);
            const fileUploaded = await blobClient.uploadData(blobData,
                {
                    blobHTTPHeaders: { blobContentType: contentType },
                    metadata: { client: config.client, name: filename, contenttype: contentType },

                });
            return tempName;
        } catch (error) {
            logger.error({ error: error }, `Error al subir el archivo al blob: ${error}`);
        }
    }

    /**
     * Descarga un archivo del blob
     * @param blobName Nombre del blob
     * @param fileName Nombre del archivo
     */
    async downloadFile(blobName: string, fileName: string): Promise<{ binaryData: string | null; filename: string | null; mimeType: string | null; }> {
        try {
            if (!this.containerClient) {
                await this.init();
            }

            const blobClient = this.containerClient.getBlockBlobClient(`${blobName}\\${fileName}`);
            let fileDownloaded = await blobClient.download();
            const downloadedBuffer = await this.streamToBuffer(fileDownloaded.readableStreamBody);
            const base64String = downloadedBuffer.toString('base64');

            let info = { binaryData: null, filename: null, mimeType: null };
            info.binaryData = base64String;
            info.filename = fileDownloaded.metadata.name;
            info.mimeType = fileDownloaded.metadata.contenttype;
            return info;
        } catch (error) {
            logger.error({ error: error }, `Error al descargar el archivo del blob: ${error}`);
            return null;
        }
    }

    async deleteFile(blobName: string, fileName: string): Promise<void> {
        try {
            if (!this.containerClient) {
                await this.init();
            }

            const blobClient = this.containerClient.getBlockBlobClient(`${blobName}\\${fileName}`);
            await blobClient.deleteIfExists();
        } catch (error) {
            logger.error({ error: error }, `Error al eliminar el archivo del blob: ${error}`);
        }
    }

    // Función auxiliar para convertir un stream a un buffer
    private async streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];
            readableStream.on("data", (data) => {
                chunks.push(data instanceof Buffer ? data : Buffer.from(data));
            });
            readableStream.on("end", () => {
                resolve(Buffer.concat(chunks));
            });
            readableStream.on("error", reject);
        });
    }
}

export default AzureBlobStorageAdapter;