<div class="condition card" [ngClass]="{'invalid-piece': !model.isErrorBlockValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
       container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="far fa-calendar-alt"></span> {{ 'CARD_CALENDAR_TITLE' | translate }}
  </div>
  <div class="calendar">
    <app-business-availability [readOnly]="readOnly" [isInsidePiece]="true" [businessAvailability]="model.Calendar"></app-business-availability>
  </div>
  <div class="next">
    <select class="select" [(ngModel)]="model.ContinueOnTrue" [disabled]="readOnly">
      <option [ngValue]="true">{{ 'CONDITION_CALENDAR_DATE_MATCHES' | translate }}</option>
      <option [ngValue]="false">{{ 'CONDITION_CALENDAR_DATE_DOES_NOT_MATCH' | translate }}</option>
    </select>
    <span class="title">{{'CONDITION_CONTINUEON' | translate}}</span>
    <app-block-picker class="input"
                      [blockId]="model.ErrorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock()"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)">
    </app-block-picker>
  </div>
</div>
