@import '_variables';

.app-block-picker {
  vertical-align: top;
  position: relative;
  display: flex;
  margin-bottom: 10px;
  
  .next {
    width: 100%;
    input {
      width: 100%;

    }
  }

  .invalid-state {
    border-color: $error-color;
  }

  .label {
    margin-right: 10px;
    margin-top: auto;
    margin-bottom: auto;
  }

  .block {
    padding: 0px;
    max-width: 100%;
  }

  .block-no-overflow {
    overflow: hidden;
  }

  .addName input,
  .block input {
    width: 100%;
  }

  .block-selected {
    white-space: nowrap;
    max-width: 100%;

    .trash {
      position: absolute;
    }
  }
}
