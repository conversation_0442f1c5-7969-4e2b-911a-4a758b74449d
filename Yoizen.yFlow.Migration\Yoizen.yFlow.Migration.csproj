<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.22" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.22" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>
