@import '_variables';
@import "_mixins";

.new-flow-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1800px;
  min-width: 1200px;
  height: 100%;
  min-height: 500px;
  background: $popup-background-color;
  border-radius: 5px;
  overflow: hidden;
  align-self: center;
  justify-self: center;
  padding-left: 15px;
  padding-right: 15px;

  .contents {
    flex-grow: 1;
    flex-shrink: 1;
    overflow-y: auto;
    padding-left: 20px;
    padding-right: 20px;

    .content {
      display: inline-block;

      &.new {
        & > .new {
          @include flow;
        }
      }
    }
    
    label {
      margin-right: 5px;
    }

    .name {
      margin-bottom: 5px;
      .input {
        width: 300px;
      }
    }
    .select-channel{
      margin-bottom: 5px;
    }
  }

  .title {
    width: 100%;
    padding: 0 20px;
    margin-bottom: 10px;
    margin-top: 10px;
    line-height: 36px;
    font-size: 20px;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .button-area {
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-content: flex-end;
    margin-top: 5px;
    margin-bottom: 5px;

    .action-button {
      @include popupButton();
    }
  }
}
