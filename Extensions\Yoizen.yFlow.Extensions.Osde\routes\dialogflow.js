const express = require('express');
const router = express.Router();
const errorHandler = require('../helpers/error-handler');
const pjson = require('../package.json');
const dialogflow = require('dialogflow');
const uuid = require('uuid');
const logger = require('../config/winston')

//https://googleapis.dev/nodejs/dialogflow/latest/index.html
callDialogFlowOsde = async (req, res) => {
  const client = new dialogflow.v2.SessionsClient({
    // optional auth parameters.
  });

  const session = uuid.v4();
  // Usar variable de entorno
  const projectId = process.env.PROJECT_ID;
  const sessionPath = client.sessionPath(projectId, session);

  if (req.params.text === '1 PLAN 210 Ciudad de Buenos Aires Boedo - Almagro - Congreso - San Cristóbal'){
    const val = '{"queryResult":{"fulfillmentText":{"sinonimia":[{"id":101, "nombre":"Alergia e inmunología" },{"cantidadPrestadores":0,"id":106,"nombre":"CARDIOLOGÍA                                                                                         ","nombreURI":"CARDIOLOGÍA                                                                                         ","observaciones":{},"rubro":300,"rubroWeb":2,"turnosHabilitado":false},{"cantidadPrestadores":0,"id":902,"nombre":"CARDIÓLOGOS DE GUARDIA                                                                              ","nombreURI":"CARDIÓLOGOS DE GUARDIA                                                                              ","observaciones":{},"rubro":100,"rubroWeb":1,"turnosHabilitado":false}],"rubroPorTexto":{"Fault":{"faultcode":"soap:Client","faultstring":"could not execute query"}},"localidadId":3,"localidadNombre":"Nueva Pompeya - Parque Patricios"}}}';
    res.send(val);
  }


  // The text query request.
  const request = {
    session: sessionPath,
    queryInput: {
      text: {
        // The query to send to the dialogflow agent
        text: req.params.text,
        // The language used by the client (en-US)
        languageCode: 'en-US',
      },
    },
  };
  client.detectIntent(request)
    .then(responses => {
      const response = responses[0];
      logger.info(`La respuesta de dialogFlow de osde fue: ${JSON.stringify(response)}`);
      res.send(response);
    })
    .catch(err => {
      logger.error(err);
    });
};

/* GET users listing. */
router.get('/:text', errorHandler(callDialogFlowOsde));


module.exports = router;