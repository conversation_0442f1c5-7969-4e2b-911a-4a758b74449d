import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { MultiMediaEntry, ErrorMessage } from '../../../../../models/pieces/MultiMediaEntry';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { TypeDefinition } from '../../../../../models/TypeDefinition';
import {Operator, OperatorDefinitions, OperatorType} from 'src/app/models/OperatorType';
import { ChannelTypes } from '../../../../../models/ChannelType';
import { DragulaService } from 'ng2-dragula';
import { v4 as uuidv4 } from 'uuid';

@Component({
  selector: 'app-multimedia-entry-piece',
  templateUrl: './multimedia-entry-piece.component.html',
  styleUrls: ['./multimedia-entry-piece.component.scss']
})
export class MultiMediaEntryPieceComponent extends BasePieceVM implements OnInit {

  model: MultiMediaEntry;
  variableData: VariableDefinition;
  variableTypes = TypeDefinition;
  channelTypes = ChannelTypes;
  MIME_TYPE_ITEMS = uuidv4();

  constructor(editorService: EditorService, public modalService: ModalService, private dragulaService: DragulaService) {
    super(editorService, modalService);

    dragulaService.createGroup(this.MIME_TYPE_ITEMS, {
      accepts: (el, target, source, sibling) => {
        return source.childElementCount !== 1;
      }
    });
  }

  getVariablesTypes() {
    return VariableDefinition.variableType;
  }

  ngOnInit() {
    this.model = this.context as MultiMediaEntry;
    this.flow = this.editorService.getCurrentFlow();
  }

  setVariable(variableData: VariableDefinition) {
    this.variableData = variableData;
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  onSelectTextExceptionBlock(blockData: BlockDefinition) {
    this.model.TextExceptionBlockId = blockData.Id;
  }

  onDeleteTextExceptionBlock(blockData: BlockDefinition) {
    this.model.TextExceptionBlockId = null;
  }

  addNewText() {
    this.model.ErrorMessages.push(new ErrorMessage());
  }

  canAddTextOptions(): boolean {
    if (this.flow.channel === ChannelTypes.Twitter) {
      return this.model.ErrorMessages.length < 7;
    }

    return this.model.ErrorMessages.length < 3;
  }

  deleteErrorMessage(element) {
    this.model.ErrorMessages.splice(element, 1);
  }

  getOperators(): Operator[] {
    const operators: Operator[] = OperatorDefinitions.Operators;
    return operators.filter(op => {
      return op.value === OperatorType.Equals ||
        op.value === OperatorType.NotEquals ||
        op.value === OperatorType.DoesntContains ||
        op.value === OperatorType.Contains ||
        op.value === OperatorType.DoesntEndsWith ||
        op.value === OperatorType.DoesntStartsWith ||
        op.value === OperatorType.EndsWith ||
        op.value === OperatorType.StartsWith;
    });
  }

  showOperand(): boolean {
    const operator = OperatorDefinitions.Operators.find(op => op.value === this.model.Operator);
    if (operator == null) {
      return false;
    }
    return operator.requiresOperand;
  }
}
