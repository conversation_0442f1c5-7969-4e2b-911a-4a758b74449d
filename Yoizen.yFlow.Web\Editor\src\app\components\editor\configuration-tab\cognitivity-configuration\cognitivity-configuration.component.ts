import { TranslateService } from '@ngx-translate/core';
import { ToasterService } from 'angular2-toaster';
import { Intent } from './../../../../models/cognitivity/Intent';
import { TypedJSON } from 'typedjson';
import { ServerService } from 'src/app/services/server.service';
import { CognitivityProject } from './../../../../models/cognitivity/CognitivityProject';
import { EditorService } from 'src/app/services/editor.service';
import { Component, Input, OnInit, Renderer2, ViewChild, ElementRef, EventEmitter } from '@angular/core';
import {normalizeString} from "../../../../urlutils.module";
import {ModalService} from "../../../../services/Tools/ModalService";
import {DeleteCognitivityProjectComponent} from "../../popups/delete-cognitivity-project/delete-cognitivity-project.component";
import { FormDefinition } from 'src/app/models/cognitivity/FormDefinition';
import { Category } from 'src/app/models/cognitivity/Category';
import { ExtractionFormat } from 'src/app/models/cognitivity/ExtractionFormat';


@Component({
  selector: 'app-cognitivity-configuration',
  templateUrl: './cognitivity-configuration.component.html',
  styleUrls: ['./cognitivity-configuration.component.scss']
})
export class CognitivityConfigurationComponent implements OnInit {
  @Input() readOnly: boolean = false;
  isCognitivityEnabled: boolean = false;
  availableProjects : CognitivityProject[] = [];
  currentProjects : CognitivityProject[] = [];
  project: CognitivityProject = null;
  searchProjectString: String = "";
  lastSearchProjectString: String = "";
  prioritizeCognitivity: boolean = true;
  loading: boolean = false;

  @ViewChild('projectPicker', { static: false }) ProjectPicker : ElementRef;

  constructor(
    public editorService: EditorService,
    private modalService: ModalService,
    private serverService: ServerService,
    private renderer: Renderer2,
    private toasterService: ToasterService,
    private translateService: TranslateService) { }

  get Projects() : CognitivityProject[] {
    if (this.searchProjectString !== this.lastSearchProjectString || this.currentProjects === null) {
      this.currentProjects = this.updateProjects();
      this.lastSearchProjectString = this.searchProjectString;
    }
    return this.currentProjects;
  }

  get EmptyProjectSet() : boolean {
    return this.currentProjects.length === 0;
  }

  updateProjects() : CognitivityProject[] {
    let projects = [];

    const lowerProjectname = normalizeString(this.searchProjectString.toLowerCase());
    this.availableProjects.forEach( project => {
      if (normalizeString(project.name.toLowerCase()).includes(lowerProjectname)) {
        projects.push(project);
      }
    });
    return projects;
  }

  ngOnInit() {
    this.isCognitivityEnabled = this.editorService.isCognitivityEnabled();
    this.project = this.editorService.getCurrentCognitivityProject();

    this.availableProjects =  this.editorService.getCognitivityProjects();
    this.currentProjects = this.availableProjects;

    this.prioritizeCognitivity = this.editorService.getGlobalCogntivityPriority();

    if (this.project !== null){
      let newestVersionOfCurrentProject = this.availableProjects.find(p => p.id === this.project.id)
      if (this.project == null || typeof(newestVersionOfCurrentProject) === 'undefined') {
        this.project = null;
      } else {
        this.project.confidence = newestVersionOfCurrentProject.confidence;
        this.project.token = newestVersionOfCurrentProject.token;
        this.editorService.selectCognitivityProject(this.project, false);
      }
    }
  }

  onCognitivityEnabledChange(value: boolean) {
    if (value) {
      this.editorService.enableCognitivity();
    } else {
      this.editorService.disableCognitivity();
      this.project = null;
      this.searchProjectString = "";
    }
  }

  hasProject() {
    let project = this.getProjectInfo();
    if (!project) {
      return false;
    }
    return true;
  }

  getProjectInfo() {
    if (this.project == null || this.project.id == null || this.project.name == null) {
      return null;
    }
    return this.project;
  }

  selectProject(projectData) {
    this.loading = true;
    this.project = projectData;
    this.searchProjectString = projectData.name;
    this.editorService.selectCognitivityProject(projectData);

    let token = this.editorService.getCognitivityProjectToken();
    this.serverService.getIntents(token).subscribe(({success, data}) => {
      if (success) {
        this.editorService.setAvailableIntents(Intent.ParseIntents(data));
      } else {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_INTENTS'));
      }
    },
    error => {
      this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_INTENTS'));
    });

    this.serverService.getEntities(token).subscribe(({success, data}) => {
      if (success) {
        this.editorService.setAvailableEntities(data);
      } else {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_ENTITIES'));
      }
    },
    error => {
      this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_ENTITIES'));
    });

    this.serverService.getForms(token).subscribe(({ success, data }) => {
      if (success) {
        this.editorService.setAvailableForms(FormDefinition.ParseForm(data));
      } else {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_FORMS'));
      }
    },
    error => {
      this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_FORMS'));
    });

    this.serverService.GetCategories(token).subscribe(({ success, data }) => {
      if (success) {
        this.editorService.setAvailableCategories(Category.ParseCategory(data));
      } else {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_CATEGORIES'));
      }
    },
    error => {
      this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_CATEGORIES'));
    });

    this.serverService.GetExtractionFormats(token).subscribe(({ success, data }) => {
      if (success) {
        this.editorService.setAvailableExtractionFormats(ExtractionFormat.ParseExtractionFormats(data));
      } else {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_EXTRACTION_FORMATS'));
      }
    },
    error => {
      this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_EXTRACTION_FORMATS'));
    });


    setTimeout(() => {
      this.loading = false;
    }, 3000, this)
  }

  deleteProject() {
    var acceptEventEmitter = new EventEmitter<Boolean>();
    acceptEventEmitter.subscribe((flag: Boolean) => {
      if(flag){
        this.project = null;
        this.searchProjectString = "";
        this.editorService.deleteCognitivityProject();
      }
    });

    this.modalService.init(
      DeleteCognitivityProjectComponent,
      null, {
        acceptAction: acceptEventEmitter
      }
   );
  }

  onInputFocusIn() {
    this.renderer.removeClass(this.ProjectPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {
    setTimeout(() => {
      this.renderer.addClass(this.ProjectPicker.nativeElement, 'hide');
    }, 500);
  }

  isValid() {
    return this.project != null;
  }

  onGlobalCognitivityPriorityChange(value: boolean) {
    this.editorService.setGlobalCognitivityPriority(value);
  }
}
