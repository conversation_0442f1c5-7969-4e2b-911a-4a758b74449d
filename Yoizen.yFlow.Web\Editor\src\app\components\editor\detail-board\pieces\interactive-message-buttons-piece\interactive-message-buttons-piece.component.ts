import {Component, OnInit, Input, OnDestroy} from '@angular/core';
import { MessagePieceType, Text } from '../../../../../models/pieces/MessagePieceType';
import {ButtonPiece, ButtonType} from '../../../../../models/pieces/ButtonPiece';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { ButtonElementComponent } from '../message-piece/button-element/button-element.component'
import {ChannelTypes} from "../../../../../models/ChannelType";
import { BasePiece } from 'src/app/models/pieces/BasePiece';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';
import {PieceType} from "../../../../../models/PieceType";
import {YSocialSettings} from "../../../../../models/YSocialSettings";
import {
  InteractiveMessageButtonsHeaderTypes,
  InteractiveMessageButtonsPiece
} from "../../../../../models/pieces/InteractiveMessageButtonsPiece";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {SourceTypes} from "../../../../../models/pieces/AttachmentPiece";
import {isUrlValid, isStringValid} from "../../../../../urlutils.module";
import {OutputVariableMap} from "../../../../../models/pieces/IntegrationPiece";
import {environment} from "../../../../../../environments/environment";


@Component({
  selector: 'app-interactive-message-buttons-piece',
  templateUrl: './interactive-message-buttons-piece.component.html',
  styleUrls: ['./interactive-message-buttons-piece.component.scss']
})
export class InteractiveMessageButtonsPieceComponent extends BasePieceVM implements OnInit, OnDestroy {
  subs = new Subscription();

  constructor( editorService : EditorService, public modalService : ModalService, private singleOverlay: SingleOverlayService, private dragulaService : DragulaService ) {
    super(editorService, modalService);
  }

  expandButton : ButtonPiece;
  model : InteractiveMessageButtonsPiece;
  currentBtnDetail : ButtonElementComponent;
  HeaderTypes = InteractiveMessageButtonsHeaderTypes;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];
  ActiveIdString: string;
  channelTypes = ChannelTypes;
  dragulaGroupName: string;
  showIsPublicToggle: boolean = true;

  get variableDefinition(): VariableDefinition {
    return this.editorService.findVariableWithId(this.model.Header.FileDataStorageId);
  }

  ngOnInit() {
    this.model = this.context as InteractiveMessageButtonsPiece;
    this.flow = this.editorService.getCurrentFlow();
    
    this.dragulaGroupName = `INTERACTIVE_MESSAGE_BUTTON_ELEMEN_${this.model.Uid}`;
    if (this.dragulaService.find(this.dragulaGroupName) == null) {
      this.dragulaService.createGroup(this.dragulaGroupName, {
        moves: function (el: any, container: any, handle: any, sibling: any): any {
          let parentElement: HTMLElement = handle.parentElement;
          while (parentElement !== null) {
            if (parentElement.classList.contains('button-content')) {
              break;
            }

            if (parentElement.classList.contains('button-details')) {
              return false;
            }

            parentElement = parentElement.parentElement;
          }

          return true;
        }
      });
    }

    this.subs.add(this.dragulaService.dropModel(this.dragulaGroupName)
      .subscribe(({ el, target, source, sourceModel, targetModel, item }) => {
        this.model.Buttons = targetModel;
      })
    );
    
    // @ts-ignore
    if (typeof(environment.onPremise) !== 'boolean' ||
    // @ts-ignore
    !environment.onPremise) {
    if (this.model.IsPublicUrl) {
      this.showIsPublicToggle = false;
    }
  }

  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.subs.unsubscribe();
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Header.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  addNewText() {
  	this.model.TextList.push(new Text());
  }

  addNewButton() {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = new ButtonPiece();
    this.model.Buttons.push(this.expandButton);
  }

  canAddTextOptions() : boolean {
  	return this.model.TextList.length < 3;
  }

  canAddButton() : boolean {
    return this.model.Buttons.length < 20;
  }

  isTextValid(index: number) {
    return str => { return this.model.isTextValid(index, this.editorService) }
  }

  isHeaderTextValid() {
    return str => { return this.model.Header.isTextValid(this.editorService) }
  }

  isFooterTextValid() {
    return str => { return this.model.isFooterTextValid(this.editorService) }
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }

  deleteButtonElement(element) {
    this.model.Buttons.splice(element, 1);
  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = btn;
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
        this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
        this.model
    );
  }

  getButtonStats(button: ButtonPiece) : any {
    if (this.stats === null ||
      typeof(this.stats.buttons) === 'undefined' ||
      this.stats.buttons === null) {
      return null;
    }

    if (button.Type !== ButtonType.Redirect ||
      button.BlockID === "-1") {
      return null;
    }

    let buttonStatsInfo = this.stats.buttons.find(b => b.id === button.Uid && b.destBlock === button.BlockID);
    if (typeof(buttonStatsInfo) === 'undefined') {
      return null;
    }

    return buttonStatsInfo;
  }

  getNextPiece() : BasePiece {
    let pieces = this.editorService.getEditorState().SelectedBlock.Pieces;

    if (pieces.length === this.index + 1) {
      return null;
    }

    return pieces[this.index + 1];
  }

  canCreateQuickReply() {
    if (this.flow.channel !== ChannelTypes.FacebookMessenger &&
      this.flow.channel !== ChannelTypes.Instagram &&
      this.flow.channel !== ChannelTypes.Chat &&
      this.flow.channel !== ChannelTypes.Twitter &&
      this.flow.channel !== ChannelTypes.Skype &&
      this.flow.channel !== ChannelTypes.Telegram &&
      this.flow.channel !== ChannelTypes.Teams) {
      return false;
    }

    if (this.flow.channel === ChannelTypes.Telegram &&
      this.model.Buttons !== null &&
      this.model.Buttons.length > 0) {
      return false;
    }

    let next = this.getNextPiece();
    if (next != null) {
      if (next.type == 'quick-reply-piece') {
        return false;
      }
    }

    return true;
  }

  containsMoreButtonsThanAllowed(): boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Generic) {
      if (this.model.Buttons.length > 3) {
        return true;
      }
    }

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return this.model.Buttons.length > 3;
      }
    }

    if (this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Telegram ||
      this.flow.channel === ChannelTypes.AppleMessaging ||
      this.flow.channel === ChannelTypes.Teams) {
      if (this.model.Buttons.length > 5) {
        return true;
      }
    }

    return false;
  }

  maxButtonsAllowed(): number {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Generic ||
      this.flow.channel === ChannelTypes.Teams) {
      return 3;
    }

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return 3;
      }
    }

    if (this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Telegram ||
      this.flow.channel === ChannelTypes.AppleMessaging) {
      return 5;
    }

    return 0;
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  isNameValid(str) {
    return str => { return this.model.Header.isNameValid(this.editorService); };
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  setVariableOnOutput(output: OutputVariableMap, variable: VariableDefinition) {
    if (variable != null) {
      this.model.Header.FileDataStorageId = variable.Id;
    }
    else {
      this.model.Header.FileDataStorageId = null;
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case "tab-url":
        this.model.Header.Source = SourceTypes.Url;
        return;
      case "tab-variable":
        this.model.Header.Source = SourceTypes.Variable;
        return
    }
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.Header.isFileDataValid(this.editorService);
  }
}
