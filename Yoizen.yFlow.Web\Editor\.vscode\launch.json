{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Firefox developer ed.",
      "type": "firefox",
      "request": "launch",
      "reAttach": true,
      "preLaunchTask": "Start server",
      "postDebugTask": "Terminate All Tasks",
      "url": "http://localhost:4200",
      "webRoot": "${workspaceFolder}",
      "profile": "dev-profile",
      "keepProfileChanges": true
    },
    {
      "name": "Launch chrome",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "Start server",
      "postDebugTask": "Terminate All Tasks",
      "url": "http://localhost:4200",
      "webRoot": "${workspaceFolder}",
    },
  ]
}
