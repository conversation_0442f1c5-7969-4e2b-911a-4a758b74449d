apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yflow-yoizen-qa-web-ing
  namespace: yflow-yoizen-qa
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: 'letsencrypt-private'
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
    nginx.ingress.kubernetes.io/secure-backends: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: "32m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "64k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "16"
    nginx.ingress.kubernetes.io/proxy-buffers-size: "256k"
spec:
  ingressClassName: nginx-private
  tls:
  - hosts:
    - yflow-yoizen-qa.ysocial.net
    secretName: cert-tls
  rules:
  - host: yflow-yoizen-qa.ysocial.net
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: yflow-yoizen-qa-web-svc
            port:
              number: 3000
