<div class="mail card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-envelope"></span> {{ 'CARD_MAIL_TITLE' | translate }}
  </div>
  <div class="option">
    <span class="title">{{'SEND_TO' | translate}}:</span>
    <app-input-with-variables
      [placeholder]="'SEND_TO' | translate"
      [(value)]="model.To"
      [validator]="model.isToValid.bind(model)"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'SEND_CC' | translate}}:</span>
    <app-input-with-variables
      [placeholder]="'SEND_CC' | translate"
      [(value)]="model.CC"
      [validator]="model.isCCValid.bind(model)"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'SEND_BCC' | translate}}:</span>
    <app-input-with-variables
      [placeholder]="'SEND_BCC' | translate"
      [(value)]="model.BCC"
      [validator]="model.isBCCValid.bind(model)"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option separator">
    <span class="title">{{'SUBJECT' | translate}}:</span>
    <app-input-with-variables
        [placeholder]="'SUBJECT' | translate"
        [(value)]="model.Subject"
        [wideInput]="true"
        [validator]="getSubjectValidator()"
        [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option separator">
    <span class="title">{{'MESSAGE' | translate}}:</span>
    <app-input-with-variables
        [placeholder]="'MESSAGE' | translate"
        [(value)]="model.Message"
        [validator]="getMessageValidator()"
        [isTextArea]="true"
        [wideInput]="true"
        [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option separator">
    <span class="title">{{'MAIL_ATTACH_RECEIVED_ATTACHMENT' | translate}}:</span>
    <ui-switch [(ngModel)]="model.AttachReceivedAttachment" color="#45c195" size="small" defaultBgColor="#e0e0e0"
               switchColor="#ffffff" [disabled]="readOnly"></ui-switch>
  </div>
  <div class="attachments separator">
    <div class="title">{{'MAIL_ATTACH_TITLE' | translate}}</div>
    <div class="info">{{'MAIL_ATTACH_INFO' | translate}}</div>
    <div class="attachments-container">
      <div class="attachment-row" *ngFor="let attachment of model.Attachments let j = index">
        <div class="trash" (click)="deleteAttachment(j)" *ngIf="!readOnly"
             data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
             tooltipClass="tooltip-trash">
          <span class="fa fa-trash-alt"></span>
        </div>
        <div class="data">
          <div class="option">
            <span class="title">{{'MAIL_ATTACH_SOURCE' | translate}}:</span>
            <select class="select" [(ngModel)]="attachment.Source" [disabled]="readOnly">
              <option [ngValue]="mailAttachmentSources.BinaryVariable">{{ 'MAIL_ATTACH_SOURCE_BINARYVARIABLE' | translate}}</option>
              <option [ngValue]="mailAttachmentSources.Url">{{ 'MAIL_ATTACH_SOURCE_URL' | translate}}</option>
            </select>
          </div>
          <div class="option" *ngIf="attachment.Source === mailAttachmentSources.BinaryVariable">
            <span class="title">{{'MAIL_ATTACH_SOURCE_VARIABLE' | translate}}:</span>
            <app-variable-selector-input
              [VariableData]="getVariableDefinition(attachment)"
              (setVariable)="setVariable(attachment, $event)"
              [typeFilters]="variableFilter"
              [readOnly]="readOnly"
              [includeImplicit]="true">
            </app-variable-selector-input>
          </div>
          <div class="option" *ngIf="attachment.Source === mailAttachmentSources.Url">
            <span class="title">{{'MAIL_ATTACH_SOURCE_URL' | translate}}:</span>
            <app-input-with-variables
              [(value)]="attachment.Url"
              [validator]="attachment.isUrlValid.bind(attachment)"
              [wideInput]="true"
              [isTextArea]="false"
              [disabled]="readOnly"
              class="input">
            </app-input-with-variables>
          </div>
          <div class="option">
            <span class="title">{{'MAIL_ATTACH_FILENAME' | translate}}:</span>
            <app-input-with-variables
              [(value)]="attachment.FileName"
              [validator]="attachment.isFileNameValid.bind(attachment, editorService)"
              [wideInput]="true"
              [isTextArea]="false"
              [disabled]="readOnly"
              class="input">
            </app-input-with-variables>
          </div>
          <div class="option">
            <span class="title">{{'MAIL_ATTACH_MIMETYPE' | translate}}:</span>
            <app-input-with-variables
              [(value)]="attachment.MimeType"
              [validator]="attachment.isMimeTypeValid.bind(attachment)"
              [wideInput]="true"
              [isTextArea]="false"
              [disabled]="readOnly"
              [list]="'knowncontenttypes'"
              class="input">
            </app-input-with-variables>
          </div>
        </div>
      </div>
    </div>
    <div class="attachment-add" (click)="addNewAttachment(); $event.stopPropagation();" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'MAIL_ATTACH_ADD' | translate }}
    </div>
  </div>
</div>
