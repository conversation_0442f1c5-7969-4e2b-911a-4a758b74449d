<div class="condition card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
       container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div *ngIf="!model.UsesCognitiveEntities" class="card-title">
    <span class="fa fa-check"></span> {{ 'CARD_VARIABLECONDITION_TITLE' | translate }}
  </div>
  <div *ngIf="model.UsesCognitiveEntities" class="card-title">
    <span class="fa fa-check"></span> {{ 'CARD_ENTITYCONDITION_TITLE' | translate }}
  </div>
  <div class="variable-entity-switch" *ngIf="cognitivityEnabled">
    <span class="title">{{'VARIABLECONDITION_SOURCE' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UsesCognitiveEntities" [disabled]="readOnly" (change)="clearCache()"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="source">
    <span class="title" *ngIf="!model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_VARIABLE' | translate}}:</span>
    <span class="title" *ngIf="model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_ENTITY' | translate}}:</span>
    <app-variable-selector-input *ngIf="!model.UsesCognitiveEntities"
      [VariableData]="VariableData"
      [includeImplicit]="true"
      (setVariable)="setVariable($event)"
      [readOnly]="readOnly">
    </app-variable-selector-input>
    <app-entity-selector-input *ngIf="model.UsesCognitiveEntities"
      [readOnly]="readOnly"
      [entity]="selectedEntity"
      (setEntity)="setEntity($event)">
    </app-entity-selector-input>
  </div>
  <div *ngIf="CanAddOperators">
    <div class="operator">
      <span class="title">{{'VARIABLECONDITION_OPERATOR' | translate}}:</span>
      <select class="select" name="" id="" [(ngModel)]="model.Operator" [disabled]="readOnly">
        <option *ngFor="let operator of operators" [ngValue]="operator.value">{{ operator.localized | translate }}
        </option>
      </select>
    </div>
    <div class="value" *ngIf="showOperand()">
      <span class="title">{{'VARIABLECONDITION_VALUETOCOMPARE' | translate}}:</span>
      <app-input-with-variables [placeholder]="'CONDITION_OPERAND' | translate"
                                [(value)]="model.SecondValue"
                                [disabled]="readOnly"
                                class="input-with-variable"
                                [wideInput]="true"
                                [validator]="getSecondInputValidator()"></app-input-with-variables>
    </div>
    <div class="commands" *ngIf="showCommands()">
      <span class="title">{{'VARIABLECONDITION_COMMANDS' | translate}}:</span>
      <div class="commands-selector-container">
        <div class="empty" *ngIf="Commands.length === 0" role="alert">
          <div class="alert alert-info">
            {{ 'COMMANDS_EMPTY' | translate }}
          </div>
        </div>
        <div *ngIf="Commands.length > 0">
          <div class="commands-table">
            <div class="commands-table-header">
              <div></div>
              <div>{{'NAME' | translate}}</div>
            </div>
            <div class="commands-table-row" *ngFor="let command of Commands">
              <div class="commands-table-cell">
                <input type="checkbox"
                       [checked]="isCommandSelected(command)"
                       (change)="changeCommand(command)" />
              </div>
              <div class="commands-table-cell">{{ command.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="normalize" *ngIf="showStringOptions()">
      <span class="title">{{'VARIABLECONDITION_NORMALIZETEXT' | translate}}:</span>
      <ui-switch [(ngModel)]="model.NormalizeText" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      <div class="info" ngbTooltip="{{ 'VARIABLECONDITION_NORMALIZETEXT_INFO' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="next">
      <select class="select" [(ngModel)]="model.ContinueOnTrue" [disabled]="readOnly">
        <option [ngValue]="true">{{ 'CONDITION_CONTINUEIF_TRUE' | translate }}</option>
        <option [ngValue]="false">{{ 'CONDITION_CONTINUEIF_FALSE' | translate }}</option>
      </select>
      <span class="title">{{'CONDITION_CONTINUEON' | translate}}</span>
      <app-block-picker class="input"
                        [blockId]="model.ErrorBlockId"
                        (onSelectNewBlock)="onSelectBlock($event)"
                        (onDeleteBlock)="onDeleteBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
    </div>
  </div>
</div>
