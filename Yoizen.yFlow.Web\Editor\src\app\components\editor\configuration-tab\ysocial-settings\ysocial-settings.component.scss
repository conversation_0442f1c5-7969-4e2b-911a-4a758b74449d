@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .controls {
    .input-group {
      .label {
        margin-right: 10px;
      }
      .info {
        margin-left: 5px;
        display: inline-block;
        vertical-align: middle;
        color: #767676;

        &:hover {
          color: #555;
        }
      }
    }
  }

  .data {
    padding-top: 10px;
    position: relative;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .title {
      width: 100%;
      margin-right: 5px;
    }

    .input {
      width: 50%;
    }
  }

  .returns-from-agent {
    margin-top: 20px;
    border-top: 1px solid $cardSeparatorBorderColor;
    padding-top: 5px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    & > .more-info {
      font-family: $fontFamily;
      font-size: 14px;
      margin-bottom: 5px;
      color: #767676;
    }

    .returns-from-agent-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;
        font-weight: bold;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .name {
          width: 150px;
          input {
            font-family: $fontFamilyMono;
          }
        }

        .description {
          min-width: 300px;
          max-width: 500px;

          input {
            width: 100%;
          }
        }

        .trash {
          width: 30px;

          & > div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }

    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }

  .whatsapp-hsms, .whatsapp-interactive-messages {
    margin-top: 20px;
    border-top: 1px solid $cardSeparatorBorderColor;
    padding-top: 5px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 10px;
    }

    & > .more-info {
      font-family: $fontFamily;
      font-size: 14px;
      margin-bottom: 5px;
      color: #767676;
    }

    .whatsapp-hsms-table {
      display: table;
      width: 100%;
      margin-bottom: 5px;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;
        font-weight: bold;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;

          &.center {
            text-align: center;
          }
        }
      }

      .row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .description {
          min-width: 300px;
          max-width: 500px;

          .bold {
            font-weight: bold;
          }

          .italic {
            font-style: italic;
          }
        }

        .center {
          text-align: center;
        }
      }
    }

    .empty {
      .alert {
        margin-bottom: 5px;
      }
    }

    .reload {
      .action-button {
        margin: 0;
      }
    }

    .whatsapp-interactive-messages-catalog {
      margin-left: 20px;
      margin-top: 10px;

      & > .subtitle {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        font-size: 105%;
        margin-bottom: 5px;
      }

      & > .more-info {
        font-family: $fontFamily;
        font-size: 14px;
        margin-bottom: 5px;
        color: #767676;
      }
    }
  }

  .user-formats {
    .format-name {
      width: 100px;
      padding-right: 10px;
      .format-key {
        font-family: $fontFamilyMono;
        width: 100%;
      }
    }
  }
}

.overlay {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #00000044;
  z-index: 100000;

  .spinner {
    display: inline-block;
    position: absolute;
    left: calc(50% - 32px);
    top: calc(50% - 32px);
  }
}
