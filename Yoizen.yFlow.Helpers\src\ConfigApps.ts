import { isEnvironmentVariableValid, parseToInt } from "./Helpers";
import { logger } from "./Logger";
export class ConfigApps {
    googleMapApiKeyYoizen: string;
    ySocialUrl: string;
    yflowUrl: string;
    yFlowUtilsUrl: string;
    yBiometricUrl: string;
    urlApiCognitiveServices: string;
    cognitivityApiVersion: string;
    yflowUrlAccountLinkingPostback: string;
    yflowUrlLocationPicker: string;
    yoizMeToken: string;
    yoizMeUrl: string;
    dniServiceUrl: string;
    dniServiceApiKey: string;
    defaultTimeout: number;
}
export const configApps: ConfigApps = {
  googleMapApiKeyYoizen:
    process.env.NODE_ENV === "dev"
      ? "AIzaSyASdb4ciNEaz9P6ga_azIrG5TCk8HhjFaw" // key publica
      : "AIzaSyCP_TlB4MEScEaqlTdghb375ARjIMec61c", // key productiva
  ySocialUrl: process.env.ySocialUrl,
  yflowUrl: process.env.yflowUrl,
  yFlowUtilsUrl: process.env.yFlowUtilsUrl || "https://common.ysocial.net",
  yBiometricUrl: process.env.yBiometricUrl || "https://biometric.ysocial.net",
  urlApiCognitiveServices: process.env.urlApiCognitiveServices,
  cognitivityApiVersion: process.env.cognitivityApiVersion,
  yflowUrlAccountLinkingPostback: process.env.yflowUrlAccountLinkingPostback,
  yflowUrlLocationPicker: process.env.yflowUrlLocationPicker,
  yoizMeToken: process.env.yoizMeToken || "6c95c2a1c8",
  yoizMeUrl: process.env.yoizMeUrl || "yoiz.me",
  dniServiceUrl: process.env.dniServiceUrl,
  dniServiceApiKey: process.env.dniServiceApiKey,
  defaultTimeout: parseToInt(process.env.defaultTimeout, 60000),
};

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(configApps.ySocialUrl)) {
        logger.error(`faltan inicializar variables de entorno: ySocialUrl`);
        process.exit(9);
    }

    if (!isEnvironmentVariableValid(configApps.yflowUrl)) {
        logger.error(`faltan inicializar variables de entorno: yflowUrl`);
        process.exit(9);
    }

    if (isEnvironmentVariableValid(configApps.urlApiCognitiveServices)) {
        if (!isEnvironmentVariableValid(configApps.cognitivityApiVersion)) {
            logger.error(`faltan inicializar variables de entorno: cognitivityApiVersion`);
            process.exit(9);
        }
    }

    if (!isEnvironmentVariableValid(configApps.dniServiceUrl) ||
        !isEnvironmentVariableValid(configApps.dniServiceApiKey)) {
        logger.error(`faltan inicializar variables de entorno: del dni`);
        process.exit(9);
    }

}

validateAndFormatConfig();