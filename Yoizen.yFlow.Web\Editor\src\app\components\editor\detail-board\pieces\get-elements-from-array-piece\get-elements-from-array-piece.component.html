<div class="get-element-from-array card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
    <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
         container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
    <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-clone"></span>
    </div>
    <div class="export" (click)="exportAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-file-export"></span>
    </div>
    <div class="card-title">
      <span class="fa fa-tasks"></span> {{ 'CARD_GETELEMENTSFROMARRAY_TITLE' | translate }}
    </div>
    <div class="card-info">
      {{ 'CARD_GETELEMENTSFROMARRAY_INFO' | translate }}
    </div>
    <div class="source">
      <span class="title">{{'GETELEMENTSFROMARRAY_SOURCE_VARIABLE' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="VariableData"
        [includeImplicit]="true"
        (setVariable)="setVariable($event)"
        [readOnly]="readOnly"
        [typeFilters]="variableFilter">
      </app-variable-selector-input>
    </div>
    <div class="item-container" *ngIf="model.VariableId !== -1">
        <div class="implicit-variables">
            <div class="title">{{'GETELEMENTFROMARRAY_VARIABLELIST' | translate}}</div>
            <div class="variables-table">
              <div class="variables-header">
                <div>{{'NAME' | translate}}</div>
                <div>{{'DESCRIPTION' | translate}}</div>
              </div>
              <div class="variable-row" *ngFor="let variable of customVariables">
                <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
                <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
              </div>
            </div>
        </div>
      <div class="where-prop">
        <app-input-with-variables
          [placeholder]="'GETELEMENTSFROMARRAY_FINDEXPRESSION_PROP' | translate"
          [(value)]="model.FindExpression"
          [validator]="isUrlValid"
          [wideInput]="true"
          [isTextArea]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [extendedStyles]="{'height': '125px', 'min-height': '50px', 'max-height': '200px'}"
          [disabled]="readOnly"
          >
        </app-input-with-variables>
      </div>
      <div class="dest">
        <span class="title">{{'GETELEMENTSFROMARRAY_DEST_VARIABLE' | translate}}:</span>
        <app-variable-selector-input
          [VariableData]="StoreVariableData"
          [includeImplicit]="false"
          (setVariable)="setStoreVariable($event)"
          [readOnly]="readOnly"
          [typeFilters]="storeVariableFilter">
        </app-variable-selector-input>
      </div>
    </div>
  </div>
