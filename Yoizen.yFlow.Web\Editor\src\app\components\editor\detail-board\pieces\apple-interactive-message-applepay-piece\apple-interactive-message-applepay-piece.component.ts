import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";
import {
  AppleInteractiveMessageAuthenticationPiece, AppleInteractiveMessageAuthenticationResponseTypes
} from "../../../../../models/pieces/AppleInteractiveMessageAuthenticationPiece";
import {
  AppleInteractiveMessageIMessageAppPiece
} from "../../../../../models/pieces/AppleInteractiveMessageIMessageAppPiece";
import {
  AppleInteractiveMessageApplePayPiece, ApplePayContactField, ApplePayLineItemType,
  ApplePayMerchantCapabilities, ApplePaySupportedNetworks, ApplePayShippingMethod, ApplePayLineItem
} from "../../../../../models/pieces/AppleInteractiveMessageApplePayPiece";
import {ApplePayMerchantConfig} from "../../../../../models/ApplePayMerchantConfig";

@Component({
  selector: 'app-apple-interactive-message-applepay-piece',
  templateUrl: './apple-interactive-message-applepay-piece.component.html',
  styleUrls: ['./apple-interactive-message-applepay-piece.component.scss']
})
export class AppleInteractiveMessageApplePayPieceComponent extends BasePieceVM implements OnInit {

  model : AppleInteractiveMessageApplePayPiece;
  merchantCapabilities = ApplePayMerchantCapabilities;
  applePaySupportedNetworks = ApplePaySupportedNetworks;
  applePayLineItemType = ApplePayLineItemType;
  applePayContactField = ApplePayContactField;
  loading: boolean = false;
  applePayMerchantsConfig: ApplePayMerchantConfig[] = null;

  countryCodesIso = [
    {
      "code": "AF",
      "name": "Afghanistan"
    },
    {
      "code": "AX",
      "name": "Åland Islands"
    },
    {
      "code": "AL",
      "name": "Albania"
    },
    {
      "code": "DZ",
      "name": "Algeria"
    },
    {
      "code": "AS",
      "name": "American Samoa"
    },
    {
      "code": "AD",
      "name": "Andorra"
    },
    {
      "code": "AO",
      "name": "Angola"
    },
    {
      "code": "AI",
      "name": "Anguilla"
    },
    {
      "code": "AQ",
      "name": "Antarctica"
    },
    {
      "code": "AG",
      "name": "Antigua and Barbuda"
    },
    {
      "code": "AR",
      "name": "Argentina"
    },
    {
      "code": "AM",
      "name": "Armenia"
    },
    {
      "code": "AW",
      "name": "Aruba"
    },
    {
      "code": "AU",
      "name": "Australia"
    },
    {
      "code": "AT",
      "name": "Austria"
    },
    {
      "code": "AZ",
      "name": "Azerbaijan"
    },
    {
      "code": "BS",
      "name": "Bahamas"
    },
    {
      "code": "BH",
      "name": "Bahrain"
    },
    {
      "code": "BD",
      "name": "Bangladesh"
    },
    {
      "code": "BB",
      "name": "Barbados"
    },
    {
      "code": "BY",
      "name": "Belarus"
    },
    {
      "code": "BE",
      "name": "Belgium"
    },
    {
      "code": "BZ",
      "name": "Belize"
    },
    {
      "code": "BJ",
      "name": "Benin"
    },
    {
      "code": "BM",
      "name": "Bermuda"
    },
    {
      "code": "BT",
      "name": "Bhutan"
    },
    {
      "code": "BO",
      "name": "Bolivia, Plurinational State of"
    },
    {
      "code": "BQ",
      "name": "Bonaire, Sint Eustatius and Saba"
    },
    {
      "code": "BA",
      "name": "Bosnia and Herzegovina"
    },
    {
      "code": "BW",
      "name": "Botswana"
    },
    {
      "code": "BV",
      "name": "Bouvet Island"
    },
    {
      "code": "BR",
      "name": "Brazil"
    },
    {
      "code": "IO",
      "name": "British Indian Ocean Territory"
    },
    {
      "code": "BN",
      "name": "Brunei Darussalam"
    },
    {
      "code": "BG",
      "name": "Bulgaria"
    },
    {
      "code": "BF",
      "name": "Burkina Faso"
    },
    {
      "code": "BI",
      "name": "Burundi"
    },
    {
      "code": "KH",
      "name": "Cambodia"
    },
    {
      "code": "CM",
      "name": "Cameroon"
    },
    {
      "code": "CA",
      "name": "Canada"
    },
    {
      "code": "CV",
      "name": "Cape Verde"
    },
    {
      "code": "KY",
      "name": "Cayman Islands"
    },
    {
      "code": "CF",
      "name": "Central African Republic"
    },
    {
      "code": "TD",
      "name": "Chad"
    },
    {
      "code": "CL",
      "name": "Chile"
    },
    {
      "code": "CN",
      "name": "China"
    },
    {
      "code": "CX",
      "name": "Christmas Island"
    },
    {
      "code": "CC",
      "name": "Cocos (Keeling) Islands"
    },
    {
      "code": "CO",
      "name": "Colombia"
    },
    {
      "code": "KM",
      "name": "Comoros"
    },
    {
      "code": "CG",
      "name": "Congo"
    },
    {
      "code": "CD",
      "name": "Congo, the Democratic Republic of the"
    },
    {
      "code": "CK",
      "name": "Cook Islands"
    },
    {
      "code": "CR",
      "name": "Costa Rica"
    },
    {
      "code": "CI",
      "name": "Côte d'Ivoire"
    },
    {
      "code": "HR",
      "name": "Croatia"
    },
    {
      "code": "CU",
      "name": "Cuba"
    },
    {
      "code": "CW",
      "name": "Curaçao"
    },
    {
      "code": "CY",
      "name": "Cyprus"
    },
    {
      "code": "CZ",
      "name": "Czech Republic"
    },
    {
      "code": "DK",
      "name": "Denmark"
    },
    {
      "code": "DJ",
      "name": "Djibouti"
    },
    {
      "code": "DM",
      "name": "Dominica"
    },
    {
      "code": "DO",
      "name": "Dominican Republic"
    },
    {
      "code": "EC",
      "name": "Ecuador"
    },
    {
      "code": "EG",
      "name": "Egypt"
    },
    {
      "code": "SV",
      "name": "El Salvador"
    },
    {
      "code": "GQ",
      "name": "Equatorial Guinea"
    },
    {
      "code": "ER",
      "name": "Eritrea"
    },
    {
      "code": "EE",
      "name": "Estonia"
    },
    {
      "code": "ET",
      "name": "Ethiopia"
    },
    {
      "code": "FK",
      "name": "Falkland Islands (Malvinas)"
    },
    {
      "code": "FO",
      "name": "Faroe Islands"
    },
    {
      "code": "FJ",
      "name": "Fiji"
    },
    {
      "code": "FI",
      "name": "Finland"
    },
    {
      "code": "FR",
      "name": "France"
    },
    {
      "code": "GF",
      "name": "French Guiana"
    },
    {
      "code": "PF",
      "name": "French Polynesia"
    },
    {
      "code": "TF",
      "name": "French Southern Territories"
    },
    {
      "code": "GA",
      "name": "Gabon"
    },
    {
      "code": "GM",
      "name": "Gambia"
    },
    {
      "code": "GE",
      "name": "Georgia"
    },
    {
      "code": "DE",
      "name": "Germany"
    },
    {
      "code": "GH",
      "name": "Ghana"
    },
    {
      "code": "GI",
      "name": "Gibraltar"
    },
    {
      "code": "GR",
      "name": "Greece"
    },
    {
      "code": "GL",
      "name": "Greenland"
    },
    {
      "code": "GD",
      "name": "Grenada"
    },
    {
      "code": "GP",
      "name": "Guadeloupe"
    },
    {
      "code": "GU",
      "name": "Guam"
    },
    {
      "code": "GT",
      "name": "Guatemala"
    },
    {
      "code": "GG",
      "name": "Guernsey"
    },
    {
      "code": "GN",
      "name": "Guinea"
    },
    {
      "code": "GW",
      "name": "Guinea-Bissau"
    },
    {
      "code": "GY",
      "name": "Guyana"
    },
    {
      "code": "HT",
      "name": "Haiti"
    },
    {
      "code": "HM",
      "name": "Heard Island and McDonald Islands"
    },
    {
      "code": "VA",
      "name": "Holy See (Vatican City State)"
    },
    {
      "code": "HN",
      "name": "Honduras"
    },
    {
      "code": "HK",
      "name": "Hong Kong"
    },
    {
      "code": "HU",
      "name": "Hungary"
    },
    {
      "code": "IS",
      "name": "Iceland"
    },
    {
      "code": "IN",
      "name": "India"
    },
    {
      "code": "ID",
      "name": "Indonesia"
    },
    {
      "code": "IR",
      "name": "Iran, Islamic Republic of"
    },
    {
      "code": "IQ",
      "name": "Iraq"
    },
    {
      "code": "IE",
      "name": "Ireland"
    },
    {
      "code": "IM",
      "name": "Isle of Man"
    },
    {
      "code": "IL",
      "name": "Israel"
    },
    {
      "code": "IT",
      "name": "Italy"
    },
    {
      "code": "JM",
      "name": "Jamaica"
    },
    {
      "code": "JP",
      "name": "Japan"
    },
    {
      "code": "JE",
      "name": "Jersey"
    },
    {
      "code": "JO",
      "name": "Jordan"
    },
    {
      "code": "KZ",
      "name": "Kazakhstan"
    },
    {
      "code": "KE",
      "name": "Kenya"
    },
    {
      "code": "KI",
      "name": "Kiribati"
    },
    {
      "code": "KP",
      "name": "Korea, Democratic People's Republic of"
    },
    {
      "code": "KR",
      "name": "Korea, Republic of"
    },
    {
      "code": "KW",
      "name": "Kuwait"
    },
    {
      "code": "KG",
      "name": "Kyrgyzstan"
    },
    {
      "code": "LA",
      "name": "Lao People's Democratic Republic"
    },
    {
      "code": "LV",
      "name": "Latvia"
    },
    {
      "code": "LB",
      "name": "Lebanon"
    },
    {
      "code": "LS",
      "name": "Lesotho"
    },
    {
      "code": "LR",
      "name": "Liberia"
    },
    {
      "code": "LY",
      "name": "Libya"
    },
    {
      "code": "LI",
      "name": "Liechtenstein"
    },
    {
      "code": "LT",
      "name": "Lithuania"
    },
    {
      "code": "LU",
      "name": "Luxembourg"
    },
    {
      "code": "MO",
      "name": "Macao"
    },
    {
      "code": "MK",
      "name": "Macedonia, the Former Yugoslav Republic of"
    },
    {
      "code": "MG",
      "name": "Madagascar"
    },
    {
      "code": "MW",
      "name": "Malawi"
    },
    {
      "code": "MY",
      "name": "Malaysia"
    },
    {
      "code": "MV",
      "name": "Maldives"
    },
    {
      "code": "ML",
      "name": "Mali"
    },
    {
      "code": "MT",
      "name": "Malta"
    },
    {
      "code": "MH",
      "name": "Marshall Islands"
    },
    {
      "code": "MQ",
      "name": "Martinique"
    },
    {
      "code": "MR",
      "name": "Mauritania"
    },
    {
      "code": "MU",
      "name": "Mauritius"
    },
    {
      "code": "YT",
      "name": "Mayotte"
    },
    {
      "code": "MX",
      "name": "Mexico"
    },
    {
      "code": "FM",
      "name": "Micronesia, Federated States of"
    },
    {
      "code": "MD",
      "name": "Moldova, Republic of"
    },
    {
      "code": "MC",
      "name": "Monaco"
    },
    {
      "code": "MN",
      "name": "Mongolia"
    },
    {
      "code": "ME",
      "name": "Montenegro"
    },
    {
      "code": "MS",
      "name": "Montserrat"
    },
    {
      "code": "MA",
      "name": "Morocco"
    },
    {
      "code": "MZ",
      "name": "Mozambique"
    },
    {
      "code": "MM",
      "name": "Myanmar"
    },
    {
      "code": "NA",
      "name": "Namibia"
    },
    {
      "code": "NR",
      "name": "Nauru"
    },
    {
      "code": "NP",
      "name": "Nepal"
    },
    {
      "code": "NL",
      "name": "Netherlands"
    },
    {
      "code": "NC",
      "name": "New Caledonia"
    },
    {
      "code": "NZ",
      "name": "New Zealand"
    },
    {
      "code": "NI",
      "name": "Nicaragua"
    },
    {
      "code": "NE",
      "name": "Niger"
    },
    {
      "code": "NG",
      "name": "Nigeria"
    },
    {
      "code": "NU",
      "name": "Niue"
    },
    {
      "code": "NF",
      "name": "Norfolk Island"
    },
    {
      "code": "MP",
      "name": "Northern Mariana Islands"
    },
    {
      "code": "NO",
      "name": "Norway"
    },
    {
      "code": "OM",
      "name": "Oman"
    },
    {
      "code": "PK",
      "name": "Pakistan"
    },
    {
      "code": "PW",
      "name": "Palau"
    },
    {
      "code": "PS",
      "name": "Palestine, State of"
    },
    {
      "code": "PA",
      "name": "Panama"
    },
    {
      "code": "PG",
      "name": "Papua New Guinea"
    },
    {
      "code": "PY",
      "name": "Paraguay"
    },
    {
      "code": "PE",
      "name": "Peru"
    },
    {
      "code": "PH",
      "name": "Philippines"
    },
    {
      "code": "PN",
      "name": "Pitcairn"
    },
    {
      "code": "PL",
      "name": "Poland"
    },
    {
      "code": "PT",
      "name": "Portugal"
    },
    {
      "code": "PR",
      "name": "Puerto Rico"
    },
    {
      "code": "QA",
      "name": "Qatar"
    },
    {
      "code": "RE",
      "name": "Réunion"
    },
    {
      "code": "RO",
      "name": "Romania"
    },
    {
      "code": "RU",
      "name": "Russian Federation"
    },
    {
      "code": "RW",
      "name": "Rwanda"
    },
    {
      "code": "BL",
      "name": "Saint Barthélemy"
    },
    {
      "code": "SH",
      "name": "Saint Helena, Ascension and Tristan da Cunha"
    },
    {
      "code": "KN",
      "name": "Saint Kitts and Nevis"
    },
    {
      "code": "LC",
      "name": "Saint Lucia"
    },
    {
      "code": "MF",
      "name": "Saint Martin (French part)"
    },
    {
      "code": "PM",
      "name": "Saint Pierre and Miquelon"
    },
    {
      "code": "VC",
      "name": "Saint Vincent and the Grenadines"
    },
    {
      "code": "WS",
      "name": "Samoa"
    },
    {
      "code": "SM",
      "name": "San Marino"
    },
    {
      "code": "ST",
      "name": "Sao Tome and Principe"
    },
    {
      "code": "SA",
      "name": "Saudi Arabia"
    },
    {
      "code": "SN",
      "name": "Senegal"
    },
    {
      "code": "RS",
      "name": "Serbia"
    },
    {
      "code": "SC",
      "name": "Seychelles"
    },
    {
      "code": "SL",
      "name": "Sierra Leone"
    },
    {
      "code": "SG",
      "name": "Singapore"
    },
    {
      "code": "SX",
      "name": "Sint Maarten (Dutch part)"
    },
    {
      "code": "SK",
      "name": "Slovakia"
    },
    {
      "code": "SI",
      "name": "Slovenia"
    },
    {
      "code": "SB",
      "name": "Solomon Islands"
    },
    {
      "code": "SO",
      "name": "Somalia"
    },
    {
      "code": "ZA",
      "name": "South Africa"
    },
    {
      "code": "GS",
      "name": "South Georgia and the South Sandwich Islands"
    },
    {
      "code": "SS",
      "name": "South Sudan"
    },
    {
      "code": "ES",
      "name": "Spain"
    },
    {
      "code": "LK",
      "name": "Sri Lanka"
    },
    {
      "code": "SD",
      "name": "Sudan"
    },
    {
      "code": "SR",
      "name": "Suriname"
    },
    {
      "code": "SJ",
      "name": "Svalbard and Jan Mayen"
    },
    {
      "code": "SZ",
      "name": "Swaziland"
    },
    {
      "code": "SE",
      "name": "Sweden"
    },
    {
      "code": "CH",
      "name": "Switzerland"
    },
    {
      "code": "SY",
      "name": "Syrian Arab Republic"
    },
    {
      "code": "TW",
      "name": "Taiwan, Province of China"
    },
    {
      "code": "TJ",
      "name": "Tajikistan"
    },
    {
      "code": "TZ",
      "name": "Tanzania, United Republic of"
    },
    {
      "code": "TH",
      "name": "Thailand"
    },
    {
      "code": "TL",
      "name": "Timor-Leste"
    },
    {
      "code": "TG",
      "name": "Togo"
    },
    {
      "code": "TK",
      "name": "Tokelau"
    },
    {
      "code": "TO",
      "name": "Tonga"
    },
    {
      "code": "TT",
      "name": "Trinidad and Tobago"
    },
    {
      "code": "TN",
      "name": "Tunisia"
    },
    {
      "code": "TR",
      "name": "Turkey"
    },
    {
      "code": "TM",
      "name": "Turkmenistan"
    },
    {
      "code": "TC",
      "name": "Turks and Caicos Islands"
    },
    {
      "code": "TV",
      "name": "Tuvalu"
    },
    {
      "code": "UG",
      "name": "Uganda"
    },
    {
      "code": "UA",
      "name": "Ukraine"
    },
    {
      "code": "AE",
      "name": "United Arab Emirates"
    },
    {
      "code": "GB",
      "name": "United Kingdom"
    },
    {
      "code": "US",
      "name": "United States"
    },
    {
      "code": "UM",
      "name": "United States Minor Outlying Islands"
    },
    {
      "code": "UY",
      "name": "Uruguay"
    },
    {
      "code": "UZ",
      "name": "Uzbekistan"
    },
    {
      "code": "VU",
      "name": "Vanuatu"
    },
    {
      "code": "VE",
      "name": "Venezuela, Bolivarian Republic of"
    },
    {
      "code": "VN",
      "name": "Viet Nam"
    },
    {
      "code": "VG",
      "name": "Virgin Islands, British"
    },
    {
      "code": "VI",
      "name": "Virgin Islands, U.S."
    },
    {
      "code": "WF",
      "name": "Wallis and Futuna"
    },
    {
      "code": "EH",
      "name": "Western Sahara"
    },
    {
      "code": "YE",
      "name": "Yemen"
    },
    {
      "code": "ZM",
      "name": "Zambia"
    },
    {
      "code": "ZW",
      "name": "Zimbabwe"
    }
  ];
  currencies = [
      {
        "code": "AED",
        "decimals": 2,
        "name": "United Arab Emirates dirham",
        "number": "784"
      },
      {
        "code": "AFN",
        "decimals": 2,
        "name": "Afghan afghani",
        "number": "971"
      },
      {
        "code": "ALL",
        "decimals": 2,
        "name": "Albanian lek",
        "number": "8"
      },
      {
        "code": "AMD",
        "decimals": 2,
        "name": "Armenian dram",
        "number": "51"
      },
      {
        "code": "ANG",
        "decimals": 2,
        "name": "Netherlands Antillean guilder",
        "number": "532"
      },
      {
        "code": "AOA",
        "decimals": 2,
        "name": "Angolan kwanza",
        "number": "973"
      },
      {
        "code": "ARS",
        "decimals": 2,
        "name": "Argentine peso",
        "number": "32"
      },
      {
        "code": "AUD",
        "decimals": 2,
        "name": "Australian dollar",
        "number": "36"
      },
      {
        "code": "AWG",
        "decimals": 2,
        "name": "Aruban florin",
        "number": "533"
      },
      {
        "code": "AZN",
        "decimals": 2,
        "name": "Azerbaijani manat",
        "number": "944"
      },
      {
        "code": "BAM",
        "decimals": 2,
        "name": "Bosnia and Herzegovina convertible mark",
        "number": "977"
      },
      {
        "code": "BBD",
        "decimals": 2,
        "name": "Barbados dollar",
        "number": "52"
      },
      {
        "code": "BDT",
        "decimals": 2,
        "name": "Bangladeshi taka",
        "number": "50"
      },
      {
        "code": "BGN",
        "decimals": 2,
        "name": "Bulgarian lev",
        "number": "975"
      },
      {
        "code": "BHD",
        "decimals": 3,
        "name": "Bahraini dinar",
        "number": "48"
      },
      {
        "code": "BIF",
        "decimals": 0,
        "name": "Burundian franc",
        "number": "108"
      },
      {
        "code": "BMD",
        "decimals": 2,
        "name": "Bermudian dollar",
        "number": "60"
      },
      {
        "code": "BND",
        "decimals": 2,
        "name": "Brunei dollar",
        "number": "96"
      },
      {
        "code": "BOB",
        "decimals": 2,
        "name": "Boliviano",
        "number": "68"
      },
      {
        "code": "BRL",
        "decimals": 2,
        "name": "Brazilian real",
        "number": "986"
      },
      {
        "code": "BSD",
        "decimals": 2,
        "name": "Bahamian dollar",
        "number": "44"
      },
      {
        "code": "BTN",
        "decimals": 2,
        "name": "Bhutanese ngultrum",
        "number": "64"
      },
      {
        "code": "BWP",
        "decimals": 2,
        "name": "Botswana pula",
        "number": "72"
      },
      {
        "code": "BYR",
        "decimals": 0,
        "name": "Belarusian ruble",
        "number": "974"
      },
      {
        "code": "BZD",
        "decimals": 2,
        "name": "Belize dollar",
        "number": "84"
      },
      {
        "code": "CAD",
        "decimals": 2,
        "name": "Canadian dollar",
        "number": "124"
      },
      {
        "code": "CDF",
        "decimals": 2,
        "name": "Congolese franc",
        "number": "976"
      },
      {
        "code": "CHE",
        "decimals": 2,
        "name": "WIR Euro (complementary currency)",
        "number": "947"
      },
      {
        "code": "CHF",
        "decimals": 2,
        "name": "Swiss franc",
        "number": "756"
      },
      {
        "code": "CHW",
        "decimals": 2,
        "name": "WIR Franc (complementary currency)",
        "number": "948"
      },
      {
        "code": "CLP",
        "decimals": 0,
        "name": "Chilean peso",
        "number": "152"
      },
      {
        "code": "CNY",
        "decimals": 2,
        "name": "Chinese yuan",
        "number": "156"
      },
      {
        "code": "COP",
        "decimals": 2,
        "name": "Colombian peso",
        "number": "170"
      },
      {
        "code": "COU",
        "decimals": 2,
        "name": "Unidad de Valor Real",
        "number": "970"
      },
      {
        "code": "CRC",
        "decimals": 2,
        "name": "Costa Rican colon",
        "number": "188"
      },
      {
        "code": "CUC",
        "decimals": 2,
        "name": "Cuban convertible peso",
        "number": "931"
      },
      {
        "code": "CUP",
        "decimals": 2,
        "name": "Cuban peso",
        "number": "192"
      },
      {
        "code": "CVE",
        "decimals": 0,
        "name": "Cape Verde escudo",
        "number": "132"
      },
      {
        "code": "CZK",
        "decimals": 2,
        "name": "Czech koruna",
        "number": "203"
      },
      {
        "code": "DJF",
        "decimals": 0,
        "name": "Djiboutian franc",
        "number": "262"
      },
      {
        "code": "DKK",
        "decimals": 2,
        "name": "Danish krone",
        "number": "208"
      },
      {
        "code": "DOP",
        "decimals": 2,
        "name": "Dominican peso",
        "number": "214"
      },
      {
        "code": "DZD",
        "decimals": 2,
        "name": "Algerian dinar",
        "number": "12"
      },
      {
        "code": "EGP",
        "decimals": 2,
        "name": "Egyptian pound",
        "number": "818"
      },
      {
        "code": "ERN",
        "decimals": 2,
        "name": "Eritrean nakfa",
        "number": "232"
      },
      {
        "code": "ETB",
        "decimals": 2,
        "name": "Ethiopian birr",
        "number": "230"
      },
      {
        "code": "EUR",
        "decimals": 2,
        "name": "Euro",
        "number": "978"
      },
      {
        "code": "FJD",
        "decimals": 2,
        "name": "Fiji dollar",
        "number": "242"
      },
      {
        "code": "FKP",
        "decimals": 2,
        "name": "Falkland Islands pound",
        "number": "238"
      },
      {
        "code": "GBP",
        "decimals": 2,
        "name": "Pound sterling",
        "number": "826"
      },
      {
        "code": "GEL",
        "decimals": 2,
        "name": "Georgian lari",
        "number": "981"
      },
      {
        "code": "GHS",
        "decimals": 2,
        "name": "Ghanaian cedi",
        "number": "936"
      },
      {
        "code": "GIP",
        "decimals": 2,
        "name": "Gibraltar pound",
        "number": "292"
      },
      {
        "code": "GMD",
        "decimals": 2,
        "name": "Gambian dalasi",
        "number": "270"
      },
      {
        "code": "GNF",
        "decimals": 0,
        "name": "Guinean franc",
        "number": "324"
      },
      {
        "code": "GTQ",
        "decimals": 2,
        "name": "Guatemalan quetzal",
        "number": "320"
      },
      {
        "code": "GYD",
        "decimals": 2,
        "name": "Guyanese dollar",
        "number": "328"
      },
      {
        "code": "HKD",
        "decimals": 2,
        "name": "Hong Kong dollar",
        "number": "344"
      },
      {
        "code": "HNL",
        "decimals": 2,
        "name": "Honduran lempira",
        "number": "340"
      },
      {
        "code": "HRK",
        "decimals": 2,
        "name": "Croatian kuna",
        "number": "191"
      },
      {
        "code": "HTG",
        "decimals": 2,
        "name": "Haitian gourde",
        "number": "332"
      },
      {
        "code": "HUF",
        "decimals": 2,
        "name": "Hungarian forint",
        "number": "348"
      },
      {
        "code": "IDR",
        "decimals": 2,
        "name": "Indonesian rupiah",
        "number": "360"
      },
      {
        "code": "ILS",
        "decimals": 2,
        "name": "Israeli new shekel",
        "number": "376"
      },
      {
        "code": "INR",
        "decimals": 2,
        "name": "Indian rupee",
        "number": "356"
      },
      {
        "code": "IQD",
        "decimals": 3,
        "name": "Iraqi dinar",
        "number": "368"
      },
      {
        "code": "IRR",
        "decimals": 0,
        "name": "Iranian rial",
        "number": "364"
      },
      {
        "code": "ISK",
        "decimals": 0,
        "name": "Icelandic króna",
        "number": "352"
      },
      {
        "code": "JMD",
        "decimals": 2,
        "name": "Jamaican dollar",
        "number": "388"
      },
      {
        "code": "JOD",
        "decimals": 3,
        "name": "Jordanian dinar",
        "number": "400"
      },
      {
        "code": "JPY",
        "decimals": 0,
        "name": "Japanese yen",
        "number": "392"
      },
      {
        "code": "KES",
        "decimals": 2,
        "name": "Kenyan shilling",
        "number": "404"
      },
      {
        "code": "KGS",
        "decimals": 2,
        "name": "Kyrgyzstani som",
        "number": "417"
      },
      {
        "code": "KHR",
        "decimals": 2,
        "name": "Cambodian riel",
        "number": "116"
      },
      {
        "code": "KMF",
        "decimals": 0,
        "name": "Comoro franc",
        "number": "174"
      },
      {
        "code": "KPW",
        "decimals": 0,
        "name": "North Korean won",
        "number": "408"
      },
      {
        "code": "KRW",
        "decimals": 0,
        "name": "South Korean won",
        "number": "410"
      },
      {
        "code": "KWD",
        "decimals": 3,
        "name": "Kuwaiti dinar",
        "number": "414"
      },
      {
        "code": "KYD",
        "decimals": 2,
        "name": "Cayman Islands dollar",
        "number": "136"
      },
      {
        "code": "KZT",
        "decimals": 2,
        "name": "Kazakhstani tenge",
        "number": "398"
      },
      {
        "code": "LAK",
        "decimals": 0,
        "name": "Lao kip",
        "number": "418"
      },
      {
        "code": "LBP",
        "decimals": 0,
        "name": "Lebanese pound",
        "number": "422"
      },
      {
        "code": "LKR",
        "decimals": 2,
        "name": "Sri Lankan rupee",
        "number": "144"
      },
      {
        "code": "LRD",
        "decimals": 2,
        "name": "Liberian dollar",
        "number": "430"
      },
      {
        "code": "LSL",
        "decimals": 2,
        "name": "Lesotho loti",
        "number": "426"
      },
      {
        "code": "LTL",
        "decimals": 2,
        "name": "Lithuanian litas",
        "number": "440"
      },
      {
        "code": "LVL",
        "decimals": 2,
        "name": "Latvian lats",
        "number": "428"
      },
      {
        "code": "LYD",
        "decimals": 3,
        "name": "Libyan dinar",
        "number": "434"
      },
      {
        "code": "MAD",
        "decimals": 2,
        "name": "Moroccan dirham",
        "number": "504"
      },
      {
        "code": "MDL",
        "decimals": 2,
        "name": "Moldovan leu",
        "number": "498"
      },
      {
        "code": "MGA",
        "decimals": 0,
        "name": "Malagasy ariary",
        "number": "969"
      },
      {
        "code": "MKD",
        "decimals": 0,
        "name": "Macedonian denar",
        "number": "807"
      },
      {
        "code": "MMK",
        "decimals": 0,
        "name": "Myanma kyat",
        "number": "104"
      },
      {
        "code": "MNT",
        "decimals": 2,
        "name": "Mongolian tugrik",
        "number": "496"
      },
      {
        "code": "MOP",
        "decimals": 2,
        "name": "Macanese pataca",
        "number": "446"
      },
      {
        "code": "MRO",
        "decimals": 0,
        "name": "Mauritanian ouguiya",
        "number": "478"
      },
      {
        "code": "MUR",
        "decimals": 2,
        "name": "Mauritian rupee",
        "number": "480"
      },
      {
        "code": "MVR",
        "decimals": 2,
        "name": "Maldivian rufiyaa",
        "number": "462"
      },
      {
        "code": "MWK",
        "decimals": 2,
        "name": "Malawian kwacha",
        "number": "454"
      },
      {
        "code": "MXN",
        "decimals": 2,
        "name": "Mexican peso",
        "number": "484"
      },
      {
        "code": "MYR",
        "decimals": 2,
        "name": "Malaysian ringgit",
        "number": "458"
      },
      {
        "code": "MZN",
        "decimals": 2,
        "name": "Mozambican metical",
        "number": "943"
      },
      {
        "code": "NAD",
        "decimals": 2,
        "name": "Namibian dollar",
        "number": "516"
      },
      {
        "code": "NGN",
        "decimals": 2,
        "name": "Nigerian naira",
        "number": "566"
      },
      {
        "code": "NIO",
        "decimals": 2,
        "name": "Nicaraguan córdoba",
        "number": "558"
      },
      {
        "code": "NOK",
        "decimals": 2,
        "name": "Norwegian krone",
        "number": "578"
      },
      {
        "code": "NPR",
        "decimals": 2,
        "name": "Nepalese rupee",
        "number": "524"
      },
      {
        "code": "NZD",
        "decimals": 2,
        "name": "New Zealand dollar",
        "number": "554"
      },
      {
        "code": "OMR",
        "decimals": 3,
        "name": "Omani rial",
        "number": "512"
      },
      {
        "code": "PAB",
        "decimals": 2,
        "name": "Panamanian balboa",
        "number": "590"
      },
      {
        "code": "PEN",
        "decimals": 2,
        "name": "Peruvian nuevo sol",
        "number": "604"
      },
      {
        "code": "PGK",
        "decimals": 2,
        "name": "Papua New Guinean kina",
        "number": "598"
      },
      {
        "code": "PHP",
        "decimals": 2,
        "name": "Philippine peso",
        "number": "608"
      },
      {
        "code": "PKR",
        "decimals": 2,
        "name": "Pakistani rupee",
        "number": "586"
      },
      {
        "code": "PLN",
        "decimals": 2,
        "name": "Polish złoty",
        "number": "985"
      },
      {
        "code": "PYG",
        "decimals": 0,
        "name": "Paraguayan guaraní",
        "number": "600"
      },
      {
        "code": "QAR",
        "decimals": 2,
        "name": "Qatari riyal",
        "number": "634"
      },
      {
        "code": "RON",
        "decimals": 2,
        "name": "Romanian new leu",
        "number": "946"
      },
      {
        "code": "RSD",
        "decimals": 2,
        "name": "Serbian dinar",
        "number": "941"
      },
      {
        "code": "RUB",
        "decimals": 2,
        "name": "Russian rouble",
        "number": "643"
      },
      {
        "code": "RWF",
        "decimals": 0,
        "name": "Rwandan franc",
        "number": "646"
      },
      {
        "code": "SAR",
        "decimals": 2,
        "name": "Saudi riyal",
        "number": "682"
      },
      {
        "code": "SBD",
        "decimals": 2,
        "name": "Solomon Islands dollar",
        "number": "90"
      },
      {
        "code": "SCR",
        "decimals": 2,
        "name": "Seychelles rupee",
        "number": "690"
      },
      {
        "code": "SDG",
        "decimals": 2,
        "name": "Sudanese pound",
        "number": "938"
      },
      {
        "code": "SEK",
        "decimals": 2,
        "name": "Swedish krona/kronor",
        "number": "752"
      },
      {
        "code": "SGD",
        "decimals": 2,
        "name": "Singapore dollar",
        "number": "702"
      },
      {
        "code": "SHP",
        "decimals": 2,
        "name": "Saint Helena pound",
        "number": "654"
      },
      {
        "code": "SLL",
        "decimals": 0,
        "name": "Sierra Leonean leone",
        "number": "694"
      },
      {
        "code": "SOS",
        "decimals": 2,
        "name": "Somali shilling",
        "number": "706"
      },
      {
        "code": "SRD",
        "decimals": 2,
        "name": "Surinamese dollar",
        "number": "968"
      },
      {
        "code": "SSP",
        "decimals": 2,
        "name": "South Sudanese pound",
        "number": "728"
      },
      {
        "code": "STD",
        "decimals": 0,
        "name": "São Tomé and Príncipe dobra",
        "number": "678"
      },
      {
        "code": "SYP",
        "decimals": 2,
        "name": "Syrian pound",
        "number": "760"
      },
      {
        "code": "SZL",
        "decimals": 2,
        "name": "Swazi lilangeni",
        "number": "748"
      },
      {
        "code": "THB",
        "decimals": 2,
        "name": "Thai baht",
        "number": "764"
      },
      {
        "code": "TJS",
        "decimals": 2,
        "name": "Tajikistani somoni",
        "number": "972"
      },
      {
        "code": "TMT",
        "decimals": 2,
        "name": "Turkmenistani manat",
        "number": "934"
      },
      {
        "code": "TND",
        "decimals": 3,
        "name": "Tunisian dinar",
        "number": "788"
      },
      {
        "code": "TOP",
        "decimals": 2,
        "name": "Tongan paʻanga",
        "number": "776"
      },
      {
        "code": "TRY",
        "decimals": 2,
        "name": "Turkish lira",
        "number": "949"
      },
      {
        "code": "TTD",
        "decimals": 2,
        "name": "Trinidad and Tobago dollar",
        "number": "780"
      },
      {
        "code": "TWD",
        "decimals": 2,
        "name": "New Taiwan dollar",
        "number": "901"
      },
      {
        "code": "TZS",
        "decimals": 2,
        "name": "Tanzanian shilling",
        "number": "834"
      },
      {
        "code": "UAH",
        "decimals": 2,
        "name": "Ukrainian hryvnia",
        "number": "980"
      },
      {
        "code": "UGX",
        "decimals": 2,
        "name": "Ugandan shilling",
        "number": "800"
      },
      {
        "code": "USD",
        "decimals": 2,
        "name": "United States dollar",
        "number": "840"
      },
      {
        "code": "UYU",
        "decimals": 2,
        "name": "Uruguayan peso",
        "number": "858"
      },
      {
        "code": "UZS",
        "decimals": 2,
        "name": "Uzbekistan som",
        "number": "860"
      },
      {
        "code": "VEF",
        "decimals": 2,
        "name": "Venezuelan bolívar fuerte",
        "number": "937"
      },
      {
        "code": "VND",
        "decimals": 0,
        "name": "Vietnamese dong",
        "number": "704"
      },
      {
        "code": "VUV",
        "decimals": 0,
        "name": "Vanuatu vatu",
        "number": "548"
      },
      {
        "code": "WST",
        "decimals": 2,
        "name": "Samoan tala",
        "number": "882"
      },
      {
        "code": "XAF",
        "decimals": 0,
        "name": "CFA franc BEAC",
        "number": "950"
      },
      {
        "code": "XAG",
        "decimals": null,
        "name": "Silver (one troy ounce)",
        "number": "961"
      },
      {
        "code": "XAU",
        "decimals": null,
        "name": "Gold (one troy ounce)",
        "number": "959"
      },
      {
        "code": "XBA",
        "decimals": null,
        "name": "European Composite Unit (EURCO) (bond market unit)",
        "number": "955"
      },
      {
        "code": "XBB",
        "decimals": null,
        "name": "European Monetary Unit (E.M.U.-6) (bond market unit)",
        "number": "956"
      },
      {
        "code": "XBC",
        "decimals": null,
        "name": "European Unit of Account 9 (E.U.A.-9) (bond market unit)",
        "number": "957"
      },
      {
        "code": "XBD",
        "decimals": null,
        "name": "European Unit of Account 17 (E.U.A.-17) (bond market unit)",
        "number": "958"
      },
      {
        "code": "XCD",
        "decimals": 2,
        "name": "East Caribbean dollar",
        "number": "951"
      },
      {
        "code": "XDR",
        "decimals": null,
        "name": "Special drawing rights",
        "number": "960"
      },
      {
        "code": "XFU",
        "decimals": null,
        "name": "UIC franc (special settlement currency)",
        "number": "Nil"
      },
      {
        "code": "XOF",
        "decimals": 0,
        "name": "CFA franc BCEAO",
        "number": "952"
      },
      {
        "code": "XPD",
        "decimals": null,
        "name": "Palladium (one troy ounce)",
        "number": "964"
      },
      {
        "code": "XPF",
        "decimals": 0,
        "name": "CFP franc",
        "number": "953"
      },
      {
        "code": "XPT",
        "decimals": null,
        "name": "Platinum (one troy ounce)",
        "number": "962"
      },
      {
        "code": "XTS",
        "decimals": null,
        "name": "Code reserved for testing purposes",
        "number": "963"
      },
      {
        "code": "XXX",
        "decimals": null,
        "name": "No currency",
        "number": "999"
      },
      {
        "code": "YER",
        "decimals": 2,
        "name": "Yemeni rial",
        "number": "886"
      },
      {
        "code": "ZAR",
        "decimals": 2,
        "name": "South African rand",
        "number": "710"
      },
      {
        "code": "ZMW",
        "decimals": 2,
        "name": "Zambian kwacha",
        "number": "967"
      }
    ];

  constructor( editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AppleInteractiveMessageApplePayPiece;

    this.applePayMerchantsConfig = this.editorService.getApplePayMerchantsConfig();
  }

  refreshMerchantCapabilities(checked: boolean, capability: ApplePayMerchantCapabilities) {
    if (checked) {
      if (this.model.applepay.merchantCapabilities.indexOf(capability) === -1) {
        this.model.applepay.merchantCapabilities.push(capability);
      }
    }
    else {
      let index = this.model.applepay.merchantCapabilities.indexOf(capability);
      if (index >= 0) {
        this.model.applepay.merchantCapabilities.splice(index, 1);
      }
    }
  }

  isMerchantCapabilitySelected(capability: ApplePayMerchantCapabilities) {
    if (this.model.applepay.merchantCapabilities === null) {
      return false;
    }

    return this.model.applepay.merchantCapabilities.indexOf(capability) >= 0;
  }

  refreshSupportedNetwork(checked: boolean, network: ApplePaySupportedNetworks) {
    if (checked) {
      if (this.model.applepay.supportedNetworks.indexOf(network) === -1) {
        this.model.applepay.supportedNetworks.push(network);
      }
    }
    else {
      let index = this.model.applepay.supportedNetworks.indexOf(network);
      if (index >= 0) {
        this.model.applepay.supportedNetworks.splice(index, 1);
      }
    }
  }

  isSupportedNetworkSelected(network: ApplePaySupportedNetworks) {
    if (this.model.applepay.supportedNetworks === null) {
      return false;
    }

    return this.model.applepay.supportedNetworks.indexOf(network) >= 0;
  }

  refreshContactFields(checked: boolean, array: ApplePayContactField[], field: ApplePayContactField) {
    if (checked) {
      if (array.indexOf(field) === -1) {
        array.push(field);
      }
    }
    else {
      let index = array.indexOf(field);
      if (index >= 0) {
        array.splice(index, 1);
      }
    }
  }

  isContactFieldSelected(array: ApplePayContactField[], field: ApplePayContactField) {
    if (array === null) {
      return false;
    }

    return array.indexOf(field) >= 0;
  }

  refreshSupportedCountries(checked: boolean, country: string) {
    if (checked) {
      if (this.model.supportedCountries.indexOf(country) === -1) {
        this.model.supportedCountries.push(country);
      }
    }
    else {
      let index = this.model.supportedCountries.indexOf(country);
      if (index >= 0) {
        this.model.supportedCountries.splice(index, 1);
      }
    }
  }

  isSupportedCountrySelected(country: string) {
    if (this.model.supportedCountries === null) {
      return false;
    }

    return this.model.supportedCountries.indexOf(country) >= 0;
  }

  deleteShippingMethod(index: number) {
    this.model.shippingMethods.splice(index, 1);
  }

  addNewShippingMethod() {
    if (this.model.shippingMethods === null) {
      this.model.shippingMethods = [];
    }
    this.model.shippingMethods.push(new ApplePayShippingMethod());
  }

  deleteLineItem(index: number) {
    this.model.lineItems.splice(index, 1);
  }

  addNewLineItem() {
    if (this.model.lineItems === null) {
      this.model.lineItems = [];
    }
    this.model.lineItems.push(new ApplePayLineItem());
  }

  selectBlock(blockData) {
    this.model.errorBlockId = blockData.Id;
  }

  deleteBlock() {
    this.model.errorBlockId = null;
  }

  selectPaymentSuccessBlock(blockData) {
    this.model.paymentSuccessBlockId = blockData.Id;
  }

  selectPaymentErrorBlock(blockData) {
    this.model.paymentErrorBlockId = blockData.Id;
  }

  deletePaymentSuccessBlock() {
    this.model.paymentSuccessBlockId = null;
  }

  deletePaymentErrorBlock() {
    this.model.paymentErrorBlockId = null;
  }
}
