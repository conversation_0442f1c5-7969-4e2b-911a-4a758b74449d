@import "_variables";
@import "_mixins";

.configurable-item {
    @include configurable-item;
    /*min-height: 420px;*/

    .alert {
      margin-top: 6px;
    }
    .variables {
      margin-top: 16px;
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;
        }
      }

      .variable {
        display: table-row;
        padding-top: 3px;
        padding-bottom: 3px;
        height: 40px;
        position: relative;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;
        }

        &:last-child {
          & > div {
            border-bottom: 1px none $sidebarBorderColor;
          }
        }

        .name, .defaultvalue {
          input {
            width: 100%;
            font-family: $fontFamilyMono;
          }
          textarea {
            width: 100%;
            font-family: $fontFamilyMono;
          }
        }

        .type {
          select {
            width: 100%;
          }
        }

        .trash {
          width: 30px;

          & > div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }
    .addButton {
      @include addPieceButton;
      border: $linkActionColor solid 1px;
      border-radius: 6px;
    }


    .tables {
      margin-top: 8px;
      .tables-table {
        display: table;
        width: 100%;

        .header {
          display: table-header-group;
          font-family: $fontFamilyTitles;
          font-weight: bold;

          &>div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 5px;
            padding-right: 5px;

            &.center {
              text-align: center;
              width: 130px;
            }
          }
        }

        .row {
          display: table-row;
          height: 40px;

          .center {
            .circle-button {
              cursor: pointer;
            }
          }
          .center > input {
            display: none;
          }

          &:hover {
            background: lighten($version-highlight, 10%);
          }

          &>div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 5px;
            padding-right: 5px;

            &.center {
              text-align: center;
              width: 130px;
            }
          }

          &:last-child {
            &>div {
              border-bottom: 1px none $sidebarBorderColor;
            }
          }
        }
      }
    }

}

.overlay {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #00000044;
  z-index: 100000;

  .spinner {
    display: inline-block;
    position: absolute;
    left: calc(50% - 32px);
    top: calc(50% - 32px);
  }
}

