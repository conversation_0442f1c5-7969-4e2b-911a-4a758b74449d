client=YoizenTest
NODE_ENV=prod
storageYFlowPath=E:\Old version\StorageYflow\
defaultLanguaje=es
gmt=-03:00
redisConnectionString=yoizenprd.redis.cache.windows.net:6380,password=duJF6cZeVrodbBm0VRBunCqEGNeAvy2KUAzCaM9rh0U=,ssl=True,abortConnect=False
urlCentralize=https://callback.ysocial.net/api/centralizer
ySocialUrl=https://qa.ysocial.net/Oldversion/
ySocialAccessToken=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=
ySocialAccessTokenSecret=nHJS3qbsN2sDzFovZmPLsA==
yflowUrl=https://qa.ysocial.net/Oldversion/yflow
useAzureBlobStorage=false
dniServiceUrl=https://yflow-dni-validator-qa.ysocial.net
dniServiceApiKey=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=
dbdialect=mssql
yFlowConnectionString=
dbcontextdialect=mysql
dbcontextConnectionString=
dbintervalsdialect=mysql
dbintervalsConnectionString=
isContingencyBot=false