import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AddQuickReplyComponent } from './add-quick-reply.component';

describe('AddQuickReplyComponent', () => {
  let component: AddQuickReplyComponent;
  let fixture: ComponentFixture<AddQuickReplyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AddQuickReplyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddQuickReplyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
