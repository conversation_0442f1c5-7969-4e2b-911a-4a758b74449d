import {Component, OnInit} from '@angular/core';
import {Concatenate} from '../../../../../models/pieces/Concatenate';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {BasePieceVM} from '../BasePieceVM';
import {TypeDefinition} from '../../../../../models/TypeDefinition';
import {VariableDefinition} from '../../../../../models/VariableDefinition';
import {MultipleMessagePiece} from "../../../../../models/pieces/MultipleMessagePiece";

@Component({
  selector: 'app-multiple-messages-piece',
  templateUrl: './multiple-messages-piece.component.html',
  styleUrls: ['./multiple-messages-piece.component.scss']
})
export class MultipleMessagesPieceComponent extends BasePieceVM implements OnInit {
  model: MultipleMessagePiece;
  variableFilter = [TypeDefinition.Array];
  outputVariableFilter = [TypeDefinition.Text];

  get customVariables(): VariableDefinition[] {
    return MultipleMessagePiece.SpecialVar;
  }

  get InputVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.SourceVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as MultipleMessagePiece;
  }

  setInputVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.model.SourceVariableId = variable.Id;
    }
    else {
      this.model.SourceVariableId = null;
    }
  }

  validateVariable(value: string) {
    return false;
  }

  validateTextArea(value: string) {
    return this.model.hasTextToAsign();
  }

  getValidator() {
    return this.validateTextArea.bind(this);
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }
}
