/****** Object:  Table [dbo].[abandoned_case]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE TABLE [dbo].[abandoned_case](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[flow_id] [int] NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[block_id] [varchar](100) NULL,
	[data] [nvarchar](max) NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[companies]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[companies](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
	[name] [nvarchar](255) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[configurations]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[configurations](
	[name] [nvarchar](100) NOT NULL,
	[content] [nvarchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[detail_statistic_event]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[detail_statistic_event](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[statistic_event_id] [int] NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[data] [nvarchar](max) NULL,
	[version] [int] NULL,
	[block_id] [varchar](100) NULL,
	[block_group_id] [varchar](100) NULL
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[flow_table]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[flow_table](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[flow_id] [int] NOT NULL,
	[version] [int] NOT NULL,
	[table_name] [nvarchar](80) NOT NULL,
	[status] [nvarchar](10) NOT NULL,
	[extension] [nvarchar](5) NOT NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
	[headers] [nvarchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[flow_versions]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[flow_versions](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[created_at] [datetime2](7) NULL,
	[number] [int] NULL,
	[blob] [nvarchar](max) NULL,
	[flow_id] [int] NOT NULL,
	[stats] [nvarchar](max) NULL,
	[published_at] [datetime2](7) NULL,
	[published_by_user_id] [int] NULL,
	[comments] [nvarchar](max) NULL,
	[user_id] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[flows]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[flows](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
	[user_id] [int] NOT NULL,
	[active_staging_version_id] [int] NULL,
	[active_production_version_id] [int] NULL,
	[deleted_at] [datetime2](7) NULL,
	[channel] [nvarchar](255) NULL,
	[company_id] [int] NULL,
	[type] [nvarchar](10) NULL,
	[master_flow_id] [int] NULL,
	[active_production_version_master_id] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[data] [nvarchar](max) NULL,
	[new_cases] [int] NULL,
	[transferred] [int] NULL,
	[closed_by_yflow] [int] NULL,
	[new_messages] [int] NULL,
	[hsm_case] [int] NULL,
	[case_abandoned] [int] NULL,
	[monthly_users] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_blocks]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_blocks](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[block_id] [varchar](100) NULL,
	[channel] [nvarchar](255) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_blocks_sequence]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_blocks_sequence](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[source_block_id] [varchar](100) NULL,
	[dest_block_id] [varchar](100) NULL,
	[type] [int] NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_commands]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_commands](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[command_id] [int] NOT NULL,
	[channel] [nvarchar](255) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_default_answers]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_default_answers](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[block_id] [varchar](100) NULL,
	[channel] [nvarchar](255) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_derivation_key]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_derivation_key](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[derivation_key] [nvarchar](255) NOT NULL,
	[channel] [nvarchar](255) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_flow]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_flow](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[channel] [nvarchar](255) NOT NULL,
	[data] [nvarchar](max) NULL,
	[new_cases] [int] NULL,
	[transferred] [int] NULL,
	[closed_by_yflow] [int] NULL,
	[new_messages] [int] NULL,
	[hsm_case] [int] NULL,
	[case_abandoned] [int] NULL,
	[monthly_users] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_group]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_group](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[group_id] [nvarchar](50) NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_groups_sequence]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_groups_sequence](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[source_group_id] [nvarchar](50) NULL,
	[dest_group_id] [nvarchar](50) NULL,
	[type] [int] NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_integrations]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_integrations](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[integration_id] [int] NOT NULL,
	[channel] [nvarchar](255) NOT NULL,
	[total] [int] NULL,
	[error] [int] NULL,
	[version] [int] NULL,
	[total_response_time] [decimal](18, 2) NOT NULL,
	[error_response_time] [decimal](18, 2) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[history_daily_by_statistic_event]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[history_daily_by_statistic_event](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[date] [datetime2](7) NOT NULL,
	[interval] [int] NOT NULL,
	[interval_datetime] [datetime2](7) NOT NULL,
	[flow_id] [int] NOT NULL,
	[statistic_event_id] [int] NOT NULL,
	[channel] [nvarchar](10) NOT NULL,
	[total] [int] NULL,
	[version] [int] NULL,
	[block_id] [varchar](100) NULL,
	[block_group_id] [varchar](100) NULL
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[migrations]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[migrations](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[run_on] [datetime2](7) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[reports]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[reports](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[report_type] [nvarchar](50) NOT NULL,
	[date_start] [nvarchar](30) NOT NULL,
	[date_end] [nvarchar](30) NOT NULL,
	[user_id] [int] NOT NULL,
	[flow_id] [int] NOT NULL,
	[status] [nvarchar](50) NULL,
	[message] [nvarchar](50) NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
	[events] [nvarchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[system_status]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[system_status](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[type] [nvarchar](255) NULL,
	[date] [datetime2](7) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[user_action_logs]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_action_logs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[created_at] [datetime2](7) NULL,
	[action] [nvarchar](max) NULL,
	[user_id] [int] NOT NULL,
	[flow_version_id] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[password] [nvarchar](255) NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
	[enabled] [int] NULL,
	[company_id] [int] NULL,
	[access_token] [nvarchar](4000) NULL,
	[is_admin] [int] NULL,
	[can_edit] [int] NULL,
	[can_publish] [int] NULL,
	[can_see_statistics] [int] NULL,
	[can_validate_passwords] [int] NULL,
	[lang] [nvarchar](5) NULL,
	[can_access_ysmart] [int] NULL,
	[login_type] [nvarchar](50) DEFAULT 'LOCAL' NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users_permissions_flows]    Script Date: 6/4/2024 1:53:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users_permissions_flows](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[flow_id] [int] NOT NULL,
	[can_edit] [int] NULL,
	[can_publish] [int] NULL,
	[can_see_statistics] [int] NULL,
	[can_access_ysmart] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Index [IX_ABANDONED_CASE_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_ABANDONED_CASE_DATE] ON [dbo].[abandoned_case]
(
	[date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_ABANDONED_CASE_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_ABANDONED_CASE_FLOW_ID] ON [dbo].[abandoned_case]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_COMPANIES_NAME]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE UNIQUE NONCLUSTERED INDEX [IX_COMPANIES_NAME] ON [dbo].[companies]
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_DETAIL_STATISTIC_EVENT_BY_DATE_FLOW_ID_STATISTIC_EVENT_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_DETAIL_STATISTIC_EVENT_BY_DATE_FLOW_ID_STATISTIC_EVENT_ID] ON [dbo].[detail_statistic_event]
(
	[flow_id] ASC,
	[statistic_event_id] ASC,
	[date] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_DETAIL_STATISTIC_EVENT_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_DETAIL_STATISTIC_EVENT_DATE] ON [dbo].[detail_statistic_event]
(
	[date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_DETAIL_STATISTIC_EVENT_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_DETAIL_STATISTIC_EVENT_FLOW_ID] ON [dbo].[detail_statistic_event]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_FLOW_TABLE_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_FLOW_TABLE_FLOW_ID] ON [dbo].[flow_table]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_FLOW_VERSION_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_FLOW_VERSION_FLOW_ID] ON [dbo].[flow_versions]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_FLOWS_CHANNEL]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_FLOWS_CHANNEL] ON [dbo].[flows]
(
	[channel] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_FLOWS_COMPANY_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_FLOWS_COMPANY_ID] ON [dbo].[flows]
(
	[company_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_FLOWS_ENABLED]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_FLOWS_ENABLED] ON [dbo].[flows]
(
	[id] ASC,
	[company_id] ASC,
	[deleted_at] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_FLOWS_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE UNIQUE NONCLUSTERED INDEX [IX_FLOWS_ID] ON [dbo].[flows]
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_DATE] ON [dbo].[history_daily]
(
	[date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_BLOCK_ID] ON [dbo].[history_daily_by_blocks]
(
	[block_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_BY_DATE_FLOW_ID_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_BY_DATE_FLOW_ID_BLOCK_ID] ON [dbo].[history_daily_by_blocks]
(
	[flow_id] ASC,
	[date] ASC,
	[block_id] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_DATE] ON [dbo].[history_daily_by_blocks]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_FLOW_ID] ON [dbo].[history_daily_by_blocks]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_BY_DATE_FLOW_ID_DEST_BLOCK_ID_SOURCE_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_BY_DATE_FLOW_ID_DEST_BLOCK_ID_SOURCE_BLOCK_ID] ON [dbo].[history_daily_by_blocks_sequence]
(
	[flow_id] ASC,
	[source_block_id] ASC,
	[dest_block_id] ASC,
	[date] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DATE] ON [dbo].[history_daily_by_blocks_sequence]
(
	[date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DEST_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DEST_BLOCK_ID] ON [dbo].[history_daily_by_blocks_sequence]
(
	[dest_block_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_FLOW_ID] ON [dbo].[history_daily_by_blocks_sequence]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_SOURCE_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_SOURCE_BLOCK_ID] ON [dbo].[history_daily_by_blocks_sequence]
(
	[source_block_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_COMMANDS_BY_DATE_FLOW_ID_COMMAND_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_COMMANDS_BY_DATE_FLOW_ID_COMMAND_ID] ON [dbo].[history_daily_by_commands]
(
	[flow_id] ASC,
	[date] ASC,
	[command_id] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_COMMANDS_COMMAND_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_COMMANDS_COMMAND_ID] ON [dbo].[history_daily_by_commands]
(
	[command_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_COMMANDS_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_COMMANDS_DATE] ON [dbo].[history_daily_by_commands]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_COMMANDS_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_COMMANDS_FLOW_ID] ON [dbo].[history_daily_by_commands]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_BY_DATE_FLOW_ID_BLOCK_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_BY_DATE_FLOW_ID_BLOCK_ID] ON [dbo].[history_daily_by_default_answers]
(
	[flow_id] ASC,
	[date] ASC,
	[block_id] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_DATE] ON [dbo].[history_daily_by_default_answers]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_FLOW_ID] ON [dbo].[history_daily_by_default_answers]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DERIVATION_KEY_BY_DATE_FLOW_ID_DERIVATION_KEY]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DERIVATION_KEY_BY_DATE_FLOW_ID_DERIVATION_KEY] ON [dbo].[history_daily_by_derivation_key]
(
	[flow_id] ASC,
	[date] ASC,
	[derivation_key] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DERIVATION_KEY_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DERIVATION_KEY_DATE] ON [dbo].[history_daily_by_derivation_key]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_DERIVATION_KEY_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_DERIVATION_KEY_FLOW_ID] ON [dbo].[history_daily_by_derivation_key]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_FLOW_BY_DATE_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_FLOW_BY_DATE_FLOW_ID] ON [dbo].[history_daily_by_flow]
(
	[flow_id] ASC,
	[date] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_FLOW_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_FLOW_DATE] ON [dbo].[history_daily_by_flow]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_FLOW_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_FLOW_FLOW_ID] ON [dbo].[history_daily_by_flow]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUP_BY_DATE_FLOW_ID_GROUP_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUP_BY_DATE_FLOW_ID_GROUP_ID] ON [dbo].[history_daily_by_group]
(
	[flow_id] ASC,
	[date] ASC,
	[group_id] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUP_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUP_DATE] ON [dbo].[history_daily_by_group]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUP_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUP_FLOW_ID] ON [dbo].[history_daily_by_group]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_BY_DATE_FLOW_ID_DEST_GROUP_ID_SOURCE_GROUP_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_BY_DATE_FLOW_ID_DEST_GROUP_ID_SOURCE_GROUP_ID] ON [dbo].[history_daily_by_groups_sequence]
(
	[flow_id] ASC,
	[source_group_id] ASC,
	[dest_group_id] ASC,
	[date] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DATE] ON [dbo].[history_daily_by_groups_sequence]
(
	[date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DEST_GROUP_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DEST_GROUP_ID] ON [dbo].[history_daily_by_groups_sequence]
(
	[dest_group_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_FLOW_ID] ON [dbo].[history_daily_by_groups_sequence]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_SOURCE_GROUP_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_SOURCE_GROUP_ID] ON [dbo].[history_daily_by_groups_sequence]
(
	[source_group_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_INTEGRATIONS_BY_DATE_FLOW_ID_INTEGRATION_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_INTEGRATIONS_BY_DATE_FLOW_ID_INTEGRATION_ID] ON [dbo].[history_daily_by_integrations]
(
	[flow_id] ASC,
	[date] ASC,
	[integration_id] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_INTEGRATIONS_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_INTEGRATIONS_DATE] ON [dbo].[history_daily_by_integrations]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_INTEGRATIONS_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_INTEGRATIONS_FLOW_ID] ON [dbo].[history_daily_by_integrations]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_INTEGRATIONS_INTEGRATION_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_INTEGRATIONS_INTEGRATION_ID] ON [dbo].[history_daily_by_integrations]
(
	[integration_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_BY_DATE_FLOW_ID_STATISTIC_EVENT_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_BY_DATE_FLOW_ID_STATISTIC_EVENT_ID] ON [dbo].[history_daily_by_statistic_event]
(
	[flow_id] ASC,
	[statistic_event_id] ASC,
	[date] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_DATE]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_DATE] ON [dbo].[history_daily_by_statistic_event]
(
	[date] ASC,
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_FLOW_ID] ON [dbo].[history_daily_by_statistic_event]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_STATISTIC_EVENT_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_HISTORY_DAILY_BY_STATISTIC_EVENT_STATISTIC_EVENT_ID] ON [dbo].[history_daily_by_statistic_event]
(
	[statistic_event_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [REPORTS_FLOWS_FK]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [REPORTS_FLOWS_FK] ON [dbo].[reports]
(
	[user_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [REPORTS_USERS_FK]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [REPORTS_USERS_FK] ON [dbo].[reports]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_USER_ACTION_LOGS_FLOW_VERSION_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_USER_ACTION_LOGS_FLOW_VERSION_ID] ON [dbo].[user_action_logs]
(
	[flow_version_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_USERS_PERMISSIONS_FLOWS_FLOW_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_USERS_PERMISSIONS_FLOWS_FLOW_ID] ON [dbo].[users_permissions_flows]
(
	[flow_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
/****** Object:  Index [IX_USERS_PERMISSIONS_FLOWS_USER_ID]    Script Date: 6/4/2024 1:53:39 PM ******/
CREATE NONCLUSTERED INDEX [IX_USERS_PERMISSIONS_FLOWS_USER_ID] ON [dbo].[users_permissions_flows]
(
	[user_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [new_cases]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [transferred]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [closed_by_yflow]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [new_messages]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [hsm_case]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [case_abandoned]
GO
ALTER TABLE [dbo].[history_daily] ADD  DEFAULT ((0)) FOR [monthly_users]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [new_cases]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [transferred]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [closed_by_yflow]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [new_messages]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [hsm_case]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [case_abandoned]
GO
ALTER TABLE [dbo].[history_daily_by_flow] ADD  DEFAULT ((0)) FOR [monthly_users]
GO
ALTER TABLE [dbo].[history_daily_by_integrations] ADD  DEFAULT ((0)) FOR [total_response_time]
GO
ALTER TABLE [dbo].[history_daily_by_integrations] ADD  DEFAULT ((0)) FOR [error_response_time]
GO


CREATE TABLE [dbo].[logs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[type] [int] NOT NULL,
	[message_id] [nvarchar](50) NULL,
	[date] [datetime2](7) NOT NULL,
	[message] [nvarchar](max) NULL,
	[stack] [nvarchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO


CREATE NONCLUSTERED INDEX [IX_LOGS_DATE] ON [dbo].[logs]
(
	[date] ASC
)
GO


IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[users]') AND name = 'login_type')
BEGIN
    ALTER TABLE [dbo].[users] ADD [login_type] [nvarchar](50) DEFAULT 'LOCAL' NULL
END


alter table users
add [can_delete]             BIT             DEFAULT ((0)) NULL,
[is_deleted]             BIT             DEFAULT ((0)) NULL
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_sessions](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [username] [nvarchar](100) NOT NULL,
    [login_time] [datetime2](7) NOT NULL,
    [logout_time] [datetime2](7) NULL,
    [ip_address] [nvarchar](50) NULL,
    [session_duration] [nvarchar](20) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [idx_login_time] ON [dbo].[user_sessions]
(
    [login_time] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [idx_username] ON [dbo].[user_sessions]
(
    [username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO


IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='detailed_events' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[detailed_events] (
        [id] [int] IDENTITY(1,1) NOT NULL,
        [fecha] [datetime2](7)  NOT NULL,
        [accion_realizada] [varchar](50) NOT NULL,
        [entidad] [varchar](100) NOT NULL,
        [usuario] [varchar](100) NOT NULL,
        [usuario_editado] [varchar](100) NULL,
        [id_entidad] [varchar](50) NOT NULL,
        [nombre_entidad] [varchar](100) NOT NULL,
        [propiedad] [varchar](100) NOT NULL,
        [valor_actual] [text] NULL,
        [valor_anterior] [text] NULL,
        CONSTRAINT [PK_detailed_events] PRIMARY KEY CLUSTERED 
        (
            [id] ASC
        ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

    -- Crear índices para mejorar el rendimiento en consultas frecuentes
    CREATE INDEX [IX_detailed_events_fecha] ON [dbo].[detailed_events]([fecha])
    CREATE INDEX [IX_detailed_events_usuario] ON [dbo].[detailed_events]([usuario])
    CREATE INDEX [IX_detailed_events_entidad] ON [dbo].[detailed_events]([entidad])
    CREATE INDEX [IX_detailed_events_accion] ON [dbo].[detailed_events]([accion_realizada])

    PRINT 'Tabla detailed_events creada correctamente.'
END
ELSE
BEGIN
    PRINT 'La tabla detailed_events ya existe.'
END