{"Version": 1, "WorkspaceRootPath": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|solutionrelative:tokenmanagerapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|solutionrelative:tokenmanagerapi\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi\\models\\tokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|solutionrelative:tokenmanagerapi\\models\\tokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0B2AAFE-8145-4695-966A-A84E86D9AB85}|TokenManagerApi.Tests\\TokenManagerApi.Tests.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi.tests\\tests\\tokenmanagerservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0B2AAFE-8145-4695-966A-A84E86D9AB85}|TokenManagerApi.Tests\\TokenManagerApi.Tests.csproj|solutionrelative:tokenmanagerapi.tests\\tests\\tokenmanagerservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi\\services\\itokenmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|solutionrelative:tokenmanagerapi\\services\\itokenmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|c:\\yoizen\\repositorios\\yflow\\yflow\\tokenmanagerapi\\services\\tokenmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D61C42B-9C57-418D-A357-35FFD640A915}|TokenManagerApi\\TokenManagerApi.csproj|solutionrelative:tokenmanagerapi\\services\\tokenmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:129:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Program.cs", "RelativeDocumentMoniker": "TokenManagerApi\\Program.cs", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Program.cs", "RelativeToolTip": "TokenManagerApi\\Program.cs", "ViewState": "AQIAAAkAAAAAAAAAAAAAACIAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:56:17.149Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "TokenResponse.cs", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Models\\TokenResponse.cs", "RelativeDocumentMoniker": "TokenManagerApi\\Models\\TokenResponse.cs", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Models\\TokenResponse.cs", "RelativeToolTip": "TokenManagerApi\\Models\\TokenResponse.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAkAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:54:51.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "README.md", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\README.md", "RelativeDocumentMoniker": "TokenManagerApi\\README.md", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\README.md", "RelativeToolTip": "TokenManagerApi\\README.md", "ViewState": "AQIAAAwAAAAAAAAAAAAAAB0AAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-01-22T12:53:57.058Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ITokenManagerService.cs", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Services\\ITokenManagerService.cs", "RelativeDocumentMoniker": "TokenManagerApi\\Services\\ITokenManagerService.cs", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Services\\ITokenManagerService.cs", "RelativeToolTip": "TokenManagerApi\\Services\\ITokenManagerService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAcAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:52:20.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "TokenManagerService.cs", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Services\\TokenManagerService.cs", "RelativeDocumentMoniker": "TokenManagerApi\\Services\\TokenManagerService.cs", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi\\Services\\TokenManagerService.cs", "RelativeToolTip": "TokenManagerApi\\Services\\TokenManagerService.cs", "ViewState": "AQIAACkAAAAAAAAAAAAcwEoAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:51:57.575Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "TokenManagerServiceTests.cs", "DocumentMoniker": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi.Tests\\Tests\\TokenManagerServiceTests.cs", "RelativeDocumentMoniker": "TokenManagerApi.Tests\\Tests\\TokenManagerServiceTests.cs", "ToolTip": "C:\\Yoizen\\Repositorios\\yFlow\\yFlow\\TokenManagerApi.Tests\\Tests\\TokenManagerServiceTests.cs", "RelativeToolTip": "TokenManagerApi.Tests\\Tests\\TokenManagerServiceTests.cs", "ViewState": "AQIAAFYAAAAAAAAAAAAkwGMAAAAKAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:51:19.722Z", "EditorCaption": ""}]}, {"DockedHeight": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}]}]}]}