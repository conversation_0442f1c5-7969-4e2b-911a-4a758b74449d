@import '_variables';
@import "_mixins";

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin: 0;

    .mono {
      font-family: monospace;
      font-weight: 500;
      color: #34495e;
    }
  }
}

.popup-content {
  padding: 1.5rem;
  overflow-y: auto;

  .section {
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  h3 {
    font-size: 1rem;
  }

  .field-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #34495e;
      font-weight: 500;
    }
  }

  .form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #dde1e7;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:focus {
      border-color: #e74c3c;
      box-shadow: 0 0 0 2px rgba(69, 193, 149, 0.1);
      outline: none;
    }
  }

  .password-strength {
    position: relative;

    meter {
      width: 100%;
      height: 4px;
      margin-top: 4px;

      &::-webkit-meter-bar {
        background: #eee;
      }

      &::-webkit-meter-optimum-value {
        background: #27ae60;
      }
    }
  }

  .alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.info {
      background: #e3f2fd;
      color: #1976d2;
    }
  }

  .permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;

    .permission-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      background: #f8f9fa;
      border-radius: 4px;

      label {
        margin: 0;
      }
    }
  }
}

.popup-footer {
  padding: 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &.btn-primary {
    background: #e74c3c;
    color: white;

    &:hover {
      background: darken(#e74c3c, 10%);
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: darken(#6c757d, 10%);
    }
  }
}

@media (max-width: 768px) {
  .popup-container {
    width: 95%;
  }

  .permissions-grid {
    grid-template-columns: 1fr !important;
  }
}
