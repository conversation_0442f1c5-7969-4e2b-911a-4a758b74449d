const express = require('express');
const { DniService } = require('./eval-dni');
class DniRoute {

    constructor(_dniService) {
        this.flowService = _dniService;
        this.router = express.Router();
        this.initializeRoutes();
    }

    async validate(req, res, next) {
        try {
            const { url } = req.body;
            var dniService = new DniService();

            const response = await dniService.validate(url);

            return res.status(200).send({ success: true, data: response });
        } catch (error) {
            return res.status(400).send({ success: false, message: 'Ocurrió un error durante la validación del dni' });
        }
    }

    initializeRoutes() {
        this.router.post('/validate', this.validate.bind(this));
    }

    getRouter() {
        return this.router;
    }
}
module.exports.DniRoute = DniRoute;