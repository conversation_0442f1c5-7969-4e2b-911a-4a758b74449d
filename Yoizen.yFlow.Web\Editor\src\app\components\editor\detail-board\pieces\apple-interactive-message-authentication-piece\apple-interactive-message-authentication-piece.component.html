<div class="authentication card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sign-in"></span> {{ 'PIECE_APPLE_INTERATIVE_MESSAGE_AUTHENTICATION' | translate }}
  </div>
  <div class="options">
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_RESPONSETYPE' | translate}}:</span>
      <select [(ngModel)]="model.responseType" [disabled]="readOnly" class="select">
        <option [ngValue]="appleInteractiveMessageAuthenticationResponseTypes.code">code</option>
        <option [ngValue]="appleInteractiveMessageAuthenticationResponseTypes.token">token</option>
      </select>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_SCOPE' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.scope"
             [ngClass]="{'invalid-input': !model.isScopeValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_CLIENTSECRET' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.clientSecret"
             [ngClass]="{'invalid-input': !model.isClientSecretValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
  </div>
  <app-apple-interactive-received-and-reply
    [receivedMessage]="model.receivedMessage"
    [replyMessage]="model.replyMessage"
    [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
  <div class="navigation">
    <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_BLOCKS_TITLE' | translate }}</div>
    <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_BLOCKS_INFO' | translate }}</div>
    <div class="options">
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_BLOCKS_BLOCK_SUCCESS' | translate}}:</span>
        <app-block-picker class="input"
                          [blockId]="model.authenticationSuccessBlockId"
                          (onSelectNewBlock)="selectSuccessBlock($event)"
                          (onDeleteBlock)="deleteSuccessBlock()"
                          [readOnly]="readOnly"
                          [isInvalid]="!model.isSuccessBlockValid(editorService)"></app-block-picker>
      </div>
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_AUTHENTICATION_BLOCKS_BLOCK_FAILED' | translate}}:</span>
        <app-block-picker class="input"
                          [blockId]="model.authenticationErrorBlockId"
                          (onSelectNewBlock)="selectErrorBlock($event)"
                          (onDeleteBlock)="deleteErrorBlock()"
                          [readOnly]="readOnly"
                          [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
      </div>
    </div>
  </div>
</div>
