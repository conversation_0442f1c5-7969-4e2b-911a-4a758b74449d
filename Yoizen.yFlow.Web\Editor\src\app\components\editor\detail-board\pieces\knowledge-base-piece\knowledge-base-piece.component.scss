@import "_variables";
@import "_mixins";

.knowledge-base {
  background-color: #fff;
  width: 500px;

  .category-name {
    display: flex;

    select {
      width: 100%;
      height: 30px;
      font-weight: normal;
      border-radius: 5px;
      overflow: scroll;
    }
  }

  .cognitivity {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }
  }

  .message {
    margin-top: 6px;

    .title {
      font-family: Cera <PERSON> Pro, San Francisco, Segoe UI Semibold, Arial, sans-serif;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }
  }

}