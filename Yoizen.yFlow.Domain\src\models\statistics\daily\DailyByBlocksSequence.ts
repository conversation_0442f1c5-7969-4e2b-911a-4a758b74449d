import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}
export class DailyByBlocksSequence extends DailyBase {
    channel: string;
    flowId: number;
    sourceBlockId: string;
    destBlockId: string;
    version: number;
    total: number;
    typeSequence: number;
    constructor(datetime: Moment, data?: { flowId: number, channel: string, sourceBlockId: string, destBlockId: string, type: number, version: number }) {
        super(datetime);
        if (data) {
            this.flowId = data.flowId;
            this.channel = data.channel;
            this.sourceBlockId = data.sourceBlockId;
            this.destBlockId = data.destBlockId;
            this.version = data.version;
            this.typeSequence = data.type;
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.BlocksSequence;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "source_block_id", "dest_block_id", "type", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.sourceBlockId, this.destBlockId, this.typeSequence, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_blocks_sequence';
    }
}