FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Create project structure
RUN mkdir -p Yoizen.yFlow.TokenManagerApi Yoizen.yFlow.TokenManagerApi.Tests/Tests

# Copy solution and project files
COPY ["TokenManagerApi.sln", "./"]
COPY ["Yoizen.yFlow.TokenManagerApi/TokenManagerApi.csproj", "./Yoizen.yFlow.TokenManagerApi/"]
COPY ["Yoizen.yFlow.TokenManagerApi.Tests/TokenManagerApi.Tests.csproj", "./Yoizen.yFlow.TokenManagerApi.Tests/"]

# Restore dependencies
RUN dotnet restore TokenManagerApi.sln

# Copy source files
COPY ["Yoizen.yFlow.TokenManagerApi/", "./Yoizen.yFlow.TokenManagerApi/"]
COPY ["Yoizen.yFlow.TokenManagerApi.Tests/", "./Yoizen.yFlow.TokenManagerApi.Tests/"]

# Run tests
ENTRYPOINT ["dotnet", "test", "./Yoizen.yFlow.TokenManagerApi.Tests/TokenManagerApi.Tests.csproj", "--logger:trx"]
