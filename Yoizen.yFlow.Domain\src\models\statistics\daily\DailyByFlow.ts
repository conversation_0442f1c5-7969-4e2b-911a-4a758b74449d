import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByFlow extends DailyBase {
    channel: string;
    flowId: number;
    flow: { id: number; name: string; };
    newCase: number;
    transferredToYSocial: number;
    closedByYFlow: number;
    newMessage: number;
    hsmCase: number;
    caseAbandoned: number;
    monthlyUsers: number;

    constructor(datetime: Moment, data?: { flowId: number, channel: string }) {
        super(datetime);
        /**
         * Casos nuevos
         */
        this.newCase = 0;

        /**
         * Casos transferidos a ySocial
         */
        this.transferredToYSocial = 0;

        /**
         * Casos cerrados a través de yFlow
         */
        this.closedByYFlow = 0;

        /**
         * Mensajes nuevos
         */
        this.newMessage = 0;

        /**
         * Cases nuevos de hsm
         */
        this.hsmCase = 0;

        /**
         * Casos abandonados
         */
        this.caseAbandoned = 0;

        /**
         * Usuarios mensuales
         */
        this.monthlyUsers = 0;
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.flow = {
                id: data.flowId,
                name: ""
            }
        }
    }

    type() {
        return DailyInfoTypes.ByFlow;
    }

    getType() {
        return 'daily_flow';
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "channel", "new_cases", "transferred", "closed_by_yflow", "new_messages", "hsm_case", "case_abandoned", "monthly_users"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.channel, this.newCase, this.transferredToYSocial, this.closedByYFlow, this.newMessage, this.hsmCase, this.caseAbandoned, this.monthlyUsers];
    }
}