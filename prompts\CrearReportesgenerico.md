# 🤖 Guía Asistida por IA para Creación de Reportes en yFlow

## 🎯 Objetivo
Crear un nuevo reporte basado en intervalos en yFlow, siguiendo los patrones establecidos como el reporte de Login/Logout de usuarios.

## 📝 Preguntas Iniciales

Antes de proceder con la implementación, por favor responde estas preguntas:

1. ¿Cómo se llamará este reporte? (ej., `ActividadUsuario`, `VentasProducto`, `RendimientoSistema`)
   - Este nombre se usará para archivos, clases y tablas de base de datos.

2. ¿Qué datos recolectará este reporte? Lista los campos:
   - Campos primarios (requeridos):
     - Campo 1 (ej., `username`, `product_id`, `event_type`): [tipo de dato]
     - Campo 2 (campo de fecha para filtrado - requerido): [datetime]
   - Campos secundarios (opcionales):
     - Campo 3: [tipo de dato]
     - Campo 4: [tipo de dato]
     - Campo 5: [tipo de dato]

3. ¿Qué intervalo de tiempo aplica a este reporte?
   - Diario
   - Por hora
   - Intervalo personalizado

4. ¿Cómo se deberían filtrar los datos?
   - Solo por rango de fechas
   - Por rango de fechas y valor de campo específico
   - Otros criterios

## 🗂️ Referencia de Estructura del Proyecto

Rutas de archivos clave para implementación:

- `@Yoizen.yFlow.Domain/src/models/statistics/`: Modelos de dominio para estadísticas
- `@Yoizen.yFlow.Web/models/`: Modelos para la interfaz web
- `@Yoizen.yFlow.IntervalServices/src/application/services/FtpService.ts`: Servicio de intervalos
- `@Yoizen.yFlow.Scripts/Database/`: Scripts de base de datos (MSSQL/MySQL)
- `@Yoizen.yFlow.Web/Editor/src/app/models/FtpConfigurations.ts`: Configuraciones FTP
- `@Yoizen.yFlow.Web/Editor/src/app/components/configuration/`: Componentes de UI
- `@Yoizen.yFlow.Web/routes/configurations.js`: Controlador de configuraciones
- `@Yoizen.yFlow.Web/Editor/src/assets/i18n/`: Archivos de traducción

## 📋 Pasos de Implementación

### Paso 1: Creación de Tabla en Base de Datos

Reemplaza los marcadores con tus respuestas de las preguntas iniciales.

**Para MSSQL** en `@Yoizen.yFlow.Scripts/Database/mssql/EsquemaInicial.sql`:

```sql
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[{{nombre_reporte_snake_case}}](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [{{nombre_campo1}}] [{{tipo_campo1}}] NOT NULL,
    [{{nombre_campo2}}] [datetime2](7) NOT NULL,  -- Este será tu campo de fecha para filtros
    {{#each campos_opcionales}}
    [{{nombre}}] [{{tipo}}] {{#if nullable}}NULL{{else}}NOT NULL{{/if}},
    {{/each}}
PRIMARY KEY CLUSTERED ([id] ASC)
)
GO

-- Crear índices para optimizar consultas
CREATE NONCLUSTERED INDEX [idx_{{nombre_reporte_snake_case}}_fecha] ON [dbo].[{{nombre_reporte_snake_case}}]
(
    [{{nombre_campo2}}] ASC
)
GO

CREATE NONCLUSTERED INDEX [idx_{{nombre_reporte_snake_case}}_busqueda] ON [dbo].[{{nombre_reporte_snake_case}}]
(
    [{{nombre_campo1}}] ASC
)
GO
```

**Para MySQL** en `@Yoizen.yFlow.Scripts/Database/mysql/EsquemaInicialMysql.sql`:

```sql
CREATE TABLE `{{nombre_reporte_snake_case}}` (
  `id` int NOT NULL AUTO_INCREMENT,
  `{{nombre_campo1}}` varchar(100) NOT NULL,
  `{{nombre_campo2}}` datetime NOT NULL,
  {{#each campos_opcionales}}
  `{{nombre}}` {{tipo}} {{#if nullable}}DEFAULT NULL{{else}}NOT NULL{{/if}},
  {{/each}}
  PRIMARY KEY (`id`),
  KEY `idx_{{nombre_reporte_snake_case}}_fecha` (`{{nombre_campo2}}`),
  KEY `idx_{{nombre_reporte_snake_case}}_busqueda` (`{{nombre_campo1}}`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### Paso 2: Implementación del Modelo de Dominio

Crear archivo: `@Yoizen.yFlow.Domain/src/models/statistics/{{NombreReporte}}.ts`:

```typescript
import { DataTypes, Model } from 'sequelize';
import sequelize from '../../../../Yoizen.yFlow.Web/helpers/sequelize';
import moment from 'moment';

export class {{NombreReporte}} extends Model {
    declare id: number;
    declare {{nombre_campo1}}: {{tipo_typescript_campo1}};
    declare {{nombre_campo2}}: Date;
    {{#each campos_opcionales}}
    declare {{nombre}}: {{tipo_typescript}};
    {{/each}}

    static generateExcel(start: moment.Moment, end: moment.Moment, offset: number, defaultLanguage: string = 'es'): Promise<string> {
        return this.generateReport(start, end, offset, defaultLanguage, true);
    }

    static generateText(folder: string, start: moment.Moment, end: moment.Moment, offset: number, operator: string, defaultLanguage: string = 'es'): Promise<string> {
        return this.generateReport(start, end, offset, defaultLanguage, false, operator);
    }

    private static async generateReport(
        start: moment.Moment, 
        end: moment.Moment, 
        offset: number, 
        defaultLanguage: string = 'es',
        isExcel: boolean = true,
        operator: string = ','
    ): Promise<string> {
        const fs = require('fs');
        const archiver = require('archiver');
        const Excel = require('exceljs');
        const path = require('path');
        const { reportsFolder } = require('../../../../Yoizen.yFlow.Web/helpers/folders');

        const startDateStr = start.format('YYYY-MM-DD HH:mm:ss');
        const endDateStr = end.format('YYYY-MM-DD HH:mm:ss');
        
        const registros = await {{NombreReporte}}.findAll({
            where: {
                {{nombre_campo2}}: {
                    [sequelize.Op.between]: [startDateStr, endDateStr]
                }
            },
            order: [[{{campo_filtro_para_ordenar}}, 'ASC']]
        });

        const fileName = `{{nombre_reporte_snake_case}}_${start.format('YYYYMMDD')}.${isExcel ? 'xlsx' : 'csv'}`;
        const filePath = path.join(reportsFolder, fileName);

        if (isExcel) {
            const workbook = new Excel.Workbook();
            const worksheet = workbook.addWorksheet('{{NombreReporte}}');
            
            worksheet.columns = [
                { header: '{{Etiqueta_Campo1}}', key: '{{nombre_campo1}}', width: 20 },
                { header: '{{Etiqueta_Campo2}}', key: '{{nombre_campo2}}', width: 22 },
                {{#each campos_opcionales}}
                { header: '{{etiqueta}}', key: '{{nombre}}', width: {{ancho}} },
                {{/each}}
            ];
            
            registros.forEach(registro => {
                worksheet.addRow({
                    {{nombre_campo1}}: registro.{{nombre_campo1}},
                    {{nombre_campo2}}: moment(registro.{{nombre_campo2}}).format('DD/MM/YYYY HH:mm:ss'),
                    {{#each campos_opcionales}}
                    {{nombre}}: {{#if es_fecha}}registro.{{nombre}} ? moment(registro.{{nombre}}).format('DD/MM/YYYY HH:mm:ss') : ''{{else}}registro.{{nombre}} || ''{{/if}},
                    {{/each}}
                });
            });
            
            await workbook.xlsx.writeFile(filePath);
        } else {
            const csvRows = [`{{Etiqueta_Campo1}}${operator}{{Etiqueta_Campo2}}${operator}{{#each campos_opcionales}}{{etiqueta}}${operator}{{/each}}`.slice(0, -operator.length)];
            
            registros.forEach(registro => {
                const row = [
                    registro.{{nombre_campo1}},
                    moment(registro.{{nombre_campo2}}).format('DD/MM/YYYY HH:mm:ss'),
                    {{#each campos_opcionales}}
                    {{#if es_fecha}}registro.{{nombre}} ? moment(registro.{{nombre}}).format('DD/MM/YYYY HH:mm:ss') : ''{{else}}registro.{{nombre}} || ''{{/if}},
                    {{/each}}
                ].join(operator);
                
                csvRows.push(row);
            });
            
            fs.writeFileSync(filePath, csvRows.join('\n'));
        }
        
        const zipFilePath = path.join(reportsFolder, `{{nombre_reporte_snake_case}}_${start.format('YYYYMMDD')}.zip`);
        const output = fs.createWriteStream(zipFilePath);
        const archive = archiver('zip', { zlib: { level: 9 } });
        
        return new Promise((resolve, reject) => {
            output.on('close', () => {
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
                resolve(zipFilePath);
            });
            
            archive.on('error', (err) => {
                reject(err);
            });
            
            archive.pipe(output);
            archive.file(filePath, { name: fileName });
            archive.finalize();
        });
    }
}

{{NombreReporte}}.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    {{nombre_campo1}}: {
        type: DataTypes.{{tipo_sequelize_campo1}},
        allowNull: false
    },
    {{nombre_campo2}}: {
        type: DataTypes.DATE,
        allowNull: false
    },
    {{#each campos_opcionales}}
    {{nombre}}: {
        type: DataTypes.{{tipo_sequelize}},
        allowNull: {{#if nullable}}true{{else}}false{{/if}}
    },
    {{/each}}
}, {
    sequelize,
    modelName: '{{nombre_reporte_snake_case}}',
    tableName: '{{nombre_reporte_snake_case}}',
    timestamps: false
});
```

### Paso 3: Implementación del Modelo Web

Crear archivo: `@Yoizen.yFlow.Web/models/{{nombreReporteCamelCase}}.js`:

```javascript
var app = require('../app.js');
const { DataTypes, Model } = require('sequelize');

class {{NombreReporte}} extends Model {
    // Métodos personalizados específicos para el modelo web
    static async {{metodoPersonalizado}}({{parametrosMetodo}}) {
        try {
            // Implementar lógica específica
            // Por ejemplo: registrar una entrada, consultas personalizadas, etc.
        } catch (error) {
            console.error('Error en método específico:', error);
            return false;
        }
    }
}

// Inicializar modelo con la misma estructura que el modelo de dominio
{{NombreReporte}}.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    {{nombre_campo1}}: {
        type: DataTypes.{{tipo_sequelize_campo1}},
        allowNull: false
    },
    {{nombre_campo2}}: {
        type: DataTypes.DATE,
        allowNull: false
    },
    {{#each campos_opcionales}}
    {{nombre}}: {
        type: DataTypes.{{tipo_sequelize}},
        allowNull: {{#if nullable}}true{{else}}false{{/if}}
    },
    {{/each}}
}, {
    sequelize: app.sequelize,
    modelName: '{{nombre_reporte_snake_case}}',
    tableName: '{{nombre_reporte_snake_case}}',
    timestamps: false
});

module.exports = {{NombreReporte}};
```

### Paso 4: Actualización del Modelo de Configuración FTP

Actualizar `@Yoizen.yFlow.Web/Editor/src/app/models/FtpConfigurations.ts`:

```typescript
// ...existing code...

@jsonMember(Boolean)
public {{nombreReporteCamelCase}}Report: boolean = false;

// ...existing code...

isValid(): boolean {
    if (!this.enabled) {
        return true;
    }

    // ...existing code...

    return (this.blocksReport ||
        this.commandsReport ||
        this.defaultAnswersReport ||
        this.derivationsReport ||
        this.eventsReport ||
        this.globalsReport ||
        this.integrationsReport ||
        this.detailedStatisticEventReport ||
        this.abandonedCasesReport ||
        this.userSessionsReport ||
        this.{{nombreReporteCamelCase}}Report);  // Agregar tu nuevo reporte aquí
}
```

### Paso 5: Actualización del Componente UI de Configuración

Actualizar `@Yoizen.yFlow.Web/Editor/src/app/components/configuration/configuration.component.html`:

```html
<div class="reports-container">
    <!-- ...existing code... -->
    
    <!-- Agregar el nuevo elemento de reporte -->
    <div class="report-item">
        <div class="report-header">
            <i class="fas fa-file-alt"></i>
            <span>{{ "{{CLAVE_TRADUCCION_NOMBRE_REPORTE}}" | translate }}</span>
        </div>
        <ui-switch
            [(ngModel)]="ftpConfig.{{nombreReporteCamelCase}}Report"
            [disabled]="!ftpConfig.enabled"
        ></ui-switch>
    </div>
    
    <!-- ...existing code... -->
</div>
```

### Paso 6: Actualización del Servicio de Intervalos FTP

Actualizar `@Yoizen.yFlow.IntervalServices/src/application/services/FtpService.ts`:

```typescript
import { {{NombreReporte}} } from '../../../../Yoizen.yFlow.Domain/src/models/statistics/{{NombreReporte}}';

// ...existing code...

// Agregar el procesamiento para el nuevo reporte
if (reportData.{{nombreReporteCamelCase}}Report) {
    try {
        console.log(`Generando reporte {{nombreReporteCamelCase}} para el intervalo ${initInterval} ${endInterval}`);
        if (reportData.protocol === 'ftp' || reportData.protocol === 'sftp') {
            const fileName = `{{NombreReporte}}_${initInterval.format("YYYYMMDD")}.zip`;
            const destFile = `${config.path}/${fileName}`;
            
            let file;
            if (config.saveInExcel) {
                file = await {{NombreReporte}}.generateExcel(initInterval, endInterval, config.offset, globalConfig.defaultLanguaje);
            } else {
                file = await {{NombreReporte}}.generateText(`{{NombreReporte}}_${initInterval.format("YYYYMMDD")}`, 
                    initInterval, endInterval, config.offset, config.operator, globalConfig.defaultLanguaje);
            }
            
            await ftp.Send(config, destFile, fs.createReadStream(file, 'utf8'));
            
            if (fs.existsSync(file)) {
                fs.unlinkSync(file);
            }
            
            console.log(`El Reporte {{NombreReporte}} ha sido enviado por ftp exitosamente`);
        }
    } catch (e) {
        console.error(`Ocurrió un error enviando el reporte {{nombreReporteCamelCase}}: ${e}`);
    }
}

// ...existing code...
```

### Paso 7: Actualización del Controlador de Configuraciones

Actualizar `@Yoizen.yFlow.Web/routes/configurations.js`:

```javascript
class ConfigurationsController {
    // ...existing code...
    
    async setAutomaticReportConfig(req, res) {
        // ...existing code...
        
        var {{nombreReporteCamelCase}}Report = false;
        
        // ...existing code...
        
        if (typeof (req.body.{{nombreReporteCamelCase}}Report) === 'boolean') {
            {{nombreReporteCamelCase}}Report = req.body.{{nombreReporteCamelCase}}Report;
        }
        
        // ...existing code...
        
        let content = {
            // ...existing code...
            {{nombreReporteCamelCase}}Report: {{nombreReporteCamelCase}}Report,
            // ...existing code...
        }
        
        // ...existing code...
    }
}
### Paso 8: Actualización de Claves de Traducción

Debes GENERAR  las siguientes claves de traducción a los archivos de idioma pero NO APLICARLAS al archivo JSON: 

- `@Yoizen.yFlow.Web/Editor/src/assets/i18n/es.json`
- `@Yoizen.yFlow.Web/Editor/src/assets/i18n/en.json`
- `@Yoizen.yFlow.Web/Editor/src/assets/i18n/pt.json`

Ejemplo para el archivo `es.json`:

```json
{
    "{{CLAVE_TRADUCCION_NOMBRE_REPORTE}}": "Reporte de {{NombreReporte}}",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}": "Reporte de {{NombreReporte}}",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}_DESCRIPTION": "{{DescripcionReporte}}"
}
```

Para `en.json`:
```json
{
    "{{CLAVE_TRADUCCION_NOMBRE_REPORTE}}": "{{NombreReporte}} Report",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}": "{{NombreReporte}} Report",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}_DESCRIPTION": "{{ReportDescription}}"
}
```

Para `pt.json`:
```json
{
    "{{CLAVE_TRADUCCION_NOMBRE_REPORTE}}": "Relatório de {{NombreReporte}}",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}": "Relatório de {{NombreReporte}}",
    "REPORTS_{{NOMBRE_REPORTE_MAYUSCULAS}}_DESCRIPTION": "{{DescricaoRelatorio}}"
}
```

Reemplaza los placeholders con los valores específicos de tu reporte. La clave `{{CLAVE_TRADUCCION_NOMBRE_REPORTE}}` debe coincidir con la referencia en el archivo de configuración HTML.

## 🔄 Proceso de Validación

Después de la implementación, sigue estos pasos para validar tu reporte:

1. **Validación del Esquema de Base de Datos**:
   - Ejecuta los scripts SQL en tu base de datos de desarrollo
   - Verifica que la tabla se haya creado con las columnas e índices correctos

2. **Verificación de Funcionalidad del Modelo**:
   - Agrega datos de prueba a tu nueva tabla
   - Crea un script de prueba simple que ejecute la generación de reportes de tu modelo
   - Verifica el formato correcto de salida (Excel/CSV/ZIP)

3. **Prueba de Integración de UI**:
   - Verifica que la nueva opción de reporte aparezca en la UI de configuración
   - Prueba habilitar/deshabilitar la opción de reporte
   - Confirma que los cambios se guarden correctamente

4. **Prueba de Extremo a Extremo**:
   - Configura un servidor FTP/SFTP para pruebas
   - Habilita tu reporte en la configuración
   - Espera a que el intervalo se ejecute o actívalo manualmente
   - Verifica que el archivo de reporte se genere y suba correctamente

## 🧰 Plantillas de Ayuda y Ejemplos

### Referencia de Mapeo de Tipos de Base de Datos

| Tipo Conceptual | Tipo MSSQL       | Tipo MySQL      | Tipo Sequelize   | Tipo TypeScript |
|----------------|------------------|-----------------|------------------|----------------|
| Identificador  | int IDENTITY     | int AUTO_INCREMENT | INTEGER        | number         |
| Texto Corto    | nvarchar(50)     | varchar(50)     | STRING(50)       | string         |
| Texto Largo    | nvarchar(MAX)    | text            | TEXT             | string         |
| Fecha y Hora   | datetime2        | datetime        | DATE             | Date           |
| Booleano       | bit              | tinyint(1)      | BOOLEAN          | boolean        |
| Numérico       | decimal(18,2)    | decimal(18,2)   | DECIMAL(18,2)    | number         |

### Ejemplo: Referencia de Implementación de UserSession

Para un ejemplo completo, estudia la implementación de `UserSession`:

1. **Esquema de base de datos**: Tabla con `username`, `login_time`, `logout_time`, `ip_address`, etc.

2. **Modelo de dominio**: Métodos para generación de reportes en `@Yoizen.yFlow.Domain/src/models/statistics/UserSession.ts`

3. **Modelo web**: API para registrar eventos de sesión en `@Yoizen.yFlow.Web/models/userSession.js`

4. **Integración API**: Endpoints de autenticación que registran datos de sesión:
   ```javascript
   // Para login
   await UserSession.create({
       username: user.name,
       login_time: moment().toDate(),
       ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress
   });

   // Para logout
   await UserSession.recordLogout(decoded.name);
   ```

## 🛠️ Mejores Prácticas

1. **Convenciones de Nomenclatura**: Usa nombres consistentes en todos los componentes
   - Nombres de clase: PascalCase (ej., `VentasProducto`)
   - Variables/propiedades: camelCase (ej., `ventasProductoReport`)
   - Tablas/columnas DB: snake_case (ej., `ventas_producto`)

2. **Manejo de Errores**: Implementa un manejo robusto de errores en todas las capas:
   - Consultas a base de datos
   - Operaciones de archivos
   - Transferencias FTP/SFTP

3. **Consideraciones de Rendimiento**:
   - Agrega índices apropiados para campos de consulta
   - Limita conjuntos de resultados para reportes grandes
   - Limpia archivos temporales

4. **Mejores Prácticas de Seguridad**:
   - Sanitiza entradas
   - Usa consultas parametrizadas
   - Maneja credenciales de forma segura

## 📌 Lista de Verificación para Completar

- [ ] Esquema de base de datos creado
- [ ] Modelo de dominio implementado
- [ ] Modelo web implementado  
- [ ] Configuración FTP actualizada
- [ ] Componente de configuración UI actualizado
- [ ] Servicio FTP actualizado
- [ ] Controlador de configuración actualizado
- [ ] Claves de traducción generadas
- [ ] Pruebas de validación realizadas
- [ ] Documentación actualizada