﻿@import '_variables';
@import "_mixins";

.button {
  width: 600px;
  background: #fff;
  min-height: 160px;
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100001100;
  border-radius: 7px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.13);
  border: solid 1px rgba(0, 0, 0, 0.09);
  cursor: default;

  &.with-condition {
    width: 600px;
  }

  & > div {
    position: relative;
    height: 100%;
    width: 100%;
  }

  .name {
    padding: 10px;
    border-bottom: 1px solid $cardSeparatorBorderColor;
    text-align: center;

    input {
      width: 70%;
      border-radius: 10px;
      border: 1px solid #9a9a9a;
      padding-left: 10px;
      padding-right: 10px;
      height: 30px;
      color: #000;
      font-weight: normal;
      text-align: center;
    }
  }

  .close-button {
    @include trash;
    position: absolute;
    right: -15px;
    top: -15px;

    &:hover {
      color: #555;
    }
  }

  &:hover {
    .close-button {
      @include trashOver;
    }
  }

  &.persistent {
    left: 90%;

    .name {
      input {
        text-align: left;
      }
    }
  }

  .block {
    vertical-align: top;
    display: flex;
    padding-bottom: 10px;

    label {
      margin-right: 10px;
      margin-top: auto;
      margin-bottom: auto;
    }
  }

  .tabs {
    padding: 10px;

    .empty {
      .alert {
        margin-bottom: 0;
      }
    }
  }
}

.add-condition {
  text-align: center;
  text-transform: uppercase;
  padding: 15px 0;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  font-weight: normal;
  color: $linkActionColor;

  &.visibility {
    border-bottom: 1px solid $cardSeparatorBorderColor;
  }

  &.condition {
    border-top: 1px solid $cardSeparatorBorderColor;
  }

  &:hover {
    color: lighten($linkActionColor, 10%);
  }

  span {
    display: inline-block;
    margin-right: 10px;
    position: absolute;
    left: -10px;
    top: 18px;
    margin-left: 30px;
  }
}

.data {
  padding-top: 10px;
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  &.visibility {
    border-bottom: 1px solid #ddd;
    padding: 5px 10px;
  }

  &.url {
    padding-top: 0;
  }

  &.condition {
    border-top: 1px solid $cardSeparatorBorderColor;
  }

  .title {
    width: 100%;
    margin: 5px;
  }

  .input {
    margin-right: 10px;
  }

  .condition {
    margin-bottom: 5px;
  }

  .trash {
    @include trash;
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);

    &:hover {
      color: #555;
    }
  }

  &:hover {
    .trash {
      @include trashOver;
    }
  }

  .input-row {
    display: flex;
    flex-direction: row;
    width: 100%;
    margin-bottom: 5px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    /*label {

    }*/

    .expresion-input {
      margin-left: 5px;
      flex-grow: 1;
      justify-self: flex-end;
      align-self: flex-end;
    }

    .field-label {
      margin-bottom: 0;
    }

    ::ng-deep .app-variable-selector-input {
      flex-grow: 1;
      justify-self: flex-start;
      align-self: flex-start;
      padding-right: 10px;
      .block {
        width: 100%;
      }
      .block-selected {
        .trash {
          position: absolute;
        }
      }
    }
    ::ng-deep .app-input-with-variables {
      flex-grow: 1;
      width: 100%;
      justify-self: flex-start;
      align-self: flex-start;
      padding-right: 10px;
      textarea {
        width: 100%;
      }
      input {
        width: 100%;
      }
    }
  }

  select{
    height: 30px;
    font-weight: normal;
    margin: 0 10px;
  }
}

.option {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-bottom: 5px;
  align-items: center;

  & > .title {
    margin-bottom: 0;
    margin-right: 5px;
  }

  app-input-with-variables {
    flex-grow: 1;
    flex-shrink: 1;
  }

  .info {
    margin-left: 5px;
    display: inline-block;
    vertical-align: middle;
    color: #767676;

    &:hover {
      color: #555;
    }
  }
}
