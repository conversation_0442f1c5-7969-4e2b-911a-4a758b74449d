@import '_variables';
@import '_mixins';

.rest {
	background-color: $block-base;
	width: 800px;

  .smart-form {
    padding: 0px 10px 10px 10px;

    label {
      display: block;
    }
  }

	.varOutput {
		padding: 10px;
		border-top: 1px solid $gray;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
    }

    .empty {
      .alert {
        margin-bottom: 0
      }
    }

    .fields {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }
      }

      .field-row {
        display: table-row;
        padding-top: 3px;
        padding-bottom: 3px;
        height: 40px;

        .field-cell {
          display: table-cell;
          width: 50%;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        &:last-child {
          .field-cell {
            border-bottom: 1px none $sidebarBorderColor;
          }
        }
      }
    }

		.dataInput,
		.dataHeaders,
		.dataUrl{
			margin-bottom: 10px;
		}
		input{
			width: 48%;
			margin-right: 10px;
		}
		.dataOutput{
      margin-bottom: 10px;
      display: flex;
      flex-direction: row;
			input,
			select{
        margin-left: 10px;
				width: 32%;
			}
		}
		.addInput,
		.addOutput{
			border: 1px solid $gray;
			border-radius: 10px;
			padding: 5px 10px;
			display: inline-block;
			font-size: 12px;
			color: $gray;
			height: 30px;
			vertical-align: top;
		}
		p{
			text-align: left;
			margin-bottom: 10px;
		}
		.addInput:hover,
		.addOutput:hover{
			color: $darkGray;
			cursor: pointer;
		}
	}
  .input-variable-area {
    width: 24%;
  }

  .next, .smart-form {
    display: flex;
    flex-direction: row;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  .next {
    padding: 10px;
    border-top: 1px solid $gray;
    position: relative;

    .input {
      width: 50%;
    }

    label, input {
      margin-right: 10px;
    }

    .trash {
      @include trash;
      position: absolute;
      right: -16px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        @include trashOver;
      }
    }
  }
}
