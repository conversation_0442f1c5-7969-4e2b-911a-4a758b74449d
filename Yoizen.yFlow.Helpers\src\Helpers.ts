import { DbContext } from "./ConfigDbContext";
import { DbIntegrationsAudit } from "./ConfigDbIntegrationsAudit";
import { DbIntervals } from "./ConfigDbInterval";
import { DbYFlow } from "./ConfigDbYFlow";
import { logger } from "./Logger";

export function parseToBoolean(value: string, defaultValue?: boolean): boolean {
    if (isEnvironmentVariableValid(value)) {
        return value.toLowerCase() === 'true';
    }
    return defaultValue;
}

export function parseToInt(value: string, defaultValue?: number): number {
    if (isEnvironmentVariableValid(value)) {
        return Number(value);
    }

    return defaultValue;
}
export function isEnvironmentVariableValid(val) {
    return (typeof (val) === 'string' && val.length > 0) ||
        (typeof (val) === 'number' && !isNaN(val)) ||
        (typeof (val) === 'boolean');
}

export function importReplaceAll() {
    if (typeof String.prototype.replaceAll != 'function') {
        String.prototype.replaceAll = function (search, replacement) {
            function escapeRegExp(str) {
                return str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
            }

            let target = this;
            return target.replace(new RegExp(escapeRegExp(search), 'g'), replacement);
        };
    }
}

export function plainToClass<T extends object>(cls: { new(): T }, plainObject: Partial<T>): T {
    const instance = new cls();

    for (const key in plainObject) {
        if (Object.prototype.hasOwnProperty.call(plainObject, key) && key in instance) {
            (instance as any)[key] = plainObject[key as keyof T];
        }
    }

    return instance;
}

export function convertObjectToClass<T>(object: T): new () => T {
    return object.constructor as new () => T;
}

export function isNullOrUndefined(value: any): any {
    return value === null || value === undefined;
}

export function parseConnectionString(connectionString: string, config: DbContext | DbIntegrationsAudit | DbYFlow | DbIntervals): void {
    if (isEnvironmentVariableValid(connectionString)) {
        switch (config.dialect) {
            case 'mssql': {
                const serverRegex = /Server=([^,;]+)(?:,(\d+))?/i;
                const databaseRegex = /Database=([^;]+)/i;
                const userRegex = /User Id=([^;]+)/i;
                const passwordRegex = /Password=([^;]+)/i;

                const serverMatch = config.connectionString.match(serverRegex);
                const databaseMatch = config.connectionString.match(databaseRegex);
                const userMatch = config.connectionString.match(userRegex);
                const passwordMatch = config.connectionString.match(passwordRegex);

                if (!serverMatch || !databaseMatch || !userMatch || !passwordMatch) {
                    logger.error(`Invalid MSSQL connection string format para ${typeof config}`);
                    process.exit(9);
                }

                config.host = serverMatch[1];
                config.port = parseToInt(serverMatch[2] || '1433');
                config.name = databaseMatch[1];
                config.username = userMatch[1];
                config.password = passwordMatch[1];
                break;
            }
            case 'mysql': {
                const serverRegex = /Server=([^;]+)/i;
                const portRegex = /Port=(\d+)/i;
                const databaseRegex = /Database=([^;]+)/i;
                const userRegex = /Uid=([^;]+)/i;
                const passwordRegex = /Pwd=([^;]+)/i;

                const serverMatch = config.connectionString.match(serverRegex);
                const portMatch = config.connectionString.match(portRegex);
                const databaseMatch = config.connectionString.match(databaseRegex);
                const userMatch = config.connectionString.match(userRegex);
                const passwordMatch = config.connectionString.match(passwordRegex);

                if (!serverMatch || !databaseMatch || !userMatch || !passwordMatch) {
                    logger.error(`Invalid MySQL connection string format para ${typeof config}`);
                    process.exit(9);
                }

                config.host = serverMatch[1];
                config.port = parseToInt(portMatch ? portMatch[1] : '3306');
                config.name = databaseMatch[1];
                config.username = userMatch[1];
                config.password = passwordMatch[1];
                break;
            }
            default:
                logger.error(`El dialecto ${config.dialect} no es soportado`);
                process.exit(9);
        }
    }
}