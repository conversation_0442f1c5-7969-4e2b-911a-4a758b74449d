<div class="name">
	<input [disabled]="readOnly || (isInUse && !pending)" class="input" type="text"
         placeholder="{{ 'OUTPUT_FIELD_NAME' | translate }}"
         spellcheck="false"
         [(ngModel)]="output.name"
         [ngClass]="{'invalid-input': empty(output.name)}" />
</div>
<div class="type">
  <select [disabled]="readOnly || (isInUse && !pending)" class="select" name="" id="" [(ngModel)]="output.type">
    <option *ngFor="let type of getVariablesTypes()" [value]="type.value">{{ type.localized | translate }}</option>
  </select>
</div>
<div class="type">
	<select class="select" name="" id="" [(ngModel)]="output.IsMasked">
		<option *ngFor="let opt of booleanOpts()" [ngValue]="opt.value">{{opt.name | translate}}</option>
	</select>
</div>
<div class="jsonpath">
	<input [disabled]="readOnly" class="input" type="text"
         placeholder="{{ 'OUTPUT_FIELD_JSONPATH' | translate }}"
         spellcheck="false"
         [(ngModel)]="output.path"
         [ngClass]="{'invalid-input': empty(output.path)}" />
</div>
<div class="nullIfUndefined">
  <ui-switch [(ngModel)]="output.nullIfUndefined" [disabled]="readOnly"
             color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
</div>

<div class="trash">
  <div *ngIf="!readOnly" (click)="delete()" data-toggle="tooltip"
       ngbTooltip="{{ 'INPUT_FIELD_DELETE' | translate }}" placement="top" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
</div>

<div class="ok">
  <div *ngIf="!readOnly && pending" (click)="finish()" data-toggle="tooltip" ngbTooltip="{{ 'INPUT_FIELD_CONFIRM' | translate }}" placement="top" tooltipClass="tooltip-action-row-top">
    <span class="fa fa-check-circle"></span>
  </div>
</div>
