@import "_variables";

.condition {
  background-color: #fff;
  min-width: 500px;

  .variable-entity-switch {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }
  }

  .variable-entity-switch {
    margin-top: 5px;
  }

  .source, .operator, .value, .next, .normalize {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }

    .input-with-variable {
      flex-grow: 1;
    }

    select {
      height: 30px;
      font-weight: normal;
      margin: 0px 10px;
    }
  }

  .normalize {
    .info {
      margin-left: 5px;
    }
  }

  .next {
    select {
      &:first-child {
        margin-left: 0;
      }
    }
  }

  .commands {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .commands-selector-container {
      width: 100%;

      .empty {
        .alert {
          margin-bottom: 0;
        }
      }

      .commands-table {
        display: table;
        width: 100%;

        .commands-table-header, .commands-table-row {
          height: 30px;
        }

        .commands-table-header {
          display: table-row;
          font-family: $fontFamilyTitles;

          div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
          }
        }

        .commands-table-row {
          display: table-row;

          .commands-table-cell {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;

            &:first-child {
              width: 20px;
            }
          }

          &:last-child {
            .commands-table-cell {
              border-bottom-style: none;
            }
          }
        }
      }

      .button-area {
        flex-grow: 0;
        flex-shrink: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-content: flex-end;
        margin-top: 5px;
        margin-bottom: 5px;

        .action-button {
          width: 150px;
          margin-left: 0;
        }
      }
    }
  }
}
