@import '_variables';
@import '_mixins';

:host {
  display: block;
  height: 100vh;
  background: #f5f7fa;
}

.configuration-wrapper {
  padding: 2rem;
  max-height: calc(100vh - 60px);
  overflow-y: auto;
}

.configuration-container {
  max-width: 1200px;
  margin: 0 auto;

  .main-title {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e74c3c;
  }
}

.config-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .section-title {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    height: 40px;
  }

  .field-row {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;

    .label {
      min-width: 200px;
      font-weight: 500;
      color: #34495e;
      display: flex;
      align-items: center;
      height: 24px; /* Altura estándar para alinear con los switches */
    }

    .input-group {
      flex: 1;
      display: flex;
      gap: 1rem;
    }
  }

  .form-control {
    padding: 0.5rem;
    border: 1px solid #dde1e7;
    border-radius: 4px;
    width: 100%;
    transition: border-color 0.2s ease;
      &:focus {
        border-color: #e74c3c;
        outline: none;
        box-shadow: 0 0 0 2px rgba(69, 193, 149, 0.1);
      }
    }

    .password-strength {
      flex: 1;
      position: relative;

      meter {
        width: 100%;
        height: 4px;
        margin-top: 4px;

        &::-webkit-meter-bar {
          background: #eee;
        }

        &::-webkit-meter-optimum-value {
          background: #27ae60;
        }
      }
    }

    .button-row {
      display: flex;
      justify-content: flex-end;
      margin-top: 1.5rem;
      gap: 1rem;
    }
  }

  .users-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      min-height: 38px; /* Altura mínima para acomodar el botón */

      h3 {
        display: flex;
        align-items: center;
        height: 24px;
        margin: 0;
        line-height: 1;
        font-size: 18px;
        color: #2c3e50;
      }

      .btn {
        display: flex;
        align-items: center;
      }

      .new-user-btn {
        display: flex;
        align-items: center;
        gap: 8px; /* Espacio entre el icono y el texto */

        i {
          font-size: 14px;
        }
      }
    }

    .users-table-wrapper {
      overflow-x: auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .users-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;

      tr {
        height: 75px;
        min-height: 75px;
        max-height: 75px;
      }

      th, td {
        padding: 1rem;
        border-bottom: 1px solid #eee;
        white-space: nowrap;
        vertical-align: middle;
      }

      th {
        background: #f8f9fa;
        font-weight: 500;
        color: #2c3e50;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        padding: 0.5rem;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.2;
        height: auto;
      }

      td{
        height: 75px;
        min-height: 75px;
        max-height: 75px;
      }

      .center {
        text-align: center;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .user-info {
        span {
          max-width: 200px;
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .success { color: #27ae60; }
      .error { color: #e74c3c; }

      .actions {
        width: 95px;
        text-align: right;
        min-width: 95px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .btn-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .col-name { width: 15%; }
      .col-login { width: 10%; }
      .col-permission {
        width: 8%;
        text-align: center;
      }
      .col-actions {
        width: 5%;
        text-align: center;
      }
    }
  }

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &.btn-primary {
    background: #e74c3c;
    color: white;

    &:hover {
      background: darken(#e74c3c, 10%);
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;
    &:hover { background: darken(#6c757d, 10%); }
  }

  &.btn-danger {
    background: #e74c3c;
    color: white;
    &:hover { background: darken(#e74c3c, 10%); }
  }

  &.btn-icon {
    padding: 0.5rem;
    border-radius: 50%;
    background: transparent;
    color: #e74c3c;
    &:hover { background: rgba(#e74c3c, 0.1); }
  }
}

@media (max-width: 768px) {
  .configuration-wrapper {
    padding: 1rem;
  }

  .field-row {
    flex-direction: column;
    align-items: flex-start;

    .label {
      margin-bottom: 0.5rem;
    }
  }
}

.reports-section {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    height: 60px; /* Altura fija para el contenedor */

    h3 {
      display: flex;
      align-items: center;
      height: 24px;
      margin: 0;
      line-height: 1; /* Evita espacios adicionales */
      font-size: 18px;
      color: #2c3e50;
    }

    ui-switch {
      display: flex;
      align-items: center;
      height: 24px;
    }
  }

  .connection-settings {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
  }

  .reports-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .report-item {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .report-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      height: 24px; /* Altura estándar para alinear con los switches */

      i {
        color: #e74c3c;
      }
    }
  }
}

/* Estilos para estandarizar los switches */
:host ::ng-deep ui-switch {
  display: flex;
  align-items: center;
  height: 24px;
  margin: 0;
  padding: 0;
  justify-content: flex-end;
}

/* Asegurar que todos los switches tengan el mismo estilo */
:host ::ng-deep .ui-switch.checked {
  background-color: #45c195 !important;
  border-color: #45c195 !important;
}

/* Ajustes adicionales para alineación vertical */
:host ::ng-deep .ui-switch small {
  height: 18px;
  width: 18px;
  margin: 0;
}

.connection-settings {
  .field-row {
    .input-with-action {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .form-control {
        height: 38px;
      }

      .btn {
        height: 38px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0 1rem;

        i {
          font-size: 14px;
        }
      }
    }
  }
}
