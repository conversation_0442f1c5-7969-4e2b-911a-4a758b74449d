import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { TypeDefinition } from "../../../../../models/TypeDefinition";
import { OperatorDefinitions } from 'src/app/models/OperatorType';
import { Tables } from 'src/app/models/Tables';
import { DbQueryPiece } from 'src/app/models/pieces/DbQueryPiece';
import { ServerService } from 'src/app/services/server.service';
import { TableHeader } from 'src/app/models/TableHeader';
@Component({
  selector: 'app-db-query-piece',
  templateUrl: './db-query-piece.component.html',
  styleUrls: ['./db-query-piece.component.scss']
})
export class DbQueryPieceComponent extends BasePieceVM implements OnInit {
  tables: Tables[] = [];
  filteredHeaders: TableHeader[] = [];
  variableTypes = TypeDefinition;
  model: DbQueryPiece;
  variableFilter: TypeDefinition[] = [TypeDefinition.Object];
  selectedTableName: String;
  selectedColumnName: String;
  tablenameStart: string = '';

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }

  constructor(public editorService: EditorService, public modalService: ModalService, public serverService: ServerService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as DbQueryPiece;

    if (this.model.selectedTable) {
      this.selectedTableName = this.model.selectedTable.name || '';
    }

    if (this.model.selectedColumn) {
      this.selectedColumnName = this.model.selectedColumn.Name || '';
    }

    this.tables = this.editorService.getFlowTables();
    let auxHeaders = []
    if((this.tables.length > 0 && this.selectedTableName != '')) {
      let auxTable = this.tables.find(x => x.name === this.selectedTableName)
      if(auxTable !== null && typeof(auxTable) !== 'undefined') {
        auxHeaders = auxTable.headers
      }
    }
    this.filteredHeaders = this.getFilteredHeaders(auxHeaders);

    this.editorService.onTablesSet.subscribe(() => {
      this.tables = this.editorService.getFlowTables();
      if(this.tables.length === 0) {
        this.model.selectedTable = null;
        this.model.selectedColumn = null;
        this.selectedColumnName = '';
        this.selectedTableName = ''
      }

      let auxHeaders = []
      if((this.tables.length > 0 && this.selectedTableName != '')) {
        let auxTable = this.tables.find(x => x.name === this.selectedTableName)
        
        if(auxTable !== null && typeof(auxTable) !== 'undefined') {
          auxHeaders = auxTable.headers
        }
      }
      this.filteredHeaders = this.getFilteredHeaders(auxHeaders);
      
      let table = this.model.selectedTable ? this.tables.find(t => t.name === this.model.selectedTable.name) : null
      if(!table) {
        this.model.selectedTable = null;
        this.model.selectedColumn = null;
        this.selectedColumnName = '';
        this.selectedTableName = ''
      }
      else if (this.model.selectedColumn !== null){
        let column = table.headers.find(t => t.variable.Name === this.model.selectedColumn.Name)
        if(!column.index) {
          this.model.selectedColumn = null;
          this.selectedColumnName = ''
        }
      }
    })


    const mCurrentFlow = this.editorService.getCurrentFlow();
    const flowId = mCurrentFlow.master_flow_id ? mCurrentFlow.master_flow_id : mCurrentFlow.id
    this.tablenameStart = `flow_${flowId}_`;
  }

  getTableName(table_name){
    return table_name.split(this.tablenameStart)[1].replaceAll('_', ' ')
  }

  getColumnType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type == VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  getOperators() {
    var operators = [];
    if (this.model.selectedColumn != null) {
      operators = OperatorDefinitions.Operators.filter(op => {
        if (
          op.types.findIndex(operand => {
            return operand === this.model.selectedColumn.Type || operand === TypeDefinition.Any
          }) === -1) {
          return false;
        }

        return true;
      });

      if (this.model.selectedColumn.Type.toString() == "nvarchar") {
        operators.push(OperatorDefinitions.EqualOperator);
      }

      if (operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = operators[0].value;
      }

    }
    else {
      operators = null;
    }
    return operators
  }

  showOperand(): boolean {
    for (let i = 0; i < OperatorDefinitions.AllOperators.length; i++) {
      if (OperatorDefinitions.AllOperators[i].value === this.model.Operator) {
        return OperatorDefinitions.AllOperators[i].requiresOperand;
      }
    }
    return true;
  }

  setVariableOnOutput(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = null;
    }
  }

  onSelectTable(event) {
    let aux = this.tables.find(x => x.name === this.selectedTableName);
    this.model.selectedTable = new Tables(aux.name, aux.headers)
    this.filteredHeaders = this.getFilteredHeaders(this.tables.find(x => x.name === this.selectedTableName).headers);
  }

  onSelectColumn(event) {
    if (this.model.selectedTable !== undefined && this.model.selectedTable !== null) {
      let aux = this.model.selectedTable.headers.find(header => header.variable.Name === this.selectedColumnName).variable
      this.model.selectedColumn = new VariableDefinition(aux.Name, aux.Type, aux.DefaultValue, aux.Description, aux.Channel);
    }
  }

  getFilteredHeaders(headers) {
    return headers.filter(header => header.index);
  }

}
