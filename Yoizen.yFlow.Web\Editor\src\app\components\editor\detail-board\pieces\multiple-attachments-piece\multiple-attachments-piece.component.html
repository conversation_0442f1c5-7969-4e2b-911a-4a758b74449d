<div class="multiple-attachments card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sitemap"></span> {{ 'CARD_MULTIPLEATTACHMENTS' | translate }}
  </div>
  <div class="card-info">
    <p>{{ 'CARD_MULTIPLEATTACHMENT_INFO_1' | translate }}</p>
    <p>{{ 'CARD_MULTIPLEATTACHMENT_INFO_2' | translate }}</p>
  </div>
  <div class="sourcearray">
    <span class="title">{{'MULTIPLEATTACHMENTS_SOURCE_ARRAY' | translate}}:</span>
    <app-variable-selector-input [VariableData]="InputVariableData" (setVariable)="setInputVariable($event)"
      [validator]="validateVariable" [readOnly]="readOnly" [typeFilters]="variableFilter">
    </app-variable-selector-input>
  </div>
  <div class="definition">
    <!--<ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
      <ngb-tab id="tab-url" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-link"></span> URL</ng-template>
      </ngb-tab>
      <ngb-tab id="tab-variable" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-database"></span> </ng-template>
      </ngb-tab>
    </ngb-tabset>-->

    <div class="source-button">
      <span class="title">
        <span *ngIf="source === 'URL'" class="fa fa-link"></span>
        <span *ngIf="source !== 'URL'" class="fa fa-database"></span>
        {{source}}
      </span>
      <ui-switch [(ngModel)]="urlMode" (change)="onTabChange()" color="#45c195" size="small" defaultBgColor="#963ae0"
      switchColor="#ffffff"></ui-switch>
    </div>
    <div class="publicurl" *ngIf="isWhatsappChannel && source === 'URL'">
      <span class="title">{{ 'ATTACHMENTS_ARE_PUBLIC_URLS' | translate}}:</span>
      <ui-switch [(ngModel)]="model.IsPublicUrl" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="body">
    <div class="implicit-variables">
      <div class="variables-info">{{'MULTIPLEATTACHMENTS_VARIABLELIST' | translate}}</div>
      <div class="variables-table">
        <div class="variables-header">
          <div>{{'NAME' | translate}}</div>
          <div>{{'DESCRIPTION' | translate}}</div>
        </div>
        <div class="variable-row" *ngFor="let variable of customVariables">
          <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span
              class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
          <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="template">
      <span class="title">{{ 'MULTIPLEATTACHMENTS_TEMPLATE' | translate}}:</span>
      <app-input-with-variables class="input-variable-area" [validator]="getValidator()"
        [variableFinder]="searchForVariable.bind(this)" [placeholder]="''" [wideInput]="'true'"
        [customVariableList]="customVariables" [JoinCustomVariable]="true"
        [disabled]="readOnly" [spellCheck]="'false'"
        [(value)]="model.FileName"></app-input-with-variables>
    </div>
    <div class="mimetype">
        <span class="title">{{ 'ATTACHMENT_MIMETYPE' | translate}}:</span>
        <app-input-with-variables class="input-variable-area" [placeholder]="'ATTACHMENT_MIMETYPE' | translate"
          [(value)]="model.MimeType" [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)" [validator]="getValidator()"
          [wideInput]="'true'" [list]="'knowncontenttypes'"
          [disabled]="readOnly" [spellCheck]="false">
        </app-input-with-variables>
    </div>
    <div class="data">
      <span class="title"> {{ source }}: </span>
      <app-input-with-variables class="input-variable-area" [validator]="getValidator()"
        [variableFinder]="searchForVariable.bind(this)" [placeholder]="''" [wideInput]="'true'"
        [customVariableList]="customVariables" [JoinCustomVariable]="true" [disabled]="readOnly"
        [(value)]="model.Data"></app-input-with-variables>
    </div>
    <div class="twitter" *ngIf="isTwitterFlow">
      <div class="empty" *ngIf="isTwitterFlow" role="alert">
        <div class="alert alert-info">
          <span class="fa fa-exclamation-circle icon"></span>
          {{ 'ATTACHMENT_TWITTER_INFO' | translate }}
        </div>
      </div>
      <div class="text">
        <span class="title">{{ 'ATTACHMENT_TWITTER_MESSAGE' | translate }}</span>
        <app-input-with-variables [placeholder]="'VALUE' | translate"
                                  [(value)]="model.Text"
                                  [wideInput]="'true'"
                                  [isTextArea]="'true'"
                                  [minRows]="5"
                                  [spellCheck]="true"
                                  [variableFinder]="searchForVariable.bind(this)"
                                  [customVariableList]="customVariables"
                                  [JoinCustomVariable]="true"
                                  [disabled]="readOnly"></app-input-with-variables>
      </div>
    </div>
  </div>
</div>
