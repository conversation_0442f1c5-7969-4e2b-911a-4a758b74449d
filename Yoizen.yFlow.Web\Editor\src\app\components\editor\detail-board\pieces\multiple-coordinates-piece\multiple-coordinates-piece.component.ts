import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { OperandTypeDefinition, OperatorDefinitions } from '../../../../../models/OperatorType';
import { ConditionPiece } from '../../../../../models/pieces/ConditionPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { VariableConditionPiece } from "../../../../../models/pieces/VariableConditionPiece";
import { VariableDefinition } from "../../../../../models/VariableDefinition";
import { TypeDefinition } from "../../../../../models/TypeDefinition";
import { MultipleCoordinatesPiece } from "../../../../../models/pieces/MultipleCoordinatesPiece";
import { ChannelTypes } from "../../../../../models/ChannelType";
import { Concatenate } from "../../../../../models/pieces/Concatenate";

@Component({
  selector: 'app-multiple-coordinates-piece',
  templateUrl: './multiple-coordinates-piece.component.html',
  styleUrls: ['./multiple-coordinates-piece.component.scss']
})
export class MultipleCoordinatesPieceComponent extends BasePieceVM implements OnInit {
  model: MultipleCoordinatesPiece;
  variableFilter = [TypeDefinition.Array];
  storeVariableFilter = [TypeDefinition.Text, TypeDefinition.Number];
  latitudeVariableFilter = [TypeDefinition.Decimal];
  longitudeVariableFilter = [TypeDefinition.Decimal];
  isChat: boolean = false;

  get VariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }

  get StoreVariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.StoreVariableId);
  }

  get VariableLatitudeData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableLatitudeId);
  }

  get VariableLongitudeData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableLongitudeId);
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as MultipleCoordinatesPiece;
    let flow = this.editorService.getCurrentFlow();
    this.isChat = flow.channel == ChannelTypes.Chat;

    if (this.model.VariableId !== -1) {
      this.setVariable(this.VariableData);
    }
    if (this.model.StoreVariableId !== -1) {
      this.setStoreVariable(this.StoreVariableData);
    }
    if (this.model.VariableLatitudeId !== -1) {
      this.setLatitudeVariable(this.VariableLatitudeData);
    }
    if (this.model.VariableLongitudeId !== -1) {
      this.setLongitudeVariable(this.VariableLongitudeData);
    }
  }

  setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = -1;
    }
  }

  setLatitudeVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableLatitudeId = variable.Id;
    }
    else {
      this.model.VariableLatitudeId = -1;
    }
  }

  setLongitudeVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableLongitudeId = variable.Id;
    }
    else {
      this.model.VariableLongitudeId = -1;
    }
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  setStoreVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.StoreVariableId = variable.Id;
    }
    else {
      this.model.StoreVariableId = -1;
    }
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.NextBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.NextBlockId = null;
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  get customVariables(): VariableDefinition[] {
    return Concatenate.SpecialVar;
  }
}
