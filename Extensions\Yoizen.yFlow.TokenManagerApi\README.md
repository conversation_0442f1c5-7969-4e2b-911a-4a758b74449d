# Token Manager API

API para la gestión de tokens.

## Descripción

Servicio API REST que gestiona la obtención de tokens desde endpoints externos.

## Requisitos Previos

- .NET 8.0

## Endpoint

### POST /api/token

Obtiene un token desde una URL externa especificada.

#### Request Body
```json
{
    "url": "string",         // URL del endpoint para obtener el token
    "headers": {             // Headers opcionales para la petición
        "key": "value"
    },
    "body": object,          // Cuerpo opcional de la petición
    "timeout": number        // Tiempo máximo de espera en segundos
}
```

#### Response
```json
{
    "access_token": "string",
    "scope": "string", 
    "token_type": "string",
    "expires_in": 0
}
```

### GET /health

Endpoint para verificar el estado de la API.

#### Response

- **200 OK**: La API está funcionando correctamente
- **503 Service Unavailable**: La API presenta problemas

No requiere autenticación ni parámetros.

Ejemplo de uso:
```bash
curl -X GET https://url/health