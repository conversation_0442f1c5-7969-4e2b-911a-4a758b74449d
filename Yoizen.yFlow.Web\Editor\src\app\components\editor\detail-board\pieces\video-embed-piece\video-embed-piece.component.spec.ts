import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VideoEmbedPieceComponent } from './video-embed-piece.component';

describe('VideoEmbedPieceComponent', () => {
  let component: VideoEmbedPieceComponent;
  let fixture: ComponentFixture<VideoEmbedPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VideoEmbedPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VideoEmbedPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
