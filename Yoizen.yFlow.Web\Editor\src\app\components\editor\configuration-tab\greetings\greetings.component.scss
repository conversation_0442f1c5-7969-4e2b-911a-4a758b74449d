@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .implicit-variables {
    .variables-info {
      font-family: $fontFamily;
      font-size: 16px;
      margin-bottom: 5px;
      color: #767676;
    }

    .variables-table {
      display: table;
      width: 100%;

      .variables-header, .variable-row {
        height: 30px;
      }

      .variables-header {
        display: table-row;
        font-family: $fontFamilyTitles;

        div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .variable-row {
        display: table-row;

        .variable-cell {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;

          .variable-name {
            @include variable-name;
          }

          .variable-type {
            @include variable-type;
          }
        }
      }
    }
  }

  .greetings {
    margin-top: 10px;

    .greeting-info {
      font-family: $fontFamily;
      font-size: 16px;
      margin-bottom: 5px;
      color: #767676;
    }

    .greeting {
      margin-bottom: 10px;
    }
  }
}
