import {Component, OnInit} from '@angular/core';
import {BaseDynamicComponent} from '../../../../utils/component-holder/BaseDynamicComponent';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {BlockDefinition} from '../../../../../models/BlockDefinition';
import {SwitchJumpToBlockCondition, SwitchJumpToBlockPiece} from "../../../../../models/pieces/SwitchJumpToBlockPiece";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import { Entity } from 'src/app/models/cognitivity/Entity';

@Component({
  selector: 'app-switch-jump-to-block-piece',
  templateUrl: './switch-jump-to-block-piece.component.html',
  styleUrls: ['./switch-jump-to-block-piece.component.scss']
})
export class SwitchJumpToBlockPieceComponent extends BasePieceVM implements OnInit {
  model: SwitchJumpToBlockPiece;
  variableTypes = TypeDefinition;
  cognitivityEnabled: boolean = false;
  selectedEntity: Entity;

  get VariableData(): VariableDefinition {
    if (this.model.VariableId < 1000) {
      return this.editorService.findImplicitVariable(this.model.VariableName);
    }
    else {
      return this.editorService.getVariableWithId(this.model.VariableId);
    }
  }

  get CanAddConditions(): boolean {
    if (!this.model.UsesCognitiveEntities) {
      return this.model.VariableId !== -1 && this.VariableData !== null;
    }
    return this.model.EntityId !== null;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as SwitchJumpToBlockPiece;
    this.selectedEntity = null;

    if (this.model.Conditions === null) {
      this.model.Conditions = [];
    }

    this.cognitivityEnabled = this.editorService.isCognitivityEnabled();
    if (this.cognitivityEnabled) {
      let availableEntities = this.editorService.getEntities();
      this.selectedEntity = availableEntities.find(e => e.cognitiveServiceId === this.model.EntityId);
      if (this.selectedEntity === undefined) {
        this.model.EntityId = null;
        this.model.EntityName = null;
      }
    } else {
      this.model.UsesCognitiveEntities = false;
    }
  }

  setVariable(variable : VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable != null) {
      this.model.VariableId = variable.Id;
      this.model.VariableName = variable.Name;
    }
    else {
      this.model.VariableId = -1;
      this.model.VariableName = null;
      //this.model.Conditions = [];
    }
  }

  setEntity(entity: Entity) {
    if (typeof(entity) !== 'undefined' && entity != null) {
      this.model.EntityId = entity.cognitiveServiceId;
      this.model.EntityName = entity.name;
    } else {
      this.model.EntityId = null;
      this.model.EntityName = null;
    } 
  }

  onSelectBlock(i: number, blockData: BlockDefinition) {
    this.model.Conditions[i].BlockId = blockData.Id;
  }

  onDeleteBlock(i: number, blockData: BlockDefinition) {
    this.model.Conditions[i].BlockId = "-1";
  }

  deleteCondition(i: number) {
    this.model.Conditions.splice(i, 1);
  }

  addCondition() {
    this.model.Conditions.push(new SwitchJumpToBlockCondition());
  }

  clearCache() {
    this.model.VariableId = -1;
    this.model.VariableName = null;
    this.model.EntityName = null;
    this.model.EntityId = null;
    this.selectedEntity = null;
  }
}
