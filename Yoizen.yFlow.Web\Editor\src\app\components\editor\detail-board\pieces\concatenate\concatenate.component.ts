import {Component, OnInit} from '@angular/core';
import {Concatenate} from '../../../../../models/pieces/Concatenate';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {BasePieceVM} from '../BasePieceVM';
import {TypeDefinition} from '../../../../../models/TypeDefinition';
import {VariableDefinition} from '../../../../../models/VariableDefinition';

@Component({
  selector: 'app-concatenate',
  templateUrl: './concatenate.component.html',
  styleUrls: ['./concatenate.component.scss']
})
export class ConcatenateComponent extends BasePieceVM implements OnInit {
  model: Concatenate;
  variableFilter = [TypeDefinition.Array];
  outputVariableFilter = [TypeDefinition.Text];

  get customVariables(): VariableDefinition[] {
    return Concatenate.SpecialVar;
  }

  get InputVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.SourceVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  get OutputVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.DestinationVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as Concatenate;
  }


  setInputVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.model.SourceVariableId = variable.Id;
    }
    else {
      this.model.SourceVariableId = null;
    }
  }

  setOutputVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.model.DestinationVariableId = variable.Id;
    }
    else {
      this.model.DestinationVariableId = null;
    }
  }

  validateVariable(value: string) {
    return false;
  }

  validateTextArea(value: string) {
    return this.model.hasTextToAsign();
  }

  getValidator() {
    return this.validateTextArea.bind(this);
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }
}
