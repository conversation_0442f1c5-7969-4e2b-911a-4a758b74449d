const express = require('express');
class RouterBase {

    constructor() {
        this.router = express.Router();
        this.initializeRoutes();
    }

    async validate(req, res, next) {
        try {
            res.status(200).json({ success: true, message: 'Health check ok' });
        } catch (error) {
            return res.status(400).send({ success: false, message: 'Ocurrió un error durante el health check' });
        }
    }

    initializeRoutes() {
        this.router.get('/health_check', this.validate.bind(this));
    }

    getRouter() {
        return this.router;
    }
}
module.exports.RouterBase = RouterBase;