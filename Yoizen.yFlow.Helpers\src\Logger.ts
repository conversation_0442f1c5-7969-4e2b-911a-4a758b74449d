import pino, { destination } from 'pino';
import expressPino from 'pino-http';

// Configuración de destinos de log
const logDestination = {
    // Destino específico para entorno de desarrollo
    development: {
        transport: {
            target: 'pino-pretty',
            options: {
                colorize: true,
                translateTime: 'SYS:yyyy-mm-dd HH:MM:ss',
                ignore: 'pid,hostname',
                messageFormat: '{msg}',
                levelFirst: false
            }
        }
    },
    // Destino para producción
    production: {
    }
};

// Configuración optimizada para alto rendimiento
const logger = pino({
    // Nivel de log (trace, debug, info, warn, error, fatal)
    level: process.env.LOG_LEVEL || 'debug',

    // Metadatos base que se incluirán en cada log
    base: {
        env: process.env.NODE_ENV
    },

    // Formato de timestamp para mejor legibilidad
    timestamp: pino.stdTimeFunctions.isoTime,

    // Configuración para mejorar rendimiento
    formatters: {
        level: (label) => {
            return { level: label };
        },
        // Personaliza el formato de los mensajes de log
        log: (object) => {
            // Trunca campos muy largos para evitar logs gigantes
            if (object.payload && typeof object.payload === 'string' && object.payload.length > 2000) {
                object.payload = object.payload.substring(0, 2000) + '... [truncado]';
            }
            return object;
        }
    },

    // Configuración del transporte según entorno
    //...(process.env.NODE_ENV !== 'production' ? logDestination.development : {}),

    // Serializers personalizados para objetos comunes
    serializers: {
        err: pino.stdSerializers.err,
        // Versión personalizada reducida de req para menor volumen de datos
        /*req: (req: Request) => ({
            id: req.id,
            method: req.method,
            url: req.url,
            query: req.query,
            body: req.body,
            // No incluimos headers completos para reducir volumen
            userAgent: req.headers && req.headers['user-agent'],
        }),
        // Versión personalizada reducida de res para menor volumen de datos
        res: (res: Response) => ({
            statusCode: res.statusCode,
            responseTime: res.responseTime,
            body: res,
        }),*/
    },

    // Optimizaciones de rendimiento
    nestedKey: 'payload', // Los objetos anidados se ponen bajo una clave para facilitar búsquedas
    enabled: true,
    messageKey: 'msg',

});
//TODO: Terminar de adaptar el logger para que funcione con pino y no use el console.log
console.log = (...args) => logger.debug.call(logger, ...args);
console.info = (...args) => logger.info.call(logger, ...args);
console.warn = (...args) => logger.warn.call(logger, ...args);
console.error = (...args) => logger.error.call(logger, ...args);
console.debug = (...args) => logger.debug.call(logger, ...args);
console.trace = (...args) => logger.trace.call(logger, ...args);


logger.debug('Logger inicializado con éxito');

// Middleware para Express que registra automáticamente requests y responses
//@ts-ignore
const expressMiddleware = expressPino({
    logger,
    // No crear un nuevo objeto de log para cada request - usar el logger principal
    //useLevel: 'info',

    // Personalización de auto-logging
    autoLogging: {
        ignore: (req) => req.url.includes('/health') || req.url.includes('/metrics'), // No loguear health checks
    },

    // Tiempo a partir del cual se considera una respuesta lenta (en ms)
    responseTime: true,
    customLogLevel: function (req, res, err): pino.LevelWithSilent {
        if (res.statusCode >= 500 || err) return 'error';
        if (res.statusCode >= 400) return 'warn';

        return 'trace';
    },

    // Qué metadatos incluir automáticamente
    customSuccessMessage: function (req, res) {
        return `${req.method} ${req.url} completed with ${res.statusCode}`;
    },
    customErrorMessage: function (req, res, err) {
        return `${req.method} ${req.url} failed with ${res.statusCode}`;
    },

    // Optimizar qué datos se capturan para reducir overhead
    serializers: {
        req: (req) => ({
            id: req.id,
            method: req.method,
            url: req.url,
            query: req.query,
            // Solo capturar headers específicos que sean relevantes
            headers: {
                'user-agent': req.headers['user-agent'],
                'content-type': req.headers['content-type'],
                'accept': req.headers['accept'],
                'x-request-id': req.headers['x-request-id'],
            },
            body: logger.level === 'trace' ? req.raw.body : undefined, // Para capturar el request body
        }),
        res: (res) => ({
            statusCode: res.statusCode,
            responseTime: res.responseTime,
            json: res.json,
            body: res.raw.jsonPayload ?? res.raw.sendPayload, // Captura el cuerpo de la respuesta
        }),
    },
});

/**
 * Permite cambiar el nombre del servicio en los logs dinámicamente.
 * @param serviceName Nuevo nombre del servicio a utilizar en los logs
 * @returns Booleano indicando si el cambio fue exitoso
 */
const changeServiceName = (serviceName: string): boolean => {
    try {
        if (!serviceName || typeof serviceName !== 'string') {
            logger.error(`Nombre de servicio inválido: ${serviceName}`);
            return false;
        }

        // Actualiza la propiedad base en el logger
        logger.setBindings({ service: serviceName });
        //expressMiddleware.logger.setBindings({ ...expressMiddleware.logger.bindings(), service: serviceName });

        logger.info(`Nombre de servicio cambiado a: ${serviceName}`);
        return true;
    } catch (error) {
        logger.error({ error }, 'Error al cambiar el nombre del servicio');
        return false;
    }
};


/**
 * Permite cambiar el nivel de log dinámicamente en tiempo de ejecución.
 * @param level Nuevo nivel de log ('trace', 'debug', 'info', 'warn', 'error', 'fatal')
 * @returns Booleano indicando si el cambio fue exitoso
 */
const changeLogLevel = (level: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal'): boolean => {
    try {
        if (!level || !['trace', 'debug', 'info', 'warn', 'error', 'fatal'].includes(level)) {
            logger.error(`Nivel de log inválido: ${level}`);
            return false;
        }

        logger.level = level;
        //expressMiddleware.logger.level = level;
        //logger.debug(`Nivel de log cambiado a: ${level}`);
        return true;
    } catch (error) {
        logger.error({ error }, 'Error al cambiar el nivel de log');
        return false;
    }
};

// Exportar la función para poder usarla desde otros módulos
export { changeLogLevel };


// Middleware para capturar el cuerpo de las respuestas
const responseBodyLoggerMiddleware = (
    req,
    res,
    next
): void => {
    if (logger.level === 'trace') {
        const originalSend = res.send;
        const originalJson = res.json;
        res.sendPayload = undefined;
        res.jsonPayload = undefined;

        // Sobreescribir el método send para capturar el body
        res.send = function (body) {
            res.sendPayload = body;
            return originalSend.call(this, body);
        };

        // Sobreescribir el método json para capturar el body
        res.json = function (body) {
            res.jsonPayload = body;
            return originalJson.call(this, body);
        };
    }

    next();
};

// Exportar la función para poder usarla desde otros módulos
export { changeServiceName, logger, expressMiddleware, responseBodyLoggerMiddleware };
