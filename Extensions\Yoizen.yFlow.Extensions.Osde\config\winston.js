const appRoot = require('app-root-path');
const winston = require('winston');
const { combine, timestamp, printf } = winston.format;
const moment = require('moment');
require('winston-daily-rotate-file');

ignoreApiKeepAlive = winston.format((info, opts) => {
  if (info.message.indexOf('/api/keep_session') === -1){
    return info
  }else{
    return false;
  }
});


// define the custom settings for each transport (file, console)
const options = {
  file: {
    filename: `${appRoot}/logs/application-%DATE%.log`,
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '14d',
    level: 'info',  
    handleExceptions: true,
    json: true,
    colorize: true,
    exitOnError: ignoreApiKeepAlive
  },
  console: {
    level: 'debug',
    handleExceptions: true,
    json: false,
    colorize: true
  },
};

const tsFormat = () => moment().format('YYYY-MM-DD hh:mm:ss').trim();
const myFormat = printf(({ level, message, timestamp }) => {
  return `${timestamp} ${level}: ${message}`;
});

var transport = new (winston.transports.DailyRotateFile)(options.file);

transport.on('rotate', function (oldFilename, newFilename) {
  // do something fun
});

// instantiate a new Winston Logger with the settings defined above
const logger = new winston.createLogger({
  format: combine(
    timestamp({
      format: tsFormat
    }),
    myFormat,
    ignoreApiKeepAlive(),
  ),
  transports: [
    new winston.transports.Console(options.console),
    transport
  ],
  exitOnError: false, // do not exit on handled exceptions
});

// create a stream object with a 'write' function that will be used by `morgan`
logger.stream = {
  write: function (message, encoding) {
    // use the 'info' log level so the output will be picked up by both transports (file and console)
    logger.info(message);
  },
};

module.exports = logger;