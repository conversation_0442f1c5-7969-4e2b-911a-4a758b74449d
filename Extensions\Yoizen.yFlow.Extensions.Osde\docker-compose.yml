version: '3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - PROJECT_ID=os-test-chatbotcorporativo
      - GOOGLE_APPLICATION_CREDENTIALS=os-test-chatbotcorporativo-0a6eecedd93e.json
      - CLIENT_CERTIFICATE_KEY=chatbotcorporativoyoizen.osde.ar.key
      - CLIENT_CERTIFICATE_CRL=chatbotcorporativoyoizen.osde.ar.pem
      - CLIENT_CERTIFICATE_PASSPHRASE=yoizen2019
    volumes:
      # Montar los archivos de credenciales como volúmenes
      - ./credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json:/app/credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json
      - ./credential/client/chatbotcorporativoyoizen.osde.ar.key:/app/credential/client/chatbotcorporativoyoizen.osde.ar.key
      - ./credential/client/chatbotcorporativoyoizen.osde.ar.pem:/app/credential/client/chatbotcorporativoyoizen.osde.ar.pem
      # Montar el directorio de logs para persistencia (con permisos de escritura)
      - ./logs:/app/logs
    restart: unless-stopped
