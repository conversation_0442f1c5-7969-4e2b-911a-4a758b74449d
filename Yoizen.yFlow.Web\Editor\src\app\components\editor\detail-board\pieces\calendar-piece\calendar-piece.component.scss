@import "_variables";
@import "_mixins";


.condition {
  background-color: #fff;
  min-width: 500px;

  .data {
    display: flex;
    padding: 10px;
    flex-direction: row;
    align-items: center;
    border-bottom: 1px solid $cardSeparatorBorderColor;
    input {
      margin-right: 10px;
    }
    select {
      height: 30px;
      font-weight: normal;
      margin: 0 10px;
    }
  }

  .source, .next {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: start;
      flex-grow: 0;
      flex-shrink: 0;
      max-width: 200px;
    }

    select {
      height: 30px;
      font-weight: normal;
      margin: 0 10px;
    }

    .input {
      flex-grow: 1;
      flex-shrink: 1;
      height: 30px;

      &.validation-error {
        border-color: $error-color;
      }
    }
  }
}
