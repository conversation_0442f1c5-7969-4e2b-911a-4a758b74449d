import { Moment } from "moment";
import { intervalDate, intervalTime } from "../../../../../Yoizen.yFlow.Web/models/historical/interval";
import { Model } from "sequelize";

export enum DetailedInfoTypes {
    /**
     * Información de respuestass por defecto
     */
    DefaultAnswers = 1,

    /**
     * Información de eventos estadísticos
     */
    StatisticEvent = 2
};

export class DetailedBase extends Model {
    declare datetime: Moment;
    declare interval: any;
    intervalDate: any;
    declare date: Moment;

    initBase(datetime: Moment) {
        this.datetime = datetime;
        this.date = datetime;
        this.interval = intervalTime(datetime);
        this.intervalDate = typeof (intervalDate(datetime).replaceAll) === "undefined" ?
            intervalDate(datetime).replace(/-/g, '') :
            intervalDate(datetime).replaceAll('-', '');
    }

    type(): DetailedInfoTypes {
        return null;
    }
}