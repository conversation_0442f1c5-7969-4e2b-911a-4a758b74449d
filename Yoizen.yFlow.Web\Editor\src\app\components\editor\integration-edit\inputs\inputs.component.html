<div class="name">
	<input class="input" type="text"
         placeholder="{{'INPUT_FIELD_NAME' | translate}}"
         spellcheck="false"
         [ngModel]="value.Name"
         (ngModelChange)="value.Name = $event"
         [ngClass]="{'invalid-input': isInvalidValidName(value.Name, value.Id)}" [disabled]="disabled || isInUse" />
</div>
<div class="type">
	<select class="select" name="" id="" [(ngModel)]="value.Type" [disabled]="disabled || isInUse">
		<option *ngFor="let type of getVariablesTypes()" [value]="type.value">{{type.localized | translate}}</option>
	</select>
</div>
<div class="masked">
	<select class="select" name="" id="" [(ngModel)]="value.IsMasked" [disabled]="disabled || isInUse">
		<option *ngFor="let opt of booleanOpts()" [ngValue]="opt.value">{{opt.name | translate}}</option>
	</select>
</div>
<div class="trash" *ngIf="!disabled">
	<div (click)="delete()" data-toggle="tooltip" ngbTooltip="{{ 'INPUT_FIELD_DELETE' | translate }}" placement="top" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
</div>
<div class="ok" *ngIf="!disabled && pending">
  <div (click)="finish()" data-toggle="tooltip" ngbTooltip="{{ 'INPUT_FIELD_CONFIRM' | translate }}" placement="top" tooltipClass="tooltip-action-row-top">
    <span class="fa fa-check-circle"></span>
  </div>
</div>
