import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PersistentMenuOptionListComponent } from './persistent-menu-option-list.component';

describe('PersistentMenuOptionListComponent', () => {
  let component: PersistentMenuOptionListComponent;
  let fixture: ComponentFixture<PersistentMenuOptionListComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PersistentMenuOptionListComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PersistentMenuOptionListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
