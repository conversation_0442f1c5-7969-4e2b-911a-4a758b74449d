﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace YFlowUtils.Helpers
{
	public static class RequestExtensions
	{
        #region Fields

        private static string[] headersToIgnore = new string[] { "Proxy-Connection", "X-Hub-Signature", "hash", "Accept", "Connection", "Host", "Max-Forwards", "X-LiveUpgrade", "DISGUISED-HOST", "X-ARR-LOG-ID", "X-SITE-DEPLOYMENT-ID", "X-Original-URL", "X-Forwarded-For", "X-ARR-SSL", "X-WAWS-Unencoded-URL", "WAS-DEFAULT-HOSTNAME", "X-Forwarded-Proto" };

        #endregion

        public static string ConvertHeadersToString(this Microsoft.AspNetCore.Http.HttpRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            Microsoft.AspNetCore.Http.IHeaderDictionary headers = request.Headers;

            var sb = new System.Text.StringBuilder();
            foreach (var header in headers.Where(h => !headersToIgnore.Contains(h.Key, StringComparer.InvariantCultureIgnoreCase)))
            {
                sb.Append(header.Key);
                sb.Append(": ");

                int index = 0;
                foreach (var value in header.Value)
                {
                    if (index > 0)
                        sb.Append(", ");
                    sb.Append(value);

                    index++;
                }
                sb.AppendLine();
            }

            if (sb.Length > 0)
                sb.Insert(0, Environment.NewLine);

            return sb.ToString();
        }

        public static string GetDisplayUrl(this Microsoft.AspNetCore.Http.HttpRequest request)
		{
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            var url = Microsoft.AspNetCore.Http.Extensions.UriHelper.GetDisplayUrl(request);

            return url;
        }
    }
}
