﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace YFlowUtils.Models
{
	public class StatusResponse
	{
		public int Success { get; set; }
		public string Message { get; set; }
		public StatusDataResponse Data { get; set; }

		public StatusResponse()
		{
			this.Data = new StatusDataResponse();
		}
	}

	public class StatusDataResponse
	{
		public string Interaction { get; set; }
		public string URL { get; set; }
	}
}
