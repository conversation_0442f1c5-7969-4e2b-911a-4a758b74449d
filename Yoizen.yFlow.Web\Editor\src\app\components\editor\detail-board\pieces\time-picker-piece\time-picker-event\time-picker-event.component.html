<div class="event">
  <div class="title">{{'TIME_PICKER_EVENT_TITLE_INFO' | translate}}</div>
  <div class="options">
    <div class="option">
      <span class="title">{{'TIME_PICKER_EVENT_TITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'TIME_PICKER_EVENT_TITLE' | translate"
        [(value)]="event.title"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="event.isTitleValid.bind(event)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'TIME_PICKER_EVENT_IMAGE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'TIME_PICKER_EVENT_IMAGE' | translate"
        [(value)]="event.image"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="event.isImageValid.bind(event)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'TIME_PICKER_EVENT_USE_LOCATION' | translate}}:</span>
      <ui-switch [(ngModel)]="event.useLocation" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="location" *ngIf="event.useLocation">
      <div class="title">{{'TIME_PICKER_EVENT_LOCATION_TITLE_INFO' | translate}}</div>
      <div class="options">
        <div class="option">
          <span class="title">{{ 'COORDINATES_LATITUDE' | translate }}:</span>
          <app-input-with-variables
            class="input"
            [placeholder]="'COORDINATES_LATITUDE' | translate"
            [(value)]="event.location.latitude"
            [validator]="event.location.isLatitudeValid.bind(event.location, editorService)"
            [wideInput]="true"
            [isTextArea]="false"
            [isHtml]="false"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
        <div class="option">
          <span class="title">{{ 'COORDINATES_LONGITUDE' | translate }}:</span>
          <app-input-with-variables
            class="input"
            [placeholder]="'COORDINATES_LONGITUDE' | translate"
            [(value)]="event.location.longitude"
            [validator]="event.location.isLongitudeValid.bind(event.location, editorService)"
            [wideInput]="true"
            [isTextArea]="false"
            [isHtml]="false"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
        <div class="option">
          <span class="title">{{ 'TIME_PICKER_EVENT_LOCATION_RADIUS' | translate }}:</span>
          <app-input-with-variables
            class="input"
            [placeholder]="'TIME_PICKER_EVENT_LOCATION_RADIUS' | translate"
            [(value)]="event.location.radius"
            [validator]="event.location.isRadiusValid.bind(event.location, editorService)"
            [wideInput]="true"
            [isTextArea]="false"
            [isHtml]="false"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
        <div class="option">
          <span class="title">{{ 'TIME_PICKER_EVENT_LOCATION_TITLE' | translate }}:</span>
          <app-input-with-variables
            class="input"
            [placeholder]="'TIME_PICKER_EVENT_LOCATION_TITLE' | translate"
            [(value)]="event.location.title"
            [validator]="event.location.isTitleValid.bind(event.location, editorService)"
            [wideInput]="true"
            [isTextArea]="false"
            [isHtml]="false"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
      </div>
    </div>
    <div class="option">
      <span class="title">{{'TIME_PICKER_EVENT_USE_USER_TIMEZONE' | translate}}:</span>
      <ui-switch [(ngModel)]="event.useUserTimezoneOffset" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="option" *ngIf="!event.useUserTimezoneOffset">
      <span class="title">{{'TIME_PICKER_EVENT_TIMEZONEOFFSET' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'TIME_PICKER_EVENT_TIMEZONEOFFSET' | translate"
        [(value)]="event.timezoneOffset"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="event.isTimezoneOffsetValid.bind(event)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'TIME_PICKER_EVENT_SOURCE_VARIABLE' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="sourceVariableData"
        (setVariable)="setSourceVariable($event)"
        [validator]="event.isSourceVariableValid.bind(event, editorService)"
        [canSelectConstants]="false"
        [readOnly]="readOnly"
        [typeFilters]="sourceVariableFilter">
      </app-variable-selector-input>
    </div>
    <div class="option with-info" *ngIf="event.sourceVariableId !== null && event.sourceVariableId !== -1">
      <span class="title">{{'TIME_PICKER_EVENT_TIMEITEM_STARTTIME_PROP' | translate}}:</span>
      <input type="text"
             class="input"
             [(ngModel)]="event.startTimeProp"
             spellcheck="false"
             [placeholder]="'TIME_PICKER_EVENT_TIMEITEM_STARTTIME_PROP' | translate"
             [ngClass]="{'invalid-input': !event.isStartTimePropValid(editorService)}"
             [disabled]="readOnly" />
      <div class="info" ngbTooltip="{{ 'TIME_PICKER_EVENT_TIMEITEM_STARTTIME_TIP' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="option with-info" *ngIf="event.sourceVariableId !== null && event.sourceVariableId !== -1">
      <span class="title">{{'TIME_PICKER_EVENT_TIMEITEM_DURATION_PROP' | translate}}:</span>
      <input type="text"
             class="input"
             [(ngModel)]="event.durationProp"
             spellcheck="false"
             [placeholder]="'TIME_PICKER_EVENT_TIMEITEM_DURATION_PROP' | translate"
             [ngClass]="{'invalid-input': !event.isDurationPropValid(editorService)}"
             [disabled]="readOnly" />
      <div class="info" ngbTooltip="{{ 'TIME_PICKER_EVENT_TIMEITEM_DURATION_TIP' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
  </div>
</div>
