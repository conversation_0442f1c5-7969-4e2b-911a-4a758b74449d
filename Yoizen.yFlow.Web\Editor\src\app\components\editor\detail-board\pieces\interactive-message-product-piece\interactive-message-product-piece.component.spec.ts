import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InteractiveMessageProductPieceComponent } from './interactive-message-product-piece.component';

describe('InteractiveMessageProductPieceComponent', () => {
  let component: InteractiveMessageProductPieceComponent;
  let fixture: ComponentFixture<InteractiveMessageProductPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InteractiveMessageProductPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InteractiveMessageProductPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
