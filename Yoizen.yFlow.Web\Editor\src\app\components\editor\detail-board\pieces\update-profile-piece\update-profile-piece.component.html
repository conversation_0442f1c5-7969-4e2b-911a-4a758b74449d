<div class="update-profile card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-user-edit"></span> {{ 'CARD_UPDATEPROFILE_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_UPDATEPROFILE_INFO' | translate }}
  </div>
  <div class="alert alert-warning" *ngIf="model.Channel === channelTypes.Generic">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'UPDATECASE_CLOSECASE_WARNING_GENERIC_CHANEL' | translate }}
  </div>
  <div class="option">
    <span class="title">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateBusinessData" [disabled]="readOnly">
      <option [ngValue]="updateProfileActionTypes.DoNotUpdate">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_DONOTUPDATE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.UpdateAndMerge">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_UPDATEANDMERGE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.UpdateAndReplace">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_UPDATEANDREPLACE' | translate}}</option>
    </select>
  </div>
  <div class="option" *ngIf="model.updateBusinessData !== updateProfileActionTypes.DoNotUpdate">
    <span class="title">{{'UPDATEPROFILE_BUSINESSDATA_TITLE' | translate}}:</span>
    <app-input-with-variables [placeholder]="'UPDATEPROFILE_BUSINESSDATA_TITLE' | translate"
      [(value)]="model.businessData" 
      [isTextArea]="false" 
      [wideInput]="true" 
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'UPDATEPROFILE_UPDATEVIP_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateVip" [disabled]="readOnly">
      <option [ngValue]="updateProfileActionTypes.DoNotUpdate">{{'UPDATEPROFILE_UPDATEVIP_DONOTUPDATE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.Yes">{{'UPDATEPROFILE_UPDATEVIP_YES' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.No">{{'UPDATEPROFILE_UPDATEVIP_NO' | translate}}</option>
    </select>
  </div>
  <div class="option">
    <span class="title">{{'UPDATEPROFILE_UPDATEDONOTCALL_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateDoNotCall" [disabled]="readOnly">
      <option [ngValue]="updateProfileActionTypes.DoNotUpdate">{{'UPDATEPROFILE_UPDATEDONOTCALL_DONOTUPDATE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.Yes">{{'UPDATEPROFILE_UPDATEDONOTCALL_YES' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.No">{{'UPDATEPROFILE_UPDATEDONOTCALL_NO' | translate}}</option>
    </select>
  </div>
  <div class="option">
    <span class="title">{{'UPDATEPROFILE_UPDATEBASE_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateBaseProfile" [disabled]="readOnly">
      <option [ngValue]="updateProfileActionTypes.DoNotUpdate">{{'UPDATEPROFILE_UPDATEBASE_DONOTUPDATE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.Yes">{{'UPDATEPROFILE_UPDATEBASE_DOUPDATE' | translate}}</option>
    </select>
  </div>
  <div *ngIf="model.updateBaseProfile == updateProfileActionTypes.Yes">
    <div class="option">
      <span class="title">{{'UPDATEPROFILE_UPDATEBASE_NAME' | translate}}:</span>
      <app-input-with-variables [placeholder]="'UPDATEPROFILE_UPDATEBASE_NAME' | translate"
        [(value)]="model.baseProfileData.Name" 
        [isTextArea]="false" 
        [wideInput]="true" 
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'UPDATEPROFILE_UPDATEBASE_MAIL' | translate}}:</span>
      <app-input-with-variables [placeholder]="'UPDATEPROFILE_UPDATEBASE_MAIL' | translate"
        [(value)]="model.baseProfileData.Mail" 
        [isTextArea]="false" 
        [wideInput]="true" 
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'UPDATEPROFILE_UPDATEBASE_PHONE' | translate}}:</span>
      <app-input-with-variables [placeholder]="'UPDATEPROFILE_UPDATEBASE_PHONE' | translate"
        [(value)]="model.baseProfileData.Phone" 
        [isTextArea]="false" 
        [wideInput]="true" 
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
  </div>
  <div class="option">
    <span class="title">{{'UPDATEPROFILE_UPDATEEXTENDED_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateExtendedProfile" [disabled]="readOnly">
      <option [ngValue]="updateProfileActionTypes.DoNotUpdate">{{'UPDATEPROFILE_UPDATEEXTENDED_DONOTUPDATE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.UpdateAndMerge">{{'UPDATEPROFILE_UPDATEEXTENDED_UPDATEANDMERGE' | translate}}</option>
      <option [ngValue]="updateProfileActionTypes.UpdateAndReplace">{{'UPDATEPROFILE_UPDATEEXTENDED_UPDATEANDREPLACE' | translate}}</option>
    </select>
  </div>
  <div class="extended-profile" *ngIf="model.updateExtendedProfile !== updateProfileActionTypes.DoNotUpdate">
    <div class="title">
      {{ 'UPDATEPROFILE_UPDATEEXTENDED_TITLE' | translate }}
    </div>
    <div class="extended-profile-table">
      <div class="header">
        <div>{{ 'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate }}</div>
        <div>{{ 'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="extended-profile-row" *ngFor="let extendedProfileData of model.extendedProfileData let i = index">
        <div class="extended-profile-value">
          <app-input-with-variables [placeholder]="'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate"
            [(value)]="extendedProfileData.Key" [disabled]="readOnly"
            [ngClass]="{'validation-error': !extendedProfileData.isValidKey()}"></app-input-with-variables>
        </div>
        <div class="extended-profile-value">
          <app-input-with-variables [placeholder]="'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate"
            [(value)]="extendedProfileData.Value" [disabled]="readOnly"
            [ngClass]="{'validation-error': !extendedProfileData.isValidValue()}"></app-input-with-variables>
        </div>
        <div class="trash" *ngIf="!readOnly">
          <div (click)="deleteExtendedProfileData(i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
            ngbTooltip="{{ 'CARD_UPDATECASE_BUSSINESS_REMOVE' | translate }}" placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" role="alert" *ngIf="model.extendedProfileData.length == 0">
      <div class="alert alert-info">
        {{ 'CARD_UPDATEPROFILEPIECE_INFO_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="addExtendedProfileData()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'UPDATEPROFILE_UPDATEEXTENDED_ADD' | translate }}
    </div>
  </div>
</div>