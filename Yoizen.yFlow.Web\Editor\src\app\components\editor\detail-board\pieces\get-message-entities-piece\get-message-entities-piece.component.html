<div class="message-entities card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-database"></span> {{ 'PIECE_GETMESSAGEENTITIES_TITLE' | translate }}
  </div>

  <div class="entities">
    <span class="title">{{ 'PIECE_GETMESSAGEENTITIES_ENTITIES' | translate }}:</span>
    <app-tagged-input-entity [selectedEntities]="model.Entities"
                      (onElementAdded)="ElementAdded($event)"
                      (onElementDeleted)="ElementDeleted($event)"></app-tagged-input-entity>
  </div>
  <div class="cognitivity">
    <span class="title">{{'EVALUATE_COGNITIVITY_USE_MESSAGE_TEXT' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UseMessageText" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="message" *ngIf="!model.UseMessageText">
    <app-input-with-variables [placeholder]="'VALUE' | translate"
                            [(value)]="model.Message"
                            [wideInput]="true"
                            [isTextArea]="true"
                            [minRows]="3"
                            [spellCheck]="true"
                            [disabled]="readOnly"></app-input-with-variables>
  </div>
  
</div>