import { isEnvironmentVariableValid, parseToInt } from "./Helpers";
import { logger } from "./Logger";

export class DbConfigBase {
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    dialect: string;
    dbtimeout: number;
    dbcanceltimeout: number;
    ssl: boolean;
    enable: boolean;
    connectionString: string;
    poolMaxSize: number;

    parseConnectionString(): void {
        if (isEnvironmentVariableValid(this.connectionString)) {
            switch (this.dialect) {
                case 'mssql': {
                    const serverRegex = /Server=([^,;]+)(?:,(\d+))?/i;
                    const databaseRegex = /Database=([^;]+)/i;
                    const userRegex = /User Id=([^;]+)/i;
                    const passwordRegex = /Password=([^;]+)/i;

                    const serverMatch = this.connectionString.match(serverRegex);
                    const databaseMatch = this.connectionString.match(databaseRegex);
                    const userMatch = this.connectionString.match(userRegex);
                    const passwordMatch = this.connectionString.match(passwordRegex);

                    if (!serverMatch || !databaseMatch || !userMatch || !passwordMatch) {
                        logger.error(`Invalid MSSQL connection string format for ${this.constructor.name}`);
                        process.exit(9);
                    }

                    this.host = serverMatch[1];
                    this.port = parseToInt(serverMatch[2] || '1433');
                    this.name = databaseMatch[1];
                    this.username = userMatch[1];
                    this.password = passwordMatch[1];
                    break;
                }
                case 'mysql': {
                    const serverRegex = /Server=([^;]+)/i;
                    const portRegex = /Port=(\d+)/i;
                    const databaseRegex = /Database=([^;]+)/i;
                    const userRegex = /Uid=([^;]+)/i;
                    const passwordRegex = /Pwd=([^;]+)/i;

                    const serverMatch = this.connectionString.match(serverRegex);
                    const portMatch = this.connectionString.match(portRegex);
                    const databaseMatch = this.connectionString.match(databaseRegex);
                    const userMatch = this.connectionString.match(userRegex);
                    const passwordMatch = this.connectionString.match(passwordRegex);

                    if (!serverMatch || !databaseMatch || !userMatch || !passwordMatch) {
                        logger.error(`Invalid MySQL connection string format para ${this.constructor.name}`);
                        process.exit(9);
                    }

                    this.host = serverMatch[1];
                    this.port = parseToInt(portMatch ? portMatch[1] : '3306');
                    this.name = databaseMatch[1];
                    this.username = userMatch[1];
                    this.password = passwordMatch[1];
                    break;
                }
                case 'redis': {
                    const regex = /^(.*?)(?::(\d+))?,password=(.*?),/;
                    const matches = this.connectionString.match(regex);

                    if (matches) {
                        this.host = matches[1];
                        this.port = parseToInt(matches[2] || '6380');
                        this.password = matches[3];
                    } else {
                        logger.error(`Invalid Redis connection string format para ${this.constructor.name}`);
                        process.exit(9);
                    }
                    break;
                }
                default:
                    logger.error(`El dialecto ${this.dialect} no es soportado`);
                    process.exit(9);
            }
        }
    }
}