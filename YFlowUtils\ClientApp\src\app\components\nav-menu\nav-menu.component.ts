import {Component, Inject, OnInit} from '@angular/core';
import * as CryptoJS from 'crypto-js';
import { Router, ActivatedRoute } from '@angular/router';
import * as url from 'url'
import {DOCUMENT} from '@angular/common';

@Component({
  selector: 'app-nav-menu',
  templateUrl: './nav-menu.component.html',
  styleUrls: ['./nav-menu.component.scss']
})
export class NavMenuComponent implements OnInit {
  isExpanded = false;

  Company = 'yFlow';

  constructor(@Inject(DOCUMENT) private _document: HTMLDocument,
              private route: ActivatedRoute) {
  }

  ngOnInit() {
    this.route.queryParams
      .subscribe(params => {
        try {
          let decryptedBytes = CryptoJS.AES.decrypt(params.pageToken, "abcdefgabcdefg12");
          let plaintext = decryptedBytes.toString(CryptoJS.enc.Utf8);
          let jsonData = JSON.parse(plaintext);
          this.Company = jsonData.Company || this.Company;
          this._document.getElementById('imgBrand').setAttribute('src', jsonData.IcoUrl);
        }
        catch (e) {
        }
      });
  }

  collapse() {
    this.isExpanded = false;
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
  }
}
