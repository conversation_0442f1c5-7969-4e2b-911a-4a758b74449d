﻿using System;
using System.Linq;
using System.Net;
using System.Text;
using System.IO;
using System.Security.Cryptography;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace YFlowUtils.Utils
{
	public class Executor
	{

		public async Task<string> newInteraction(JObject tokenData, JObject fileData, InteractionType type)
		{
			try
			{
				const string URL = "https://callback.ysocial.net/api/interactions/new";

				var allData = new JObject();
				allData["socialServiceType"] = tokenData["sst"];
				allData["accountId"] = fileData["account"];
				allData["socialUserId"] = tokenData["u"];
				allData["timestamp"] = tokenData["timestamp"];
				allData["messageType"] = type.ToString();
				switch (type)
				{
					case InteractionType.digitalSignature:
						allData["imageUrl"] = fileData["msg"]["attach"]["url"];
						allData["mimeType"] = fileData["msg"]["attach"]["mimeType"];
						allData["fileName"] = fileData["msg"]["attach"]["name"];
						allData["imageBase64"] = fileData["msg"]["attach"]["data"];
						allData["blockDest"] = tokenData["ps"];
						break;
					case InteractionType.accountLinking:
						allData["user"] = new JObject();
						allData["user"]["data"] = fileData["user"]["data"];
						break;
				}

				HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
				request.Method = "POST";
				request.Accept = "application/json";
				request.ContentType = "application/json; charset=utf-8";

				string json = allData.ToString();
				byte[] bytes = Encoding.UTF8.GetBytes(json);
				request.ContentLength = bytes.Length;

				request.Headers.Add("X-Hub-Signature", $"sha1={Encode(json, "d4061499a5eb796b73f4569c0df91988")}");

				using (Stream stream = await request.GetRequestStreamAsync())
				{
					await stream.WriteAsync(bytes, 0, bytes.Length);
				}
				HttpWebResponse response = (HttpWebResponse) await request.GetResponseAsync();
				var responseString = await new StreamReader(response.GetResponseStream()).ReadToEndAsync();
				return responseString;
			}
			catch (WebException ex)
			{
				if (ex.Response != null)
				{
					try
					{
						using (var sr = new StreamReader(ex.Response.GetResponseStream()))
						{
							string json = sr.ReadToEnd();
							return json;
						}
					}
					catch
					{
						return ex.Message;
					}
				}
				return ex.Message;
				throw;
			}
			catch (Exception ex)
			{
				return ex.Message;
				throw;
			}
		}

		public string UploadFile(JObject data, string account)
		{
			try
			{
				var url = $"https://ysocialfiles.azurewebsites.net/api/upload/whatsapp/{account}";
				//var url = $"https://dev.ysocial.net/WebApiCloudFiles/api/upload/whatsapp/{account}";
				HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
				request.Method = "POST";
				request.Accept = "application/json";
				request.ContentType = "application/json; charset=utf-8";

				string json = data.ToString();
				byte[] bytes = Encoding.UTF8.GetBytes(json);
				request.ContentLength = bytes.Length;

				request.Headers.Add("X-Hub-Signature", $"sha1={Encode(json, "b29780205f1d02e838cf75ff19f64c76")}");

				using (Stream stream = request.GetRequestStream())
				{
					stream.Write(bytes, 0, bytes.Length);
				}

				string fileUrl = null;
				using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
				{
					try
					{
						using (StreamReader sr = new StreamReader(response.GetResponseStream()))
						{
							string jsonResponse = sr.ReadToEnd();
							var jResponse = JObject.Parse(jsonResponse);
							fileUrl = jResponse["url"].ToString();
						}
					}
					catch { }
				}

				return fileUrl;
			}
			catch (WebException ex)
			{
				if (ex.Response != null)
				{
					try
					{
						using (var sr = new StreamReader(ex.Response.GetResponseStream()))
						{
							string json = sr.ReadToEnd();
						}
					}
					catch
					{
						return ex.Message;
					}
				}
				else
				{
					return ex.Message; ;
				}

				throw;
			}
			catch (Exception ex)
			{
				return ex.Message;
				throw;
			}
		}

		public static byte[] StringToByteArray(string hex)
		{
			return Enumerable.Range(0, hex.Length)
							 .Where(x => x % 2 == 0)
							 .Select(x => Convert.ToByte(hex.Substring(x, 2), 16))
							 .ToArray();
		}

		private static string Encode(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (HMACSHA1 myhmacsha1 = new HMACSHA1(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				return myhmacsha1.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}
		private static byte[] HashHMAC(byte[] key, byte[] message)
		{
			var hash = new HMACSHA256(key);
			return hash.ComputeHash(message);
		}
	}

	public enum InteractionType
	{
		digitalSignature = 0,
		accountLinking = 1,
		accountUnlinking = 2,
	}


}
