<div class="invoke card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-file-import"></span> {{ 'CARD_CALLBLOCKASPROCEDURE_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CALLBLOCKASPROCEDURE_INFO' | translate }}
  </div>
  <div class="next">
    <span class="title">{{ 'CALLBLOCKASPROCEDURE_BLOCK' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.BlockId" (onSelectNewBlock)="onSelectBlock($event)"
                      [readOnly]="readOnly"
                      (onDeleteBlock)="onDeleteBlock($event)" [isInvalid]="!model.isBlockValid(editorService)"></app-block-picker>
  </div>
</div>
