{"name": "yoizen.social.extensions.osde", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www --inspect=5858", "start:docker": "node app.js", "dev": "nodemon app.js", "test": "node test/test-api.js"}, "dependencies": {"app-root-path": "^3.0.0", "axios": "^0.19.0", "compression": "^1.7.4", "cookie-parser": "~1.4.4", "debug": "~2.6.9", "dialogflow": "^0.14.1", "dotenv": "^16.4.7", "express": "~4.16.1", "helmet": "^3.21.2", "http-errors": "~1.6.3", "moment": "^2.24.0", "morgan": "~1.9.1", "pug": "2.0.0-beta11", "uuid": "^3.3.3", "winston": "^3.2.1", "winston-daily-rotate-file": "^4.3.0"}}