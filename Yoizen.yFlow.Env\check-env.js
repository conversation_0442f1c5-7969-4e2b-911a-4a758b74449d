/**
 * Script para verificar la configuración de entorno cargada
 */
const os = require('os');
const fs = require('fs');
const path = require('path');

// Identificar al usuario actual
const username = os.userInfo().username;
console.log(`Usuario detectado: ${username}`);

// Verificar si existe un archivo de configuración personalizado
const personalEnvPath = path.join(__dirname, `.env.${username}`);
if (fs.existsSync(personalEnvPath)) {
  console.log(`Archivo de configuración personalizado encontrado: ${personalEnvPath}`);
  
  // Mostrar contenido del archivo
  try {
    const envContent = fs.readFileSync(personalEnvPath, 'utf8');
    const envLines = envContent.split('\n');
    console.log('\nContenido del archivo:');
    console.log('---------------------');
    envLines.forEach(line => {
      // Ocultar contraseñas y tokens por seguridad
      if (line.match(/password|token|key/i)) {
        const parts = line.split('=');
        if (parts.length > 1) {
          console.log(`${parts[0]}=******`);
        } else {
          console.log(line);
        }
      } else {
        console.log(line);
      }
    });
    console.log('---------------------');
  } catch (error) {
    console.error(`Error al leer el archivo: ${error.message}`);
  }
} else {
  console.log(`No se encontró un archivo de configuración personalizado para ${username}`);
}

// Verificar la carga de variables desde el proceso
console.log('\nVariables de entorno cargadas:');
console.log('---------------------------');
const requiredVars = [
  'NODE_ENV',
  'PORT',
  'dbname',
  'dbusername',
  'dbhost',
  'dbport',
  'dbdialect',
  'client',
  'multipleCores',
  'hostInsideIIS',
  'useSocketIo',
  'executorUrl',
  'storageYFlowPath',
  'useAzureBlobStorage',
  'trackAdminSessions'
];

requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value !== undefined) {
    console.log(`${varName}: ${value}`);
  } else {
    console.log(`${varName}: NO DEFINIDA`);
  }
});
