<div class="tag card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-tag"></span> {{ 'CARD_TAG_TITLE' | translate }}
  </div>
  <div class="contents">
    <app-input-with-variables
      [placeholder]="'TAG' | translate"
      [(value)]="model.Key"
      [isTextArea]="false"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>

  <div class="option">
    <span class="title">{{'IMPORTANT_TAG' | translate}}:</span>
    <ui-switch [(ngModel)]="model.importantTag" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff" (valueChange)="onValueChange($event)"></ui-switch>
  </div>

</div>
