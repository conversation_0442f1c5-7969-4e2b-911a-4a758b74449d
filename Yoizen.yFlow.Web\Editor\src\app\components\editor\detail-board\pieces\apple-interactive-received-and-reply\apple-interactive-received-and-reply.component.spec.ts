import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AppleInteractiveReceivedAndReplyComponent } from './apple-interactive-received-and-reply.component';

describe('AppleInteractiveReceivedAndReplyComponent', () => {
  let component: AppleInteractiveReceivedAndReplyComponent;
  let fixture: ComponentFixture<AppleInteractiveReceivedAndReplyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AppleInteractiveReceivedAndReplyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppleInteractiveReceivedAndReplyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
