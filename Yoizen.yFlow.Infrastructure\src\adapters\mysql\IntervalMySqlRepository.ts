import { IIntervalPort } from "../../ports/IIntervalPort";
import { dbIntervals } from '../../../../Yoizen.yFlow.Helpers/src/ConfigDbInterval';
import { DailyBase } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/daily/DailyBase";
import { DataTypes, Sequelize, Options as SequelizeOptions, Dialect as SequelizeDialect, QueryTypes } from "sequelize";
import { config as configAux } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";
import { StatisticsConfigService } from "../../../../Yoizen.yFlow.Helpers/src/StatisticsConfigService";


export class IntervalMySqlRepository implements IIntervalPort {
    private sequelize: Sequelize;
    constructor() {
        const config: SequelizeOptions = {
            dialect: dbIntervals.dialect as SequelizeDialect,
            host: dbIntervals.host,
            port: dbIntervals.port,
            database: dbIntervals.name,
            username: dbIntervals.username,
            password: dbIntervals.password,
            ssl: dbIntervals.ssl,
            logging: configAux.isDebug ? function (str) {
                logger.trace(str);
            } : false,
            define: {
                underscored: true
            },
            dialectOptions: {
                connectTimeout: dbIntervals.dbtimeout,
                ssl: {
                    require: dbIntervals.ssl,
                }
            },
        };

        if (!dbIntervals.ssl) {
            // @ts-ignore: Lo quito ya que no encontré otra forma elegante de quitar el warning
            delete config.dialectOptions.ssl;
        }

        if (typeof (dbIntervals.poolMaxSize) === 'number') {
            config.pool = {
                max: dbIntervals.poolMaxSize,
                min: 0,
                acquire: 30000,
                idle: 10000
            };
        }

        this.sequelize = new Sequelize(config);
    }

    async saveInDatabase(interval: DailyBase): Promise<any> {
        try {
            // Check if consolidated statistics are enabled
            if (!StatisticsConfigService.isConsolidatedEnabled()) {
                logger.debug('Consolidated statistics are disabled, skipping save operation');
                return {
                    failed: false,
                    message: 'Consolidated statistics disabled - save operation skipped'
                };
            }

            const query = 'INSERT INTO ' + interval.getType() + '_' + interval.intervalDate + '_' + interval.interval.toString().padStart(4, '0') + ' (`' + interval.getColumns().join('`,`') + '`) VALUES (' + '?,'.repeat(interval.getTotalsColumns()).substring(0, 2 * interval.getTotalsColumns() - 1) + ')';
            await this.sequelize.query(query, {
                replacements: interval.getValues(),
                type: QueryTypes.INSERT
            });

            return {
                failed: false,
                message: 'Saved in database'
            };
        } catch (error) {
            logger.fatal({ error: error }, `CRITICAL - Error al guardar estadísticas ${JSON.stringify(interval)} error`);
            return {
                failed: true,
                message: error
            };
        }
    }

    generateTableName(interval: DailyBase) {
        return interval.getType() + '_' + interval.intervalDate + '_' + interval.interval.toString().padStart(4, '0');
    }

    async executeStoredProcedure(procedureName: string, params: string[]): Promise<any> {
        try {
            let result = await this.sequelize.query(`CALL ${procedureName}(?,?)`, {
                replacements: params
            });

            return result;
        } catch (error) {
            throw error;
        }
    }

    async dropTable(interval: DailyBase): Promise<any> {
        try {
            let result = await this.sequelize.query(`DROP TABLE IF EXISTS ${this.generateTableName(interval)}`)

            return result;
        } catch (error) {
            throw error;
        }
    }

    async connect(): Promise<void> {
        try {
            await this.sequelize.authenticate();
            logger.info('Database Connection has been established successfully  - DbIntervals');
        }
        catch (err) {
            logger.error({ error: err }, 'Unable to connect to the database - DbIntervals:');
            process.exit(9);
        }
    }

    async calculate(interval: DailyBase) {
        try {

            let result = await this.sequelize.query(`CALL ${interval.getType()}_calculate (?)`, {
                replacements: [interval.intervalDate + '_' + interval.interval.toString().padStart(4, '0')]
            });

            return result;
        } catch (error) {

            throw error;
        }
    }
}