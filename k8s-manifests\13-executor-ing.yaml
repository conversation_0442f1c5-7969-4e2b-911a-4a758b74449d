apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yflow-yoizen-qa-executor-ing
  namespace: yflow-yoizen-qa
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
    nginx.ingress.kubernetes.io/secure-backends: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: "32m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "64k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "16"
    nginx.ingress.kubernetes.io/proxy-buffers-size: "256k"
spec:
  ingressClassName: nginx-private
  tls:
  - hosts:
    - yflow-yoizen-qa-executor.local.ysocial.net
    secretName: local-ysocial-cert
  rules:
  - host: yflow-yoizen-qa-executor.local.ysocial.net
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: yflow-yoizen-qa-executor-svc
            port:
              number: 3000
