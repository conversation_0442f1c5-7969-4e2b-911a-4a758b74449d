$flowColor: #0d7499;
$flowColorGrayScale: grayscale($flowColor);
$defaultColor: rgb(33, 37, 41);
$detail-board-bg : #f3f2f2;
$primary: #57b7b3;
$secondary: #185550;

$md-smart-pallete: (
  50 : #e2eef3,
  100 : #b6d5e0,
  200 : #86bacc,
  300 : #569eb8,
  400 : #3189a8,
  500 : #0d7499,
  600 : #0b6c91,
  700 : #096186,
  800 : #07577c,
  900 : #03446b,
  A100 : #9ad4ff,
  A200 : #67beff,
  A400 : #34a8ff,
  A700 : #1a9dff,
  contrast: (
    50 : #000000,
    100 : #000000,
    200 : #000000,
    300 : #000000,
    400 : #ffffff,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #000000,
    A200 : #000000,
    A400 : #000000,
    A700 : #000000,
  )
);

$block-base : white;
$block-contrast : black;
$block-border: #eee;

$darkGreen: #242;
$green: green;
$darkGray: #555;
$gray: #bbb;
$error-color: #E95F4A;
$disabled: #efefef;

$default-icon-color: #999;
$selection-icon-color: #000;

$delete-popup-border: #ccc;

$popup-delete-test: #777;
$popup-background-color: white;
$popup-delete-button-background: #E95F4A;

$block-selector-group-name: #777;
$block-selector-hover-color: #f3f2f2;

$block-in-piece-background: #aaa;
$block-in-piece-color: black;

$menu-highlight: #eee;

$input-highlight-color: #057CCD;
$input-border-radius: 5px;

$version-highlight: rgb(179, 217, 243);

$facebookColor: #2980b9;
$facebookMessengerColor: #0084ff;
$twitterColor: #33ccff;
$instagramColor: #3f729b;
$mailColor: #000;
$chatColor: #e01f36;
$smsColor: #4cd964;
$whatsappColor: #64D448;
$linkedinColor: #0077b5;
$skypeColor: #00aff0;
$telegramColor: #37aee2;
$mercadoLibreColor: #ffe600;
$genericColor: #51ebff;
$teamsColor: #464EB8;

$newCaseColor: green;
$transferredToYSocialColor: orangered;
$closedByYFlowColor: cornflowerblue;
$newMessageColor: blueviolet;
$caseAbandonedColor: rgb(224, 224, 0);
$hsmCase: brown;
$iconColorForCard:rgba(0,0,0,.15);
$monthlyUserColor: #007bff;

$fontFamily: unquote("Cera Round Pro"), unquote("San Francisco"), unquote("Segoe UI"), unquote("Arial"), unquote("sans-serif");
$fontFamilyCondensed: unquote("Cera Round Pro"), unquote("San Francisco"), unquote("Segoe UI Light"), unquote("Arial"), unquote("sans-serif");
$fontFamilyTitles: unquote("Cera Round Pro"), unquote("San Francisco"), unquote("Segoe UI Semibold"), Arial, sans-serif;
$fontFamilyConfigurationTitles: unquote("Cera Round Pro"), unquote("San Francisco"), unquote("Segoe UI Light"), unquote("Arial"), unquote("sans-serif");
$fontSize: 13px;
$fontSizeConfigurationTitles: 24px;
$fontFamilyMono: unquote("Inconsolata"), unquote("Lucida Console"), Monaco, unquote("Courier New"), Courier, monospace;

$navbarBackgroundColor: rgb(243, 242, 242);
$navbarBorderColor: darken($navbarBackgroundColor, 5%);
$sidebarBackgroundColor: rgb(243, 242, 242);
$sidebarBorderColor: darken($sidebarBackgroundColor, 5%);

$navbarHeight: 49px;
$searchbarHeight: 50px;

$linkActionColor: #337ab7;
$selectOption: #027c26;

$cardSeparatorBorderColor: #ebebeb;
$cardSeparator: 1px solid $cardSeparatorBorderColor;

$inputVariableBackgroundColor: rgba(#1F9BB1, 1);
$inputVariableColor: #fff;

$inputScriptBackgroundColor: rgba(#D08050, 1);
$inputScriptColor: #fff;

$disabledBackgroundColor: rgb(235, 235, 228);

$hightlightColor: rgb(241, 222, 0);
