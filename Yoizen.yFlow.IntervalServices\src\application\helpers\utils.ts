import DailyByBlocks from "../../../../Yoizen.yFlow.Web/models/historical/dailyByBlocks";
import DailyByCommands from "../../../../Yoizen.yFlow.Web/models/historical/dailyByCommands";
import DailyByDefaultAnswer from "../../../../Yoizen.yFlow.Web/models/historical/dailyByDefaultAnswer";
import DailyByDerivationKey from "../../../../Yoizen.yFlow.Web/models/historical/dailyByDerivationKey";
import DailyByFlow from "../../../../Yoizen.yFlow.Web/models/historical/dailyByFlow";
import DailyByGroups from "../../../../Yoizen.yFlow.Web/models/historical/dailyByGroups";
import DailyByIntegrations from "../../../../Yoizen.yFlow.Web/models/historical/dailyByIntegrations";
import DailyByStatisticEvent from "../../../../Yoizen.yFlow.Web/models/historical/dailyByStatisticEvent";
import DetailedStatisticEvent from "../../../../Yoizen.yFlow.Web/models/detailed/detailedStatisticEvent";
import AbandonedCase from "../../../../Yoizen.yFlow.Web/models/historical/abandonedCase";
import DailyByBlocksSequence from "../../../../Yoizen.yFlow.Web/models/historical/dailyByBlocksSequence";
import DailyByGroupsSequence from "../../../../Yoizen.yFlow.Web/models/historical/dailyByGroupsSequence";

export const GetModelName = (model) => {
    switch (model) {
        case DailyByBlocks:
            return "DailyBlocks";
        case DailyByCommands:
            return "DailyCommands";
        case DailyByDefaultAnswer:
            return "DailyDefaultAnswer";
        case DailyByDerivationKey:
            return "DailyDerivationKey";
        case DailyByFlow:
            return "DailyGlobal";
        case DailyByGroups:
            return "DailyGroups";
        case DailyByIntegrations:
            return "DailyIntegrations";
        case DailyByStatisticEvent:
            return "DailyStatisticEvent";
        case DetailedStatisticEvent:
            return "DailyStatisticEventMessages";
        case AbandonedCase:
            return "DetailedAbandonedCases"
        case DailyByBlocksSequence:
            return "DailyByBlocksSequence"
        case DailyByGroupsSequence:
            return "DailyByGroupsSequence"
        default:
            return "Unknown";
    }
}