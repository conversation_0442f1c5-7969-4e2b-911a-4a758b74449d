import {Component, OnInit, ElementRef, ViewChild, Input} from '@angular/core';
import { EditorService } from '../../../services/editor.service';

@Component({
  selector: 'app-block-tab',
  templateUrl: './block-tab.component.html',
  styleUrls: ['./block-tab.component.scss']
})
export class BlockTabComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @ViewChild('container', { static: false }) container: ElementRef;
  isLoading: boolean = false;

  constructor(private editorService: EditorService) { }

  ngOnInit() {
      this.editorService.onBlockSelected.subscribe(() => {
          this.isLoading = true;
          this.container.nativeElement.scrollTop = 0;
          this.isLoading = false;
      });
  }
}
