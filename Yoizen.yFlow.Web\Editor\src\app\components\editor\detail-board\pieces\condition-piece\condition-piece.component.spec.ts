import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ConditionPieceComponent } from './condition-piece.component';

describe('ConditionPieceComponent', () => {
  let component: ConditionPieceComponent;
  let fixture: ComponentFixture<ConditionPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ConditionPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConditionPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
