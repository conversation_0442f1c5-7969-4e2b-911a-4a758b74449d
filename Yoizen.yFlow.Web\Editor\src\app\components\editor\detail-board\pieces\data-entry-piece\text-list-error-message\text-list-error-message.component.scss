@import "_variables";
@import "_mixins";

.addName {
  padding: 10px;
  position: relative;
  width: 218px;

  .trash {
    @include trash;
    position: absolute;
    right: -5px;
    top: -5px;
    cursor: pointer;

    &:hover {
      color: #555;
    }
  }

  &:hover {
    .trash {
      @include trashOver;
    }
  }
}

textarea {
  width: 100%;
  background: transparent;
  white-space: pre-wrap;
  height: 33px;
  font-weight: normal;
  border-radius: 10px;
  border: 1px solid #9a9a9a;
  padding-left: 10px;
}
