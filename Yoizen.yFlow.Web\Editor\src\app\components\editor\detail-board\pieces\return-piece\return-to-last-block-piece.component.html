<div class="return card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-undo-alt"></span> {{ 'CARD_RETURNTOLASTBLOCK_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_RETURNTOLASTBLOCK_INFO' | translate }}
  </div>
  <div class="empty" *ngIf="isInInvalidPosition()" role="alert">
    <div class="alert alert-danger">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'RETURNTOLASTBLOCK_ONLYLASTPIECE_INFO' | translate }}
    </div>
  </div>
</div>
