﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using YFlowUtils.Models;
using YFlowUtils.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Dynamic;

namespace YFlowUtils.Controllers
{
	[ApiController]
	[Route("api/Account")]
	public class AccountController : ControllerBase
    {
		private const string key = "6+0WAvwNXuEtpst2l6ruHHBdHLUVfTVFJQhsECxNUPI=";
		private const string IV = "YJY2yfEyqHiGeS7dRnDSKw==";
		[HttpPost]
		[Route("Linking")]
		public async Task<IActionResult> Linking([FromQuery] string token, [FromBody] JObject data)
		{
			StatusResponse response = new StatusResponse();
			try
			{
				Cryptography crypto = new Cryptography(key, IV);
				string tokenPlainData = crypto.Decrypt(token);

				dynamic tokenData = JObject.Parse(tokenPlainData);

				int messageId = tokenData["messageId"];
				string client = tokenData["client"];

				long expiration = tokenData["exp"];

				if (new DateTimeOffset(DateTime.Now).ToUnixTimeMilliseconds() > expiration)
				{
					response.Success = 0;
					response.Message = "Token expired";
					response.Data = null;

					return Unauthorized(response);
				}

				var fileData = new JObject();

				fileData["msg"] = new JObject();
				fileData["user"] = new JObject();

				switch (tokenData["sst"].ToObject<string>())
				{
					case "1":
						fileData["account"] = tokenData["sc"]["UserId"].ToObject<string>();
						break;
					case "2":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					/*case "4":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;
					case "8":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;*/
					case "16":
						fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
						break;
					case "32":
						fileData["account"] = tokenData["sc"]["number"].ToObject<string>();
						break;
					/*case "64":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;*/
					case "128":
						fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
						break;
					case "256":
						fileData["account"] = tokenData["sc"]["AccountId"].ToObject<string>();
						break;
					case "512":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					case "1024":
						fileData["account"] = tokenData["sc"]["ID"].ToObject<string>();
						break;
					case "2048":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					case "4096":
						fileData["account"] = tokenData["sc"]["UserID"].ToObject<string>();
						break;
				}
				Executor exectorHelper = new Executor();
				fileData["user"]["data"] = data;
				response.Data.Interaction = await exectorHelper.newInteraction(tokenData, fileData, InteractionType.accountLinking);

				response.Success = 1;
				response.Message = "OK";
			}
			catch(Exception exception) 
			{
				response.Success = 0;
				response.Message = exception.Message;
				response.Data = null;
			}
			return Ok(response);
		}

		[HttpPost]
		[Route("unlinking")]
		public async Task<IActionResult> Unlinking([FromQuery] string token)
		{
			StatusResponse response = new StatusResponse();
			try
			{
				Cryptography crypto = new Cryptography(key, IV);
				string tokenPlainData = crypto.Decrypt(token);

				dynamic tokenData = JObject.Parse(tokenPlainData);

				string guid = tokenData["uuid"];
				int messageId = tokenData["messageId"];
				string client = tokenData["client"];

				var fileData = new JObject();

				long expiration = tokenData["exp"];
				if (new DateTimeOffset(DateTime.Now).ToUnixTimeMilliseconds() > expiration)
				{
					response.Success = 0;
					response.Message = "Token expired";
					response.Data = null;

					return Unauthorized(response);
				}

				fileData["msg"] = new JObject();
				fileData["user"] = new JObject();

				switch (tokenData["sst"].ToObject<string>())
				{
					case "1":
						fileData["account"] = tokenData["sc"]["UserId"].ToObject<string>();
						break;
					case "2":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					/*case "4":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;
					case "8":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;*/
					case "16":
						fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
						break;
					case "32":
						fileData["account"] = tokenData["sc"]["number"].ToObject<string>();
						break;
					/*case "64":
						fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
						break;*/
					case "128":
						fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
						break;
					case "256":
						fileData["account"] = tokenData["sc"]["AccountId"].ToObject<string>();
						break;
					case "512":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					case "1024":
						fileData["account"] = tokenData["sc"]["ID"].ToObject<string>();
						break;
					case "2048":
						fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
						break;
					case "4096":
						fileData["account"] = tokenData["sc"]["UserID"].ToObject<string>();
						break;
				}
				Executor exectorHelper = new Executor();
				response.Data.Interaction = await exectorHelper.newInteraction(tokenData, fileData, InteractionType.accountUnlinking);

				response.Success = 1;
				response.Message = "OK";
			}
			catch (Exception exception)
			{
				response.Success = 0;
				response.Message = exception.Message;
				response.Data = null;
			}
			return Ok(response);
		}

		[HttpGet]
		[Route("Version")]
		public IActionResult Version()
		{
			StatusResponse response = new StatusResponse();
			response.Message = "v1.0.0";
			return Ok(response);
		}
	}
}