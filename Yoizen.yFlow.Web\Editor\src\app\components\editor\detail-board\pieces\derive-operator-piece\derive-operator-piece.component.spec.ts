import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DeriveOperatorPieceComponent } from './derive-operator-piece.component';

describe('DeriveOperatorPieceComponent', () => {
  let component: DeriveOperatorPieceComponent;
  let fixture: ComponentFixture<DeriveOperatorPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DeriveOperatorPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DeriveOperatorPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
