<div class="configurable-item" *ngIf="isYoizenAdmin">
  <div class="title">
    {{ 'CONFIGURATION_HIGH_AVAILABILITY_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_HIGH_AVAILABILITY_DESCRIPTION' | translate }}
  </div>
  <div class="controls">
    <div class="input-group">
      <label class="label">{{ 'CONFIGURATION_HIGH_AVAILABILITY_ENABLED' | translate }}:</label>
      <ui-switch [(ngModel)]="isHighAvailabilityEnabled" [disabled]="readOnly" color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
</div>
