import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {StoreMessagePiece} from "../../../../../models/pieces/StoreMessagePiece";
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { FlowDefinition } from 'src/app/models/FlowDefinition';
import { ChannelTypes } from "../../../../../models/ChannelType";

@Component({
  selector: 'app-store-message-piece',
  templateUrl: './store-message-piece.component.html',
  styleUrls: ['./store-message-piece.component.scss']
})
export class StoreMessagePieceComponent extends BasePieceVM implements OnInit {
  model : StoreMessagePiece;
  
  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as StoreMessagePiece;
  }

  isFileEvent(editorService : EditorService): boolean{
    const parentBlock = this.editorService.getSelectedBlock();
    const flow = this.editorService.getCurrentFlow();

    if (flow.channel !== ChannelTypes.Chat){
      this.model.IsFileEvent = false;
      return false;
    }
    
    if (parentBlock !== null) {
      let currentIndex = parentBlock.Pieces.findIndex(p => p.Id === this.model.Id);
      if (currentIndex !== -1) {
        if (currentIndex === 0) {
          this.model.IsFileEvent = false;
          return false;
        }

        let previousPiece = parentBlock.Pieces[currentIndex - 1];
        if (previousPiece.type === 'multimedia-entry-piece') {
          this.model.IsFileEvent = true;
          return true;
        }
      }
    }

    this.model.IsFileEvent = false;
    return false;
  }
}
