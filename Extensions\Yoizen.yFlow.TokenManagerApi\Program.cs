using TokenManagerApi.Models;
using TokenManagerApi.Services;
using Microsoft.AspNetCore.HttpsPolicy;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddHttpClient();

if (builder.Environment.IsProduction())
{
    builder.Services.Configure<HttpsRedirectionOptions>(options =>
    {
        options.HttpsPort = null;
    });
}

builder.Services.AddSingleton<ITokenManagerService, TokenManagerService>();

builder.Services.AddHealthChecks();
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();
builder.Host.UseSerilog();


var app = builder.Build();

app.MapHealthChecks("/health").WithName("health");

app.MapPost("/api/token", async (
    TokenRequest request,
    ITokenManagerService tokenManager,
    ILogger<Program> logger) =>
{
    try
    {
        var tokenResponse = await tokenManager.GetTokenAsync(request);
        return Results.Ok(tokenResponse);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error al procesar solicitud de token");
        return Results.BadRequest(new { error = ex.Message });
    }
})
.WithName("GetToken")
.WithOpenApi();


app.Run();
