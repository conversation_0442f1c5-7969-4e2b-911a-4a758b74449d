import moment from "moment";
import { IReportPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IReportPort";
import AbandonedCase from '../../../../Yoizen.yFlow.Web/models/historical/abandonedCase';
import DailyByBlocks from '../../../../Yoizen.yFlow.Web/models/historical/dailyByBlocks';
import DailyByCommands from '../../../../Yoizen.yFlow.Web/models/historical/dailyByCommands';
import DailyByDefaultAnswer from '../../../../Yoizen.yFlow.Web/models/historical/dailyByDefaultAnswer';
import DailyByDerivationKey from '../../../../Yoizen.yFlow.Web/models/historical/dailyByDerivationKey';
import DailyByStatisticEvent from '../../../../Yoizen.yFlow.Web/models/historical/dailyByStatisticEvent';
import DailyByFlow from '../../../../Yoizen.yFlow.Web/models/historical/dailyByFlow';
import DailyByGroups from '../../../../Yoizen.yFlow.Web/models/historical/dailyByGroups';
import DailyByIntegrations from '../../../../Yoizen.yFlow.Web/models/historical/dailyByIntegrations';
import DetailedStatisticEvent from '../../../../Yoizen.yFlow.Web/models/detailed/detailedStatisticEvent';
import DetailedEvent from '../../../../Yoizen.yFlow.Web/models/detailed/detailedEvent';
import UserSession from '../../../../Yoizen.yFlow.Web/models/userSession';
import DailyByBlocksSequence from "../../../../Yoizen.yFlow.Web/models/historical/dailyByBlocksSequence";
import DailyByGroupsSequence from "../../../../Yoizen.yFlow.Web/models/historical/dailyByGroupsSequence";
import Flow from '../../../../Yoizen.yFlow.Web/models/flow';
import fs from 'fs';
import { reportsFolder } from "../../../../Yoizen.yFlow.Web/helpers/folders";
import JSZip from 'jszip'
import zipdir from 'zip-dir';
import { config } from "../../config";
import { GetModelName } from "../helpers/utils";
import { FlowRedisCacheRepository } from "../../../../Yoizen.yFlow.Infrastructure/src/adapters/RedisCacheRepository";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class ReportService {
  constructor(
    private reportPort: IReportPort,
    private cache: FlowRedisCacheRepository
  ) {
  }

  private async Consolidate(): Promise<any> {
    try {
      logger.info(`Voy a consolidar los reportes pendientes`);
      let reports = await this.reportPort.GetAll('PENDING');

      let i = 0;
      while (i < reports.length) {
        await this.generateReport(reports[i]);
        i++;
      }
    } catch (error) {
      logger.error(`Ocurrió un error al intentar consolidar los reportes`);
      logger.error(`Error: ${error}`);
    }

    return null;
  }

  private async generateFile(report) {
    let model, file, files = [], alternativeCompression = false;
    switch (report.report_type) {
      case "REPORT_TYPE_BLOCKS":
        file = await DailyByBlocks.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByBlocks;
        break;
      case "REPORT_TYPE_COMMAND":
        file = await DailyByCommands.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByCommands;
        break;
      case "REPORT_TYPE_DEFAULT_ANSWERS":
        file = await DailyByDefaultAnswer.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByDefaultAnswer;
        break;
      case "REPORT_TYPE_DERIVATION_KEY":
        file = await DailyByDerivationKey.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByDerivationKey;
        break;
      case "REPORT_TYPE_GLOBAL":
        file = await DailyByFlow.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByFlow;
        break;
      case "REPORT_TYPE_GROUP":
        file = await DailyByGroups.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByGroups;
        break;
      case "REPORT_TYPE_INTEGRATIONS":
        file = await DailyByIntegrations.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByIntegrations;
        break;
      case "REPORT_TYPE_STATISTICS_EVENT":
        report.events = report.events ? report.events.split('|') : [];
        file = await DailyByStatisticEvent.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang, report.events);
        model = DailyByStatisticEvent;
        break;
      case "REPORT_TYPE_STATISTICS_EVENT_MESSAGES": {
        report.events = report.events ? report.events.split('|') : [];

        if (!fs.existsSync(`${reportsFolder}/${report.id}/`)) {
          fs.mkdirSync(`${reportsFolder}/${report.id}/`, { recursive: true });
        }

        let flowVersion = await Flow.findLastProductionById(report.flow_id);
        if (flowVersion !== null &&
          Array.isArray(flowVersion.ActiveProductionVersion.blob.StatisticEventList)) {
          if (flowVersion.ActiveProductionVersion.blob.StatisticEventList.length !== 0) {
            let existsOneEventWithStructureData = flowVersion.ActiveProductionVersion.blob.StatisticEventList.find(event => event.StructuredDataEnabled) !== undefined;

            if (existsOneEventWithStructureData) {
              alternativeCompression = true;

              for (const element of report.events) {
                const eventId = Number(element);
                let event = flowVersion.ActiveProductionVersion.blob.StatisticEventList.find(item => item.Id === eventId);
                files.push(await DetailedStatisticEvent.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang, Array(event)));
              }
            }
            else {
              let events = report.events.map(event => {
                return {
                  Id: Number(event)
                };
              });
              file = await DetailedStatisticEvent.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang, events);
            }
            model = DetailedStatisticEvent;
          }
          else {
            throw new Error('No existen eventos estadísticos');
          }
        }
        else {
          throw new Error('El flujo no existe');
        }
      }
        break;
      case "REPORT_TYPE_ABANDONED_CASES":
        try {
          logger.info(`Generando reporte de casos abandonados para el flujo ${report.flow_id} desde ${report.date_start} hasta ${report.date_end}`);
          file = await AbandonedCase.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
          model = AbandonedCase;
          logger.info(`Successfully generated abandoned cases report: ${file}`);
        } catch (error) {
          logger.error(`Error generating abandoned cases report for flow ${report.flow_id}:`, error.message);
          logger.error(`Stack trace:`, error.stack);

          throw new Error(`Failed to generate abandoned cases report for flow ${report.flow_id}: ${error.message}`);
        }
        break;
      case "REPORT_TYPE_GROUP_SEQUENCE":
        file = await DailyByGroupsSequence.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByGroupsSequence;
        break;
      case "REPORT_TYPE_BLOCKS_SEQUENCE":
        file = await DailyByBlocksSequence.generateText(report.id, report.flow_id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DailyByBlocksSequence;
        break;
      case "REPORT_TYPE_DETAILED_EVENTS":
        file = await DetailedEvent.generateText(report.id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = DetailedEvent;
        break;
      case "REPORT_TYPE_USER_SESSIONS":
        file = await UserSession.generateText(report.id, report.date_start, report.date_end, config.offset, ';', report.user.lang);
        model = UserSession;
        break;
      default:
        logger.info(`No hay tratamiento para el tipo de reporte ${report.report_type}`);
        break;
    }

    return {
      file,
      model,
      alternativeCompression
    }

  }

  private async generateReport(report) {
    let tempFolder = null;

    report.date_start = moment(Number(report.date_start));
    report.date_end = moment(Number(report.date_end));

    try {
      logger.info(`Se generará el reporte ${report.report_type}`);

      const { file, model, alternativeCompression } = await this.generateFile(report)

      if (file) {
        const filename = `${report.id}_${GetModelName(model)}.csv`;

        let zip = new JSZip();
        
        const fileBuffer = fs.readFileSync(file);
        zip.file(filename, fileBuffer, { binary: false });
        return new Promise<void>(resolve => {
          zip.generateNodeStream({
            type: 'nodebuffer',
            compression: "DEFLATE",
            streamFiles: true
          })
            .pipe(fs.createWriteStream(`${reportsFolder}/${report.id}.zip`))
            .on('finish', async () => {
              await this.reportPort.ChangeStatus(report.id, 'FINISHED');

              if (fs.existsSync(file)) {
                fs.unlinkSync(file);
              }

              return resolve();
            });
        });
      }
      else if (alternativeCompression) {
        zipdir(`${reportsFolder}/${report.id}/`, async (err, buffer) => {
          logger.info(`Comprimiendo el archivo: ${tempFolder}`)

          fs.rmdirSync(`${reportsFolder}/${report.id}/`, { recursive: true });
          if (err) {
            logger.info(`Ocurrio un error durante la compresion del archivo: ${tempFolder}`)
            await this.reportPort.ChangeStatus(report.id, 'ERROR');

          }
          else {
            fs.writeFileSync(`${reportsFolder}/${report.id}.zip`, buffer)
            await this.reportPort.ChangeStatus(report.id, 'FINISHED');
          }
        });
      }
    }
    catch (ex) {
      logger.error(`Ocurrió un error generando el reporte ${report.report_type}: ${ex.message}`);

      if (fs.existsSync(`${reportsFolder}/${report.id}/`)) {
        fs.rmdirSync(`${reportsFolder}/${report.id}/`, { recursive: true });
      }
      await this.reportPort.SetError(report.id, ex.message);
    }
  }

  async Process() {
    await this.Consolidate();

    setTimeout(this.Process.bind(this), 60000 * 5);
  }
}