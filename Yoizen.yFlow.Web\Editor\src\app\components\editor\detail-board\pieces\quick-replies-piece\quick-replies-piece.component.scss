@import "_variables";
@import "_mixins";

.quick-reply {
  width: 100%;

  .definition {
    display: flex;
    align-items: center;
    flex-direction: row;

    .quick {
      width: 250px;
      margin-right: 20px;
      margin-bottom: 20px;
      background-color: #fff;
      position: relative;
    }

    .addQuick {
      vertical-align: top;
      background: #fff;
      width: 250px;
      border: 1px solid $cardSeparatorBorderColor;
      text-align: center;
      padding: 10px;
      border-radius: 20px;
      margin-right: 10px;
      text-transform: uppercase;
      font-size: 12px;
      flex-shrink: 0;
      flex-grow: 0;

      &:hover {
        cursor: pointer;
      }
    }

    .attribute {
      vertical-align: top;
      display: flex;
      flex-shrink: 1;
      flex-grow: 1;

      label {
        margin-right: 10px;
        margin-top: auto;
        margin-bottom: auto;
        flex-shrink: 0;
        flex-grow: 0;
      }

      .block {
        width: 200px;
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .addQuick {
      span {
        color: #bbb;
        display: inline-block;
        margin-right: 10px;
      }
      &:hover {
        span {
          color: #555;
        }
        box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.17);
      }
    }

    .options {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .block {
      padding: 10px;

      input {
        width: 100%;
      }

      .selected-block-container {
        .block-selected {
          white-space: nowrap;
          max-width: 100%;

          .block-display {
            padding: 2px 10px;
            flex-wrap: nowrap;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100%;

            .variable-name {
              @include variable-name;
            }

            .variable-type {
              @include variable-type;
            }
          }

          .trash {
            position: absolute;
          }
        }
      }
    }
  }
}
