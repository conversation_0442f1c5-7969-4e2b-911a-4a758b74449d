<div class="wa-menu card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa far fa-list-alt"></span> {{ 'CARD_WA_MENU_TITLE' | translate }}
  </div>
  <div class="message">
    <div class="max-length" role="alert" *ngIf="!readOnly">
      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_MAX_LENGTH' | translate }}</span>
      </div>
    </div>
    <div class="messages">
      <app-text-list-message *ngFor="let text of messageModel?.TextList let i = index"
                             [(Text)]="text.text"
                             [Index]="i"
                             [CanDelete]="canDeleteMessages()"
                             [readOnly]="readOnly"
                             [Validator]="isTextValid(i)"
                             (onDelete)="deleteElement($event)"></app-text-list-message>
      <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
        <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
             placement="right" tooltipClass="tooltip-add">
          <span class="fa fa-plus"></span>
        </div>
      </div>
    </div>
  </div>

  <div class="jump">
    <div class="conditions" *ngIf="switchJumpToBlockModel.VariableId !== -1">
      <div class="title">
        {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS' | translate }}
      </div>
      <div class="conditions-table" *ngIf="switchJumpToBlockModel.Conditions !== null && switchJumpToBlockModel.Conditions.length > 0">
        <div class="header">
          <div>{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_VALUE' | translate }}</div>
          <div>{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_BLOCK' | translate }}</div>
          <div></div>
        </div>
        <div class="condition-row" *ngFor="let condition of switchJumpToBlockModel.Conditions let i = index">
          <div class="condition-value">
            <input class="input" type="text" [(ngModel)]="condition.Value"
                   [disabled]="readOnly"
                   (ngModelChange)="generateRegex()"
                   spellcheck="false" autocomplete="off"/>
          </div>
          <div class="condition-variable">
            <app-block-picker class="input" [blockId]="condition.BlockId"
                              (onSelectNewBlock)="onSelectBlock(i, $event)"
                              [readOnly]="readOnly"
                              (onDeleteBlock)="onDeleteBlock(i, $event)"
                              [isInvalid]="!condition.isBlockValid(editorService)"></app-block-picker>
          </div>
          <div class="trash" *ngIf="!readOnly">
            <div (click)="deleteCondition(i)"
                 tooltipClass="tooltip-trash-left"
                 data-toggle="tooltip" ngbTooltip="{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_REMOVE' | translate }}" placement="left">
              <span class="fa fa fa-trash-alt"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="empty" *ngIf="switchJumpToBlockModel.Conditions === null || switchJumpToBlockModel.Conditions.length === 0" role="alert">
        <div class="alert alert-info">
          {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS_EMPTY' | translate }}
        </div>
      </div>
      <div class="add" (click)="addCondition()" *ngIf="!readOnly">
        <span class="fa fa-plus"></span> {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS_ADD' | translate }}
      </div>
    </div>
  </div>

  <div class="data-entry">
    <!--<div class="destvariableparseformat" *ngIf="model.VariableId !== -1 && variableData?.Type == variableTypes.StringDate">
      <span class="title">{{ 'DATAENTRY_DESTVARIABLEPARSEFORMAT' | translate }}:</span>
      <input type="text"
             class="input"
             [disabled]="readOnly"
             [(ngModel)]="model.ParseFormat"
             placeholder="{{'PARSEFORMAT_PLACEHOLDER' | translate }}"
             [ngClass]="{'invalid-input': !model.isParseFormatValid(editorService)}"
             spellcheck="false" autocomplete="off" />
    </div>-->
    <div class="regex">
      <span class="title">{{ 'DATAENTRY_REGEX' | translate }}:</span>
        <!--<div class="regex-container" [hidden]="regexReadOnly">
          <input class="input" type="text" placeholder="{{'REGEX_PLACEHOLDER' | translate }}" [(ngModel)]="model.Regex"
               [disabled]="readOnly || regexReadOnly"
               [ngClass]="{'invalid-input': !dataEntryModel.isRegexValid(editorService)}" spellcheck="false">
          <i class="fa fa-question-circle icon" *ngIf="!readOnly" (click)="displayRegexHelp()" data-toggle="tooltip" ngbTooltip="{{ 'HELP_REGEX' | translate }}" placement="top"
          tooltipClass="tooltip-action-row-top"></i>
        </div>-->
        <div class="template-container">
          <input class="input" type="text" [value]="currentRegexString" [disabled]="true">
          <!--<i class="fa fa-trash icon" (click)="clearRegex()" data-toggle="tooltip" ngbTooltip="{{ 'CLEAR' | translate }}" placement="top"
          tooltipClass="tooltip-action-row-top"></i>
          <i class="fa fa-edit icon" (click)="displayRegexHelp()" data-toggle="tooltip" ngbTooltip="{{ 'EDIT' | translate }}" placement="top"
          tooltipClass="tooltip-action-row-top"></i>-->
        </div>
    </div>
    <!--<div class="add-condition" (click)="dataEntryModel.HasCondition = true" *ngIf="!dataEntryModel.HasCondition && !readOnly">
      <span class="fa fa-plus"></span> {{ 'DATAENTRY_ADD_VALIDATION' | translate }}
    </div>
    <div class="condition" *ngIf="dataEntryModel.HasCondition">
      <label>{{ 'DATAENTRY_VALIDATION_INFO' | translate }}</label>
      <div class="info">
        <select class="select" name="" id=""
                [disabled]="readOnly"
                [(ngModel)]="dataEntryModel.Operator"
                [ngClass]="{'validation-error': !dataEntryModel.isOperatorValid()}">
          <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
        </select>
        <app-input-with-variables *ngIf="showOperand()"
                                  class="value"
                                  [placeholder]="'VALUE' | translate"
                                  [(value)]="dataEntryModel.SecondValue"
                                  [wideInput]="true"
                                  [disabled]="readOnly"
                                  [ngClass]="{'validation-error': !dataEntryModel.isSecondValueValid()}"></app-input-with-variables>
      </div>
      <div class="trash" (click)="dataEntryModel.HasCondition=false" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'DATAENTRY_REMOVE_VALIDATION' | translate }}" placement="top" container="body"
           tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>-->
    <!--<div class="commands">
      <label>{{'DATAENTRY_VALIDATIONERROR_COMMANDS' | translate}}</label>
      <div class="command">
        <span class="title">{{'DATAENTRY_CHECKCOMMANDS' | translate}}:</span>
        <ui-switch [(ngModel)]="dataEntryModel.CheckCommands" [disabled]="readOnly"
                   color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      </div>
    </div>
    <div class="commands">
      <label>{{'DATAENTRY_VALIDATIONERROR_COMMANDS_ALWAYS' | translate}}</label>
      <div class="command">
        <span class="title">{{'DATAENTRY_CHECKCOMMANDS' | translate}}:</span>
        <ui-switch [(ngModel)]="dataEntryModel.CheckCommandsAlways" [disabled]="readOnly"
                   color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      </div>
    </div>-->

    <div class="validation" *ngIf="dataEntryModel.VariableId !== -1">
      <label>{{'DATAENTRY_VALIDATIONERROR' | translate}}</label>
      <div class="validation-table">
        <div class="header">
          <div></div>
          <div class="center">{{ 'ENTRY_PIECE_DO_NOT_VALIDATE' | translate }}</div>
          <div class="center">{{ 'ENTRY_PIECE_VALIDATE_ON_ERROR' | translate }}</div>
          <div class="center">{{ 'ENTRY_PIECE_VALIDATE_ALWAYS' | translate }}</div>
        </div>
        <div class="row">
          <div>{{'DATA_ENTRY_COMMANDS' | translate}}
            <span class="fas fa-star" *ngIf="!getCognitivityPriority()" data-toggle="tooltip" ngbTooltip="{{ 'PRIORITY_LABEL' | translate }}"
            placement="top" container="body" tooltipClass="tooltip-star"></span>
          </div>
          <div class="center" (click)="selectCommandState(validationState.Never)">
            <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.Never"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.Never"></span>
          </div>
          <div class="center" (click)="selectCommandState(validationState.OnError)">
            <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.OnError"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.OnError"></span>
          </div>
          <div class="center" (click)="selectCommandState(validationState.Always)">
            <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.Always"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.Always"></span>
          </div>
        </div>
        <div class="row" *ngIf="cognitivityEnabled">
          <div>{{'DATA_ENTRY_COGNITIVITY' | translate}}
            <span class="fas fa-star" *ngIf="getCognitivityPriority()" data-toggle="tooltip" ngbTooltip="{{ 'PRIORITY_LABEL' | translate }}"
            placement="top" container="body" tooltipClass="tooltip-star"></span>
          </div>
          <div class="center" (click)="selectCognitivityState(validationState.Never)">
            <span class="fa fa-check-circle icon-green circle-button"  [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.Never"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.Never"></span>
          </div>
          <div class="center" (click)="selectCognitivityState(validationState.OnError)">
            <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.OnError"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.OnError"></span>
          </div>
          <div class="center" (click)="selectCognitivityState(validationState.Always)">
            <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.Always"></span>
            <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.Always"></span>
          </div>
        </div>
      </div>
    </div>


    <div class="validationerror">
      <label>{{'DATAENTRY_VALIDATIONERROR_DESC' | translate}}</label>
      <div class="errormessage">
        <span class="title">{{ 'DATAENTRY_VALIDATIONERROR_MESSAGES' | translate }}:</span>
        <div class="messages">
          <app-text-list-error-message *ngFor="let text of dataEntryModel?.ErrorMessages let i = index"
                                       [(Text)]="text.text"
                                       [Index]="i"
                                       [CanDelete]="canDeleteErrorMessages()"
                                       [readOnly]="readOnly"
                                       (onDelete)="deleteErrorMessage($event)"></app-text-list-error-message>
          <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
            <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
                 placement="right" tooltipClass="tooltip-add">
              <span class="fa fa-plus"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="retries">
        <span class="title">{{ 'DATAENTRY_RETRIES' | translate }}:</span>
        <input class="input tries" type="number" placeholder="0" min="0"
               [disabled]="readOnly"
               [(ngModel)]="dataEntryModel.TryLimit" [ngClass]="{'invalid-input': !dataEntryModel.isTryLimitValid()}">
      </div>
    </div>
    <div class="next" *ngIf="dataEntryModel.VariableId !== -1">
      <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
      <app-block-picker class="input"
                        [blockId]="dataEntryModel.ErrorBlockId"
                        (onSelectNewBlock)="onSelectErrorBlock($event)"
                        (onDeleteBlock)="onDeleteErrorBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!dataEntryModel.isErrorBlockValid(editorService)"></app-block-picker>
    </div>
    <div class="mark-as-pending-reply" *ngIf="model.Channel !== channelTypes.Chat">
      <div class="alert alert-info">
        <span class="fa fa-lg fa-exclamation-circle icon"></span>
        {{ 'DATAENTRY_MARKASPENDINGREPLY_INFO' | translate }}
      </div>
      <div class="option">
        <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_TITLE' | translate}}:</span>
        <select class="select" [(ngModel)]="dataEntryModel.MarkAsPendingReplyFromCustomer">
          <option [ngValue]="markAsPendingReplyFromCustomerTypes.No">{{ 'NO' | translate }}</option>
          <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndJump">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_JUMP' | translate }}</option>
          <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndSendMessage">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_SENDMESSAGE' | translate }}</option>
        </select>
      </div>
      <div class="next" *ngIf="dataEntryModel.MarkAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndJump">
        <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_BLOCK_TITLE' | translate}}:</span>
        <app-block-picker class="input"
                          [blockId]="dataEntryModel.PendingReplyFromCustomerBlockId"
                          (onSelectNewBlock)="onSelectPendingReplyFromCustomerBlock($event)"
                          (onDeleteBlock)="onDeletePendingReplyFromCustomerBlock($event)"
                          [readOnly]="readOnly"
                          [isInvalid]="!dataEntryModel.isPendingReplyFromCustomerBlockValid(editorService)"></app-block-picker>
      </div>
      <div class="option" *ngIf="dataEntryModel.MarkAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndSendMessage">
        <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MESSAGE_TITLE' | translate}}:</span>
        <app-input-with-variables
          [placeholder]="'MESSAGE' | translate"
          [(value)]="dataEntryModel.CustomMessageForPendingReply"
          [isTextArea]="true"
          [wideInput]="true"
          [validator]="isCustomMessageForPendingReplyValid()"
          [extendedStyles]="{'height': '100px', 'min-height': '100px', 'max-height': '100px'}"
          [disabled]="readOnly">
        </app-input-with-variables>
      </div>
      <div class="option" *ngIf="dataEntryModel.MarkAsPendingReplyFromCustomer !== markAsPendingReplyFromCustomerTypes.No">
        <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MINUTES_TITLE' | translate}}:</span>
        <input type="number"
               class="input"
               min="0"
               max="1440"
               step="10"
               spellcheck="false"
               [(ngModel)]="dataEntryModel.PendingReplyFromCustomerMinutes"
               [readOnly]="readOnly"
               [ngClass]="{'invalid-input': !dataEntryModel.isPendingReplyFromCustomerMinutesValid()}" />
      </div>
    </div>
  </div>

</div>
