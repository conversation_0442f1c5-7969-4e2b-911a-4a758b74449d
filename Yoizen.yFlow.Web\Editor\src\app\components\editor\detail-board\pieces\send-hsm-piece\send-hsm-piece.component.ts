import {Component, ElementRef, OnChanges, OnInit, ViewChild} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToasterService } from 'angular2-toaster';
import { finalize } from 'rxjs/operators';
import { SourceTypes } from 'src/app/models/pieces/AttachmentPiece';
import { SendHsmPiece, Parameter, ParametersTypesHSM, FlowParameter } from 'src/app/models/pieces/SendHsmPiece';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ServerService } from 'src/app/services/server.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { isStringValid, isUrlValid } from 'src/app/urlutils.module';
import { StatusResponse } from '../../../../../models/StatusResponse';
import { HSMTemplateButton, HSMTemplateParameter, WhatsappHSMTemplateByService, YSocialSettings } from '../../../../../models/YSocialSettings';
import { ErrorPopupComponent } from '../../../../error-popup/error-popup.component';
import { BasePieceVM } from '../BasePieceVM';
import { environment } from '../../../../../../environments/environment';
import { ChannelTypes } from '../../../../../models/ChannelType';
import {TypeDefinition} from '../../../../../models/TypeDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { InvokeWhatsappFlowParameter } from 'src/app/models/pieces/InvokeWhatsappFlowPiece';

interface Template {
  description: string;
  elementName: string;
}

@Component({
  selector: 'app-send-hsm-piece',
  templateUrl: './send-hsm-piece.component.html',
  styleUrls: ['./send-hsm-piece.component.scss']
})
export class SendHsmPieceComponent extends BasePieceVM implements OnInit, OnChanges {

  model: SendHsmPiece;
  loading: boolean = false;
  settings: YSocialSettings;
  socialServices: String[] = [];
  templates: Template[] = [];
  validTemplates: WhatsappHSMTemplateByService[] = [];
  selectedService: string = '';
  showIsPublicToggle: boolean = true;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];

  ActiveIdString: string;

  SuccessBlockData: BlockDefinition = null;
  searchSuccessBlockString : String;
  ExpirationBlockData: BlockDefinition = null;
  searchExpirationBlockString : String;
  CaseExceptionBlockData: BlockDefinition = null;
  searchCaseExceptionBlockString : String;

  constructor(editorService: EditorService,
              public modalService: ModalService,
              public serverService: ServerService,
              private translateService: TranslateService,
              private toasterService: ToasterService) {
    super(editorService, modalService);
  }

  get variableDefinition(): VariableDefinition {
    return this.editorService.findVariableWithId(this.model.attachment.FileDataStorageId);
  }

  get isWhatsappChannel(): boolean {
    return this.model.Channel === ChannelTypes.WhatsApp;
  }

  get isTelegramChannel(): boolean {
    return this.model.Channel === ChannelTypes.Telegram;
  }

  get haveParameters(): boolean {
    const parameters = this.model.BodyParameters.length + this.model.HeaderParameter.length + this.model.ButtonsParameters.length + this.model.FlowParameters.length;
    return parameters > 0;
  }
  ngOnInit() {
    this.model = this.context as SendHsmPiece;
    if (this.settings === null || this.settings === undefined) {
      this.settings = this.editorService.getYSocialSettings();
    }
    this.selectedService = this.model.HSMTemplate.Name || '';

    this.setSocialServices();
    this.setDescriptions();

    this.ActiveIdString = this.getActiveTab();

    this.editorService.onYSocialSettingsChanged.subscribe((settings: YSocialSettings) => {
      this.settings = settings;
      this.setSocialServices();
      this.setDescriptions();
    });

    // @ts-ignore
    if (typeof (environment.onPremise) !== 'boolean' ||
        // @ts-ignore
        !environment.onPremise) {
      if (this.model.attachment.IsPublicUrl) {
        this.showIsPublicToggle = false;
      }
    }
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.attachment.isFileDataValid(this.editorService);
  }

  setVariableOnOutput(variable: VariableDefinition) {
    if (variable != null) {
      this.model.attachment.FileDataStorageId = variable.Id;
    } else {
      this.model.attachment.FileDataStorageId = null;
    }
  }

  getActiveTab(): string {
    switch (this.model.attachment.Source) {
      case SourceTypes.Variable:
        return 'tab-variable';
      case SourceTypes.Url:
        return 'tab-url';
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case 'tab-url':
        this.model.attachment.Source = SourceTypes.Url;
        return;
      case 'tab-variable':
        this.model.attachment.Source = SourceTypes.Variable;
        return;
    }
  }

  isUrlValid(str): boolean {
    return isUrlValid(str);
  }

  isNameValid(str): boolean {
    return isStringValid(str);
  }

  isMimeTypeValid(str): boolean {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
        str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
        str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  refreshWhatsappHSM() {
    this.loading = true;
    if (!this.settings) {
      return;
    }
    this.serverService.retrieveWhatsappHSMTemplates(this.settings.Url)
        .pipe(finalize(() => {
          this.loading = false;
        }))
        .subscribe((status: StatusResponse) => {
              if (status.data !== null && Array.isArray(status.data)) {
                console.log(status.data);
                this.editorService.loadySocialSettings(status.data);
                this.setSocialServices();
              }
            },
            error => {
              this.toasterService.pop({
                type: 'error',
                body: this.translateService.instant('CANNOT_FIND_HSM'),
                timeout: 2000
              });
            });
  }

  setSocialServices() {
    if (this.settings && this.settings.WhatsappHSMTemplatesServices && this.settings.WhatsappHSMTemplatesServices.length > 0) {
      const aux = this.settings.WhatsappHSMTemplatesServices.map(t => t.Name);
      const socialServices = new Set(aux);
      this.socialServices = Array.from(socialServices);
    }
  }

  setDescriptions(forceRefresh = false) {
    if (!this.settings || !this.settings.WhatsappHSMTemplatesServices) {
      return;
    }

    this.validTemplates = this.settings.WhatsappHSMTemplatesServices.filter(t => t.Name === this.selectedService);
    this.templates = this.validTemplates.map(x => {
      const t: Template = {
        description: x.Description,
        elementName: x.ElementName
      };
      return t;
    });

    if (forceRefresh) {
      this.model.clear();
      this.model.HSMTemplate = this.validTemplates[0];
    }
  }

  setTemplate() {
    this.model.initTemplate();
    this.model.HSMTemplate = this.validTemplates.find(x => x.ElementName === this.model.selectedElementName);

    if (this.model.HSMTemplate) {
      if (this.model.HSMTemplate.HeaderTextParameter) {
        this.model.HeaderParameter.push(new Parameter(ParametersTypesHSM.HEADER, `${this.model.HSMTemplate.HeaderTextParameter.Name}=${this.model.HSMTemplate.HeaderTextParameter.Description}`, ''));
      }
      if (this.model.HSMTemplate.TemplateParameters && this.model.HSMTemplate.TemplateParameters.length > 0) {
        this.model.BodyParameters = this.model.HSMTemplate.TemplateParameters.map(TP => new Parameter(ParametersTypesHSM.BODY, `${TP.Name}=${TP.Description}`, ''));
      }

      if (this.model.HSMTemplate.Buttons && this.model.HSMTemplate.Buttons.length > 0) {

        switch (this.model.HSMTemplate.ButtonsType) {
          case 1:
            this.model.ButtonsParameters = this.model.HSMTemplate.Buttons
                .filter(b => b.QuickReplyParameter)
                .map(b => new Parameter(ParametersTypesHSM.BUTTON, `${b.QuickReplyParameter.Name}=${b.QuickReplyParameter.Description}`, ''));
            break;

          case 2:
            this.model.HSMTemplate.Buttons.forEach((button) => {
              if (typeof button.UrlParameter === 'object' && button.UrlParameter != null) {
                const urlParameter = new Parameter(ParametersTypesHSM.BUTTON, `${button.UrlParameter.Name}=${button.UrlParameter.Description}`, '');
                this.model.ButtonsParameters.push(urlParameter);
              }

              if (typeof button.OfferCodeParameter === 'object' && button.OfferCodeParameter != null) {
                const offerParameter = new Parameter(ParametersTypesHSM.BUTTON, `${button.OfferCodeParameter.Name}=${button.OfferCodeParameter.Description}`, '');
                this.model.ButtonsParameters.push(offerParameter);
              }

              if (typeof button.FlowParameter === 'object' && button.FlowParameter != null) {
                this.model.templateHasFlow = true;
                this.getFlowParameters(button.FlowParameter);
              }
            });
            break;

          case 3:
            this.model.HSMTemplate.Buttons.forEach((button) => {
              if (typeof button.UrlParameter === 'object' && button.UrlParameter !== null) {
                const urlParameter = new Parameter(ParametersTypesHSM.BUTTON, `${button.UrlParameter.Name}=${button.UrlParameter.Description}`, '');
                this.model.ButtonsParameters.push(urlParameter);
              }

              if (typeof button.OfferCodeParameter === 'object' && button.OfferCodeParameter !== null) {
                const offerParameter = new Parameter(ParametersTypesHSM.BUTTON, `${button.OfferCodeParameter.Name}=${button.OfferCodeParameter.Description}`, '');
                this.model.ButtonsParameters.push(offerParameter);
              }

              if (typeof button.QuickReplyParameter === 'object' && button.QuickReplyParameter !== null) {
                const quickrReplyParameter = new Parameter(ParametersTypesHSM.BUTTON, `${button.QuickReplyParameter.Name}=${button.QuickReplyParameter.Description}`, '');
                this.model.ButtonsParameters.push(quickrReplyParameter);
              }

              if (typeof button.FlowParameter === 'object' && button.FlowParameter != null) {
                this.model.templateHasFlow = true;
                this.getFlowParameters(button.FlowParameter);
              }
            });
            break;
        }
      }
    }
  }

  replaceVariableInText(text, variable, value) {
    if (text && value !== '') {
      return text.replace('{{' + variable + '}}', value);
    }
    return text;
  }

  getExampleHeader() {
    return this.replaceVariableInText(this.model.HSMTemplate.HeaderText, this.model.HSMTemplate.HeaderTextParameter.Name, this.model.HeaderParameter[0].Value);
  }

  getExampleDescription() {
    let aux = this.model.HSMTemplate.Template;
    if (this.model.HSMTemplate.TemplateParameters && this.model.HSMTemplate.TemplateParameters.length > 0) {
      this.model.HSMTemplate.TemplateParameters.forEach(p => {
        const parameter = this.model.findParameterByKey(p.Name, ParametersTypesHSM.BODY);
        if (parameter && p) {
          aux = this.replaceVariableInText(aux, p.Name, parameter.Value);
        }
      });
    }
    return aux;
  }

  getFlowParameters(flowButtonParameter) {
    if (this.settings && this.settings.WhatsappFlowsByService) {
      const flow = this.settings.WhatsappFlowsByService.find(f => f.FlowId == flowButtonParameter.FlowID);
      if (flow) {
        const flowScreen = flow.FlowScreens.find(s => s.ID === flowButtonParameter.NavigateScreen);
        if (flowScreen && flowScreen.Data.length > 0) {
          const parameters = flowScreen.Data.map(function (data) {
            return new FlowParameter(data.Name, data.Type, -1, '');
          });
          this.model.FlowParameters.push(...parameters);
        }
      }
    }
  }

  getButtonText(button) {
    const type = button.UrlButtonType === 1 ?
        this.translateService.instant('SEND_HSM_BUTTON_FIXED') :
        this.translateService.instant('SEND_HSM_BUTTON_DYNAMIC');

    let buttonText = `${button.Text} (${type})`;

    if (button.UrlParameter) {
      buttonText += `- ${this.translateService.instant('SEND_HSM_BUTTON_PARAMETER')} ${button.UrlParameter.Name}=${button.UrlParameter.Description}`;
    }

    return buttonText;
  }

  getAssignVariableIdValid() {
    return true;
  }

  getVariableData(parameter: InvokeWhatsappFlowParameter) {
    if(!parameter) {
      return null;
    }

    let data = this.model.FlowParameters.find(d => d.Name === parameter.Name);
    
    if(!data || data.VariableId === -1) {
      return null;
    }
    return this.editorService.getVariableWithId(data.VariableId);
  }

  getVariableType(parameter: InvokeWhatsappFlowParameter) {
    if(!parameter) {
      return [];
    }

    let type = parameter.Type.toLowerCase();

    if(type === 'string') {
      return [ TypeDefinition.Text ];
    } else if(type === 'array') {
      return [ TypeDefinition.Array ];
    } else if(type === 'object') {
      return [ TypeDefinition.Object ];
    } else if(type === 'number') {
      return [ TypeDefinition.Number ];
    } else {
      return [ ];
    }
  }

  setFlowVariableOnOutput(variable, parameter) {
    if(!parameter || !variable) {
      this.model.FlowParameters.find(d => d.Name === parameter.Name).VariableId = -1
    } else {
      this.model.FlowParameters.find(d => d.Name === parameter.Name).VariableId = variable.Id
    }
  }

}
