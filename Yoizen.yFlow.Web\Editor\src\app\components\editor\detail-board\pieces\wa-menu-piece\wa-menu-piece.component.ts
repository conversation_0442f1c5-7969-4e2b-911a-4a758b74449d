import { TranslateService } from '@ngx-translate/core';
import { VariableDefinition } from './../../../../../models/VariableDefinition';
import { Component, OnInit } from '@angular/core';
import { WAMenu } from 'src/app/models/pieces/WAMenu';
import { DataEntry, ErrorMessage, MarkAsPendingReplyFromCustomerTypes } from 'src/app/models/pieces/DataEntry';
import { MessagePieceType, Text } from 'src/app/models/pieces/MessagePieceType';
import { SwitchJumpToBlockPiece, SwitchJumpToBlockCondition } from 'src/app/models/pieces/SwitchJumpToBlockPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';
import {ChannelTypes} from "../../../../../models/ChannelType";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {OperatorDefinitions, OperatorType} from 'src/app/models/OperatorType';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { trim } from 'lodash';
import { TemplateItem } from 'src/app/models/TemplateItem';

@Component({
  selector: 'app-wa-menu-piece',
  templateUrl: './wa-menu-piece.component.html',
  styleUrls: ['./wa-menu-piece.component.scss']
})
export class WAMenuPieceComponent extends BasePieceVM implements OnInit {
  model: WAMenu;
  messageModel: MessagePieceType;
  dataEntryModel: DataEntry;
  switchJumpToBlockModel: SwitchJumpToBlockPiece;
  currentRegexString: string;
  currentTemplate: string;
  variableData: VariableDefinition;
  variableTypes = TypeDefinition;
  markAsPendingReplyFromCustomerTypes = MarkAsPendingReplyFromCustomerTypes;
  channelTypes = ChannelTypes;
  cognitivityEnabled: boolean = false;
  validationState = ValidationState;
  validateCommandsState: ValidationState;
  validateCognitivityState: ValidationState;

  constructor(editorService: EditorService, public modalService: ModalService, public translate: TranslateService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as WAMenu;
    this.messageModel = this.model.messageModel;
    this.dataEntryModel = this.model.dataEntryModel;
    this.switchJumpToBlockModel = this.model.switchJumpToBlockModel;

    if (this.model.dataEntryModel.CheckCommandsAlways) {
      this.validateCommandsState = ValidationState.Always;
    } else if (this.model.dataEntryModel.CheckCommands) {
      this.validateCommandsState = ValidationState.OnError;
    } else {
      this.validateCommandsState = ValidationState.Never;
    }

    if (this.model.dataEntryModel.CheckCognitivityAlways) {
      this.validateCognitivityState = ValidationState.Always;
    } else if (this.model.dataEntryModel.CheckCognitivity) {
      this.validateCognitivityState = ValidationState.OnError;
    } else {
      this.validateCognitivityState = ValidationState.Never;
    }

    this.cognitivityEnabled = this.editorService.isCognitivityEnabled();
    this.flow = this.editorService.getCurrentFlow();
    this.variableData = this.editorService.findImplicitVariable('menuInput');

    this.switchJumpToBlockModel.VariableId = this.variableData.Id;
    this.switchJumpToBlockModel.VariableName = this.variableData.Name;
    this.dataEntryModel.VariableId = this.variableData.Id;

    this.generateRegex();
    //this.currentRegexString = `${this.translate.instant('LIST')} ()`;
    //this.currentTemplate = "/^$/";

    if (this.flow.channel === ChannelTypes.Twitter) {
      let index = 0
      if(this.model.messageModel.TextList.length < 3){
        index = this.model.messageModel.TextList.length;
        while (index < 3) {
          this.model.messageModel.TextList.push(new Text());
          index++;
        }
      }
      if(this.model.dataEntryModel.ErrorMessages.length < 3){
        index = this.model.dataEntryModel.ErrorMessages.length;
        while (index < 3) {
          this.model.dataEntryModel.ErrorMessages.push(new ErrorMessage());
          index++;
        }
      }

    }
  }

  isTextValid(index: number) {
    return str => { return this.messageModel.isTextValid(index, this.editorService) }
  }

  deleteElement(element) {
    this.messageModel.TextList.splice(element, 1);
  }

  canDeleteMessages() : boolean {
    if(this.flow.channel === ChannelTypes.Twitter){
      return !this.readOnly && this.model.messageModel.TextList.length > 3
    }
    return !this.readOnly && this.model.messageModel.TextList.length > 1

  }

  canDeleteErrorMessages() : boolean {
    if(this.flow.channel === ChannelTypes.Twitter){
      return !this.readOnly && this.model.dataEntryModel.ErrorMessages.length > 3
    }
    return !this.readOnly && this.model.messageModel.TextList.length > 1

  }

  canAddButton() : boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Telegram ||
      this.flow.channel === ChannelTypes.Teams) {
      var value = this.messageModel.Buttons.length < 20;
      return value;
    }

    return false;
  }

  canAddTextOptions() : boolean {
  	var value = this.messageModel.TextList.length < 3;
  	return value;
  }

  addNewText() {
  	this.messageModel.TextList.push(new Text());
  }

  getOperators() {
    if (/*this.model.VariableId === -1 ||*/ this.variableData === null) {
      return null;
    }

    let operators = OperatorDefinitions.Operators;
    if (this.variableData.Type === TypeDefinition.Number ||
      this.variableData.Type === TypeDefinition.Decimal ||
      this.variableData.Type === TypeDefinition.Timestamp ||
      this.variableData.Type === TypeDefinition.StringDate) {
      return operators.filter(op => {
        if (op.value === OperatorType.Equals ||
          op.value === OperatorType.NotEquals ||
          op.value === OperatorType.GreaterThan ||
          op.value === OperatorType.LessThan) {
          return true;
        }
        return false;
      });
    }
    else if (this.variableData.Type === TypeDefinition.Text) {
      return operators.filter(op => {
        if (op.value === OperatorType.Equals ||
          op.value === OperatorType.NotEquals ||
          op.value === OperatorType.DoesntContains ||
          op.value === OperatorType.Contains ||
          op.value === OperatorType.DoesntEndsWith ||
          op.value === OperatorType.DoesntStartsWith ||
          op.value === OperatorType.EndsWith ||
          op.value === OperatorType.StartsWith) {
          return true;
        }
        return false;
      });
    }
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.dataEntryModel.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }

  deleteErrorMessage(element) {
    this.dataEntryModel.ErrorMessages.splice(element, 1);
  }

  onSelectBlock(i: number, blockData: BlockDefinition) {
    this.switchJumpToBlockModel.Conditions[i].BlockId = blockData.Id;
  }

  onDeleteBlock(i: number, blockData: BlockDefinition) {
    this.switchJumpToBlockModel.Conditions[i].BlockId = "-1";
  }

  onSelectErrorBlock(blockData : BlockDefinition) {
    this.dataEntryModel.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock(blockData : BlockDefinition) {
    this.dataEntryModel.ErrorBlockId = null;
  }

  onSelectPendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.dataEntryModel.PendingReplyFromCustomerBlockId = blockData.Id;
  }

  onDeletePendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.dataEntryModel.PendingReplyFromCustomerBlockId = "-1";
  }

  isCustomMessageForPendingReplyValid() {
    return str => { return this.dataEntryModel.isCustomMessageForPendingReplyValid(this.editorService) };
  }

  deleteCondition(i: number) {
    this.switchJumpToBlockModel.Conditions.splice(i, 1);
    this.generateRegex();
  }

  addCondition() {
    this.switchJumpToBlockModel.Conditions.push(new SwitchJumpToBlockCondition());
    //this.generateRegex();
  }

  generateRegex() : void {
    let text = "";
    let options = this.switchJumpToBlockModel.Conditions.map(c => c.Value);
    if (options.length > 0) {
      text += "(";
      if (trim(options[0]) !== '') {
        text += `${options[0]}|`;
      }
      for (let i = 1; i < options.length; i++) {
        if (trim(options[i]) !== '') {
          text += `${options[i]}|`;
        }
      }
      text = text.slice(0, -1); //Sacar el ultimo |
      text += ")";
    }
    this.currentTemplate = `^${text}$`;
    this.currentRegexString = `${this.translate.instant('LIST')} (${options.join('-')})`;
    this.dataEntryModel.Regex = this.currentTemplate;
    this.dataEntryModel.Template = new TemplateItem();
    this.dataEntryModel.Template.init("LIST", this.currentTemplate, options.join('\n'));
  }

  getCognitivityPriority() : boolean {
    return this.editorService.getGlobalCogntivityPriority() && this.cognitivityEnabled;
  }

  selectCommandState(state: ValidationState) {
    if (this.readOnly) {
      return;
    }
    this.validateCommandsState = state;
    switch (state) {
      case ValidationState.Always:
        this.model.dataEntryModel.CheckCommands = false;
        this.model.dataEntryModel.CheckCommandsAlways = true;
        break;
      case ValidationState.OnError:
        this.model.dataEntryModel.CheckCommands = true;
        this.model.dataEntryModel.CheckCommandsAlways = false;
        break;
      case ValidationState.Never:
        this.model.dataEntryModel.CheckCommands = false;
        this.model.dataEntryModel.CheckCommandsAlways = false;
        break;
    }
  }

  selectCognitivityState(state: ValidationState) {
    if (this.readOnly) {
      return;
    }
    this.validateCognitivityState = state;
    switch (state) {
      case ValidationState.Always:
        this.model.dataEntryModel.CheckCognitivity = false;
        this.model.dataEntryModel.CheckCognitivityAlways = true;
        break;
      case ValidationState.OnError:
        this.model.dataEntryModel.CheckCognitivity = true;
        this.model.dataEntryModel.CheckCognitivityAlways = false;
        break;
      case ValidationState.Never:
        this.model.dataEntryModel.CheckCognitivity = false;
        this.model.dataEntryModel.CheckCognitivityAlways = false;
        break;
    }
  }


}

enum ValidationState {
  Never,
  OnError,
  Always
}
