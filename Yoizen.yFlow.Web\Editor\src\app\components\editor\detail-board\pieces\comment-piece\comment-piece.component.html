<div class="card comment" >
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title" >
    <span class="fa fa-comment"></span> {{ 'PIECE_COMMENT' | translate }}
    <div class="end">
      <span class="fa fa-info-circle" 
            data-toggle="tooltip" 
            ngbTooltip="{{ shortText }}" 
            placement="top" 
            container="body" 
            tooltipClass="tooltip-trash"></span>

      <span class="fa fa-angle-down fa-light" *ngIf="!expanded" (click)="changeExpandStatus()"></span>
      <span class="fa fa-angle-up fa-light" *ngIf="expanded" (click)="changeExpandStatus()"></span>
    </div>
  </div>
  
  <div class="content">
    <div [@expanded]="expanded">
      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        <span class="description">{{ 'PIECE_COMMENT_DESCRIPTION' | translate }}</span>
      </div>
     
      <div class="text">
        <textarea [disabled]="readOnly" [(ngModel)]="model.Text" type="text" (input)="onTextareaInput($event)"></textarea>
      </div>

    </div>
  </div>
  
</div>
