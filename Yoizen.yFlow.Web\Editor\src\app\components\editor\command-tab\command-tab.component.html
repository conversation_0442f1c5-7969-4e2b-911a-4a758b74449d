<div class="commands-tab">
  <div class="commands-list">
    <div class="command">
      <div class="empty" *ngIf="commandDefinitions.length === 0" role="alert">
        <div class="alert alert-info">
          {{ 'COMMANDS_EMPTY' | translate }}
        </div>
      </div>
      <div dragula="COMMAND_DEFINITION" [(dragulaModel)]="commandDefinitions">
          <app-integration-button *ngFor="let command of commandDefinitions"
                                  (click)="selectCommand(command)"
                                  [name]="command.name"
                                  [isValid]="command.isValid()"
                                  [showActions]="!readOnly"
                                  [isSelected]="selectedCommand === command"
                                  (onDeleteElement)="onDelete(command)"
                                  (onCloneElement)="cloneCommand(command)"
                                  [showCloneAction]="!readOnly && (!isLiteVersion || (isLiteVersion && !isMaxCommands))"
                                  [deleteTooltip]="'COMMANDS_DELETE'"
                                  [cloneTooltip]="'COMMANDS_CLONE'"></app-integration-button>
      </div>
      <div class="add" (click)="createNewCommand()" *ngIf="!readOnly && (!isLiteVersion || (isLiteVersion && !isMaxCommands))">
        <span class="fa fa-plus"></span> {{ 'COMMANDS_ADD' | translate }}
      </div>
      <div class="empty" *ngIf="isLiteVersion && isMaxCommands" role="alert">
        <div class="alert alert-info">
          {{ 'COMMANDS_MAX' | translate }}
        </div>
      </div>
    </div>
  </div>
  <div class="command-detail">
    <app-command-definition *ngIf="selectedCommand!=null"
                            [readOnly]="readOnly"
                            [selectedCommand]="selectedCommand"></app-command-definition>
  </div>
</div>
