import { IIntegrationAuditPort } from "../../ports/IIntegrationAuditPort";
import { IntegrationAudit } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/IntegrationAudit";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class IntegrationAuditMock implements IIntegrationAuditPort {
    public constructor() {
        logger.info(`No se utiliza la base IntegrationsAudit`);
    }

    async connect(): Promise<void> {
        return;
    }

    async Save(integrationAudit: IntegrationAudit): Promise<IntegrationAudit> {
        return;
    }
}