FROM node:14.15.2-slim
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm install --only=production
COPY . .
EXPOSE 3000
CMD ["node", "index.js"]


FROM node:14.15.2 as builder

# Instala jq

WORKDIR /app

#Copio los repos necesarios para armar el executor
COPY Yoizen.yFlow.DniValidator ./Yoizen.yFlow.DniValidator

#Instalo las dependencias de los proyectos relacionados.
RUN cd ./Yoizen.yFlow.DniValidator && npm install

##############################################################################################
# Finalizado el build generamos la imagen final con un runtime más liviano de node
FROM node:14.15.2-slim as runtime

# Creación de directorios para despliegue de dependencias de node y aplicación
# Se crean dentro de la home del usuario "node"
# El usuario "node" no root viene en la imagen de node y evita ejecutar contenedores como root por seguridad
# https://docs.docker.com/engine/security/#linux-kernel-capabilities
RUN mkdir -p /home/<USER>/app/node_modules

# Definición del directorio de trabajo de la aplicación
WORKDIR /home/<USER>/app

# Copiamos todas las dependecias de los proyectos relacionados.
COPY --from=builder /app/Yoizen.yFlow.DniValidator/node_modules/ /home/<USER>/app/node_modules

# Copiamos el código de la aplicación al directorio dentro del contenedor.
COPY --from=builder /app/Yoizen.yFlow.DniValidator/ /home/<USER>/app

EXPOSE 3000
CMD [ "npm", "run" ,"start" ]
#CMD ["tail", "-f", "/dev/null"]