import { TemplateItem } from './../../../../../models/TemplateItem';
import { RegexHelpComponent } from '../../../popups/regex-help/regex-help.component'
import { Component, OnInit, EventEmitter } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {DataEntry, ErrorMessage, MarkAsPendingReplyFromCustomerTypes} from '../../../../../models/pieces/DataEntry';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {OperatorDefinitions, OperatorType} from 'src/app/models/OperatorType';
import {ChannelTypes} from "../../../../../models/ChannelType";
import {FlowTypes} from "../../../../../models/FlowType";
import {TranslateService} from "@ngx-translate/core";
import { FlowDefinition } from 'src/app/models/FlowDefinition';

@Component({
  selector: 'app-data-entry-piece',
  templateUrl: './data-entry-piece.component.html',
  styleUrls: ['./data-entry-piece.component.scss']
})
export class DataEntryPieceComponent extends BasePieceVM implements OnInit {
  model: DataEntry;
  variableData: VariableDefinition;
  variableTypes = TypeDefinition;
  channelTypes = ChannelTypes;
  flowTypes = FlowTypes;
  markAsPendingReplyFromCustomerTypes = MarkAsPendingReplyFromCustomerTypes;
  regexReadOnly: boolean = false;
  currentRegexFormattedTitle: string;
  validationState = ValidationState;
  validateCommandsState: ValidationState;
  validateCognitivityState: ValidationState;
  cognitivityEnabled: boolean = false;
  flow: FlowDefinition;

  constructor( editorService: EditorService, public translate: TranslateService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  getVariablesTypes() {
    return VariableDefinition.variableType;
  }

  ngOnInit() {
    this.flow = this.editorService.getCurrentFlow();
    this.model = this.context as DataEntry;
    this.variableData = this.editorService.findVariableWithId(this.model.VariableId);
    if (typeof (this.variableData) === 'undefined') {
      this.variableData = null;
    }
    this.regexReadOnly = false;
    this.currentRegexFormattedTitle = "";
    if (this.model.Template) {
      this.displayTemplate();
    }

    this.cognitivityEnabled = this.editorService.isCognitivityEnabled();

    if (this.model.CheckCommandsAlways) {
      this.validateCommandsState = ValidationState.Always;
    }
    else if (this.model.CheckCommands) {
      this.validateCommandsState = ValidationState.OnError;
    }
    else {
      this.validateCommandsState = ValidationState.Never;
    }

    if (this.model.CheckCognitivityAlways) {
      this.validateCognitivityState = ValidationState.Always;
    }
    else if (this.model.CheckCognitivity) {
      this.validateCognitivityState = ValidationState.OnError;
    }
    else {
      this.validateCognitivityState = ValidationState.Never;
    }

    if (this.flow.channel === ChannelTypes.Twitter && this.model.ErrorMessages.length < 3) {
      let index = this.model.ErrorMessages.length;
      while (index < 3) {
        this.model.ErrorMessages.push(new ErrorMessage());
        index++;
      }
    }
  }

  setVariable(variableData: VariableDefinition) {
    this.variableData = variableData;
    this.model.VariableId = variableData? variableData.Id : -1;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  onSelectPendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.model.PendingReplyFromCustomerBlockId = blockData.Id;
  }

  onDeletePendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.model.PendingReplyFromCustomerBlockId = "-1";
  }

  isCustomMessageForPendingReplyValid() {
    return str => { return this.model.isCustomMessageForPendingReplyValid(this.editorService) };
  }

  addNewText() {
    this.model.ErrorMessages.push(new ErrorMessage());
  }

  displayRegexHelp() {
    let action = new EventEmitter();
    action.subscribe((template: TemplateItem) => {
      this.model.Template = template;
      this.displayTemplate();
    });
    this.modalService.init(RegexHelpComponent, {PreviousTemplate: this.model.Template}, {ChooseTemplateAction: action});
  }

  displayTemplate() {
    this.model.Regex = this.model.Template.regex;
    this.regexReadOnly = true;
    this.currentRegexFormattedTitle = this.translate.instant(this.model.Template.key);
    if (this.model.Template.key === "NUMBER_RANGE") {
      this.currentRegexFormattedTitle += ` (${this.model.Template.state[0]} - ${this.model.Template.state[1]})`;
    } else if (this.model.Template.key === "LIST") {
      this.currentRegexFormattedTitle += ` (${this.model.Template.state.toString().split('\n').join('-')})`;
    }
  }

  clearRegex() {
    this.model.Regex = "";
    this.regexReadOnly = false;
    this.model.Template = null;
    //this.currentRegexTemplate = null;
    this.currentRegexFormattedTitle = "";
  }

  /*disableReadOnly(){
    this.regexReadOnly = false;
  }*/

  canAddTextOptions() : boolean {
    if (this.flow.channel === ChannelTypes.Twitter) {
      return this.model.ErrorMessages.length < 7;
    }

    return this.model.ErrorMessages.length < 3;
  }

  deleteErrorMessage(element) {
    this.model.ErrorMessages.splice(element, 1);
  }

  getInputVariableValidator() {
    return str => { return this.model.isVariableIdValid(this.editorService);};
  }

  getOperators() {
    if (this.model.VariableId === -1 || this.variableData === null) {
      return null;
    }

    let operators = OperatorDefinitions.Operators;
    if (this.variableData.Type === TypeDefinition.Number ||
      this.variableData.Type === TypeDefinition.Decimal ||
      this.variableData.Type === TypeDefinition.Timestamp ||
      this.variableData.Type === TypeDefinition.StringDate) {
      return operators.filter(op => {
        if (op.value === OperatorType.Equals ||
          op.value === OperatorType.NotEquals ||
          op.value === OperatorType.GreaterThan ||
          op.value === OperatorType.LessThan) {
          return true;
        }
        return false;
      });
    }
    else if (this.variableData.Type === TypeDefinition.Text) {
      return operators.filter(op => {
        if (op.value === OperatorType.Equals ||
          op.value === OperatorType.NotEquals ||
          op.value === OperatorType.DoesntContains ||
          op.value === OperatorType.Contains ||
          op.value === OperatorType.DoesntEndsWith ||
          op.value === OperatorType.DoesntStartsWith ||
          op.value === OperatorType.EndsWith ||
          op.value === OperatorType.StartsWith) {
          return true;
        }
        return false;
      });
    }
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }

  selectCommandState(state: ValidationState) {
    if (this.readOnly) {
      return;
    }
    this.validateCommandsState = state;
    switch (state) {
      case ValidationState.Always:
        this.model.CheckCommands = false;
        this.model.CheckCommandsAlways = true;
        break;
      case ValidationState.OnError:
        this.model.CheckCommands = true;
        this.model.CheckCommandsAlways = false;
        break;
      case ValidationState.Never:
        this.model.CheckCommands = false;
        this.model.CheckCommandsAlways = false;
        break;
    }
  }

  selectCognitivityState(state: ValidationState) {
    if (this.readOnly) {
      return;
    }
    this.validateCognitivityState = state;
    switch (state) {
      case ValidationState.Always:
        this.model.CheckCognitivity = false;
        this.model.CheckCognitivityAlways = true;
        break;
      case ValidationState.OnError:
        this.model.CheckCognitivity = true;
        this.model.CheckCognitivityAlways = false;
        break;
      case ValidationState.Never:
        this.model.CheckCognitivity = false;
        this.model.CheckCognitivityAlways = false;
        break;
    }
  }

  getCognitivityPriority() : boolean {
    return this.editorService.getGlobalCogntivityPriority() && this.cognitivityEnabled;
  }

  canDelete() : boolean {
    if(this.flow.channel === ChannelTypes.Twitter){
      return !this.readOnly && this.model.ErrorMessages.length > 3
    }
    return !this.readOnly && this.model.ErrorMessages.length > 1
  }
}

enum ValidationState {
  Never,
  OnError,
  Always
}
