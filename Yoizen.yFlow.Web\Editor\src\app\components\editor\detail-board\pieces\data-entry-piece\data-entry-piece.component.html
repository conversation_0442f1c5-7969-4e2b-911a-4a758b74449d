<div class="dataEntry card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-edit"></span> {{ 'CARD_DATAENTRY_TITLE' | translate }}
  </div>
  <div class="destvariable">
    <span class="title">{{ 'DATAENTRY_DESTVARIABLE' | translate }}:</span>
    <app-variable-selector-input class="input"
                                 [VariableData]="variableData"
                                 (setVariable)="setVariable($event)"
                                 [canSelectConstants]="false"
                                 [readOnly]="readOnly"
                                 [typeFilters]="[ variableTypes.Number, variableTypes.Decimal, variableTypes.Timestamp, variableTypes.StringDate, variableTypes.Text ]"
                                 [validator]="getInputVariableValidator()"></app-variable-selector-input>
  </div>
  <div class="destvariable">
    <span class="title">{{'OBFUSCATE_DATA' | translate}}:</span>
    <ui-switch [(ngModel)]="model.HideNextData" color="#45c195" size="small" defaultBgColor="#e0e0e0"
      switchColor="#ffffff"></ui-switch>
  </div>
  <div class="destvariableparseformat" *ngIf="model.VariableId !== -1 && variableData?.Type == variableTypes.StringDate">
    <span class="title">{{ 'DATAENTRY_DESTVARIABLEPARSEFORMAT' | translate }}:</span>
    <input type="text"
           class="input"
           [disabled]="readOnly"
           [(ngModel)]="model.ParseFormat"
           placeholder="{{'PARSEFORMAT_PLACEHOLDER' | translate }}"
           [ngClass]="{'invalid-input': !model.isParseFormatValid(editorService)}"
           spellcheck="false" autocomplete="off" />
  </div>
  <div class="regex" *ngIf="model.VariableId !== -1 && variableData?.Type != variableTypes.StringDate">
    <span class="title">{{ 'DATAENTRY_REGEX' | translate }}:</span>
      <div class="regex-container" [hidden]="regexReadOnly">
        <input class="input" type="text" placeholder="{{'REGEX_PLACEHOLDER' | translate }}" [(ngModel)]="model.Regex"
             [disabled]="readOnly || regexReadOnly"
             [ngClass]="{'invalid-input': !model.isRegexValid(editorService)}" spellcheck="false">
        <i class="fa fa-question-circle icon" *ngIf="!readOnly" (click)="displayRegexHelp()" data-toggle="tooltip" ngbTooltip="{{ 'HELP_REGEX' | translate }}" placement="top"
        tooltipClass="tooltip-action-row-top"></i>
      </div>
      <div class="template-container" *ngIf="regexReadOnly">
        <input class="input" type="text" [value]="currentRegexFormattedTitle" [disabled]="true">
        <i class="fa fa-trash icon" *ngIf="!readOnly" (click)="clearRegex()" data-toggle="tooltip" ngbTooltip="{{ 'CLEAR' | translate }}" placement="top"
        tooltipClass="tooltip-action-row-top"></i>
        <i class="fa fa-edit icon" *ngIf="!readOnly" (click)="displayRegexHelp()" data-toggle="tooltip" ngbTooltip="{{ 'EDIT' | translate }}" placement="top"
        tooltipClass="tooltip-action-row-top"></i>
      </div>
  </div>
  <div class="add-condition" (click)="model.HasCondition = true" *ngIf="model.VariableId !== -1 && !model.HasCondition && !readOnly">
    <span class="fa fa-plus"></span> {{ 'DATAENTRY_ADD_VALIDATION' | translate }}
  </div>
  <div class="condition" *ngIf="model.VariableId !== -1 && model.HasCondition">
    <label>{{ 'DATAENTRY_VALIDATION_INFO' | translate }}</label>
    <div class="info">
      <select class="select" name="" id=""
              [disabled]="readOnly"
              [(ngModel)]="model.Operator"
              [ngClass]="{'validation-error': !model.isOperatorValid()}">
        <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
      </select>
      <app-input-with-variables *ngIf="showOperand()"
                                class="value"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="model.SecondValue"
                                [wideInput]="true"
                                [disabled]="readOnly"
                                [ngClass]="{'validation-error': !model.isSecondValueValid()}"></app-input-with-variables>
    </div>
    <div class="trash" (click)="model.HasCondition=false" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'DATAENTRY_REMOVE_VALIDATION' | translate }}" placement="top" container="body"
         tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
  </div>
  <div class="validation" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR' | translate}}</label>
    <div class="validation-table">
      <div class="header">
        <div></div>
        <div class="center">{{ 'ENTRY_PIECE_DO_NOT_VALIDATE' | translate }}</div>
        <div class="center">{{ 'ENTRY_PIECE_VALIDATE_ON_ERROR' | translate }}</div>
        <div class="center">{{ 'ENTRY_PIECE_VALIDATE_ALWAYS' | translate }}</div>
      </div>
      <div class="row">
        <div>{{'DATA_ENTRY_COMMANDS' | translate}}
          <span class="fas fa-star" *ngIf="!getCognitivityPriority()" data-toggle="tooltip" ngbTooltip="{{ 'PRIORITY_LABEL' | translate }}"
          placement="top" container="body" tooltipClass="tooltip-star"></span>
        </div>
        <div class="center" (click)="selectCommandState(validationState.Never)">
          <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.Never"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.Never"></span>
        </div>
        <div class="center" (click)="selectCommandState(validationState.OnError)">
          <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.OnError"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.OnError"></span>
        </div>
        <div class="center" (click)="selectCommandState(validationState.Always)">
          <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState === validationState.Always"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCommandsState !== validationState.Always"></span>
        </div>
      </div>
      <div class="row" *ngIf="cognitivityEnabled">
        <div>{{'DATA_ENTRY_COGNITIVITY' | translate}}
          <span class="fas fa-star" *ngIf="getCognitivityPriority()" data-toggle="tooltip" ngbTooltip="{{ 'PRIORITY_LABEL' | translate }}"
          placement="top" container="body" tooltipClass="tooltip-star"></span>
        </div>
        <div class="center" (click)="selectCognitivityState(validationState.Never)">
          <span class="fa fa-check-circle icon-green circle-button"  [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.Never"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.Never"></span>
        </div>
        <div class="center" (click)="selectCognitivityState(validationState.OnError)">
          <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.OnError"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.OnError"></span>
        </div>
        <div class="center" (click)="selectCognitivityState(validationState.Always)">
          <span class="fa fa-check-circle icon-green circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState === validationState.Always"></span>
          <span class="fa fa-times-circle icon-red circle-button" [ngClass]="{'readonly-button': readOnly}" *ngIf="validateCognitivityState !== validationState.Always"></span>
        </div>
      </div>
    </div>
  </div>
  <div class="jumping" *ngIf="validateCognitivityState === validationState.Always && validateCommandsState === validationState.Never">
    <span class="title">{{'EVALUATE_COGNITIVITY_JUMP_TO_OTHER_BLOCK' | translate}}:</span>
    <ui-switch [(ngModel)]="model.JumpToOtherBlock" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <!--<div class="commands" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR_COMMANDS' | translate}}</label>
    <div class="command">
      <span class="title">{{'DATAENTRY_CHECKCOMMANDS' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CheckCommands" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="commands" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR_COMMANDS_ALWAYS' | translate}}</label>
    <div class="command">
      <span class="title">{{'DATAENTRY_CHECKCOMMANDS' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CheckCommandsAlways" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="cognitivities" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR_COGNITIVITY' | translate}}</label>
    <div class="cognitivity">
      <span class="title">{{'DATAENTRY_CHECKCOGNITIVITY' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CheckCognitivity" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="cognitivities" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR_COGNITIVITY_ALWAYS' | translate}}</label>
    <div class="cognitivity">
      <span class="title">{{'DATAENTRY_CHECKCOGNITIVITY' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CheckCognitivityAlways" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>-->
  <div class="validationerror" *ngIf="model.VariableId !== -1">
    <label>{{'DATAENTRY_VALIDATIONERROR_DESC' | translate}}</label>
    <div class="errormessage">
      <span class="title">{{ 'DATAENTRY_VALIDATIONERROR_MESSAGES' | translate }}:</span>
      <div class="messages">
        <app-text-list-error-message *ngFor="let text of model?.ErrorMessages let i = index"
                                     [(Text)]="text.text"
                                     [Index]="i"
                                     [CanDelete]="canDelete()"
                                     [readOnly]="readOnly"
                                     (onDelete)="deleteErrorMessage($event)"></app-text-list-error-message>
        <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
          <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
               placement="right" tooltipClass="tooltip-add">
            <span class="fa fa-plus"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="retries">
      <span class="title">{{ 'DATAENTRY_RETRIES' | translate }}:</span>
      <input class="input tries" type="number" placeholder="0" min="0"
             [disabled]="readOnly"
             [(ngModel)]="model.TryLimit" [ngClass]="{'invalid-input': !model.isTryLimitValid()}">
    </div>
  </div>
  <div class="next" *ngIf="model.VariableId !== -1">
    <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
    <app-block-picker class="input"
                      [blockId]="model.ErrorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
  <div class="mark-as-pending-reply" *ngIf="model.VariableId !== -1 && model.Channel !== channelTypes.Chat">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-exclamation-circle icon"></span>
      {{ 'DATAENTRY_MARKASPENDINGREPLY_INFO' | translate }}
    </div>
    <div class="option">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_TITLE' | translate}}:</span>
      <select class="select" [(ngModel)]="model.MarkAsPendingReplyFromCustomer" [disabled]="readOnly">
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.No">{{ 'NO' | translate }}</option>
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndJump">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_JUMP' | translate }}</option>
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndSendMessage">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_SENDMESSAGE' | translate }}</option>
      </select>
    </div>
    <div class="next" *ngIf="model.MarkAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndJump">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_BLOCK_TITLE' | translate}}:</span>
      <app-block-picker class="input"
                        [blockId]="model.PendingReplyFromCustomerBlockId"
                        (onSelectNewBlock)="onSelectPendingReplyFromCustomerBlock($event)"
                        (onDeleteBlock)="onDeletePendingReplyFromCustomerBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!model.isPendingReplyFromCustomerBlockValid(editorService)"></app-block-picker>
    </div>
    <div class="option" *ngIf="model.MarkAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndSendMessage">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MESSAGE_TITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MESSAGE' | translate"
        [(value)]="model.CustomMessageForPendingReply"
        [isTextArea]="true"
        [wideInput]="true"
        [validator]="isCustomMessageForPendingReplyValid()"
        [extendedStyles]="{'height': '100px', 'min-height': '100px', 'max-height': '100px'}"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option" *ngIf="model.MarkAsPendingReplyFromCustomer !== markAsPendingReplyFromCustomerTypes.No">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MINUTES_TITLE' | translate}}:</span>
      <input type="number"
             class="input"
             min="0"
             max="1440"
             step="10"
             spellcheck="false"
             [(ngModel)]="model.PendingReplyFromCustomerMinutes"
             [readOnly]="readOnly"
             [ngClass]="{'invalid-input': !model.isPendingReplyFromCustomerMinutesValid()}" />
    </div>
  </div>
</div>
