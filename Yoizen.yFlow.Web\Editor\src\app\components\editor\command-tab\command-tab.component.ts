import {Component, OnInit, EventEmitter, Input, OnD<PERSON>roy} from '@angular/core';
import { DragulaService } from 'ng2-dragula';
import { CommandDefinition } from 'src/app/models/commands/CommandDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { DeleteInput } from 'src/app/models/UI/DeleteInput';
import { DeleteGroupQuestionComponent } from '../popups/delete-group-question/delete-group-question.component';
import {SectionType} from "../../../models/MenuType";
import { Subscription } from 'rxjs';
import { FlowDefinition } from 'src/app/models/FlowDefinition';
import { FlowTypes } from 'src/app/models/FlowType';

@Component({
  selector: 'app-command-tab',
  templateUrl: './command-tab.component.html',
  styleUrls: ['./command-tab.component.scss']
})
export class CommandTabComponent implements OnInit, OnDestroy {
  @Input() readOnly: boolean = false;
  subs = new Subscription();
  commandDeletedDefinitions : CommandDefinition[];
  commandDefinitions: CommandDefinition[];
  filteredCommandDefinitions: CommandDefinition[];
  flow: FlowDefinition;
  isLiteVersion: boolean = false;
  isMaxCommands: boolean = false;
  get selectedCommand() : CommandDefinition {
    let state = this.editorService.getEditorState();
    if (state.SelectedTab !== SectionType.Commands) {
      return null;
    }

    return state.SelectedCommand;
  }

  constructor(private editorService : EditorService, private modalService : ModalService, private dragulaService:DragulaService) {
    this.subs.add(dragulaService.dropModel("COMMAND_DEFINITION")
      .subscribe(({ el, target, source, sourceModel, targetModel, item }) => {
        this.editorService.updateCommandDefnitions(targetModel);
      })
    );

    this.commandDeletedDefinitions = this.editorService.getCommandsDeleted();
  }

  ngOnInit() {
    this.commandDefinitions = this.editorService.getCommands();
    this.flow = this.editorService.getCurrentFlow();
    this.isLiteVersion = this.flow.type == FlowTypes.Lite;
    this.isMaxCommands = this.editorService.isMaxCommands();

    this.editorService.onMaxCommands.subscribe(isMaxCommands => {
      this.isMaxCommands = isMaxCommands;
    });

    if (!this.readOnly){
      var currentModule = this.editorService.getCurrentModule();
      if (currentModule !== undefined){
        this.readOnly = !currentModule.isMaster();
      }
    }

  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.subs.unsubscribe();
  }

  createNewCommand() {
    var newCommand = this.editorService.createCommand();
    let state = this.editorService.getEditorState();
    state.SelectedCommand = newCommand;
    //this.isMaxCommands = this.commandDefinitions.length >= 5;
  }

  selectCommand(command: CommandDefinition) {
    let state = this.editorService.getEditorState();
    state.SelectedCommand = command;
  }

  onFilter() {

  }

  onDelete(command: CommandDefinition) {
    var emmitAction = new EventEmitter();
    emmitAction.subscribe( ()=> {
      let state = this.editorService.getEditorState();
      if (typeof(state.SelectedCommand) !== 'undefined' &&
        state.SelectedCommand !== null &&
        state.SelectedCommand.id === command.id) {
        state.SelectedCommand = null;
      }

      var index = this.commandDefinitions.indexOf(command, 0);
      if (index > -1) {
        this.commandDefinitions.splice(index, 1);

        var deleted = new CommandDefinition();
        deleted.id = command.id;
        deleted.name = command.name;
        this.commandDeletedDefinitions.push(deleted);
      }
      this.editorService.updateCommandDefnitions(this.commandDefinitions);
    });


    var deleteInfo : DeleteInput = new DeleteInput();
    deleteInfo.ElementName = command.name;

    this.modalService.init(
       DeleteGroupQuestionComponent,
       { DeleteDetail: deleteInfo, Title : 'ARE_YOU_SURE_DELETE_COMMAND_QUESTION' ,
        HideAffected : true, HideConfirmation : true, deleteText : 'ACCEPT'
       }, { DeleteAction : emmitAction}
    );
  }

  cloneCommand(command:CommandDefinition) {
    this.editorService.cloneCommand(command);
    //this.isMaxCommands = this.commandDefinitions.length >= 5;
  }
}
