<div class="evaluate-cognitivity card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-robot icon"></span> {{ 'CARD_EVALUATE_COGNITIVITY_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_EVALUATE_COGNITIVITY_INFO' | translate"></span>
  </div>
  <div class="cognitivity">
    <span class="title">{{'EVALUATE_COGNITIVITY_USE_MESSAGE_TEXT' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UseMessageText" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="jumping">
    <span class="title">{{'EVALUATE_COGNITIVITY_JUMP_TO_OTHER_BLOCK' | translate}}:</span>
    <ui-switch [(ngModel)]="model.JumpToOtherBlock" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="expression" *ngIf="!model.UseMessageText">
    <span class="title">{{'EVALUATE_COGNITIVITY_EXPRESSION' | translate}}:</span>
    <app-input-with-variables placeholder="{{ '{' + '{text}' + '}' }}"
                              [(value)]="model.Expression"
                              class="input-variable-area"
                              [disabled]="readOnly"
                              [wideInput]="true"></app-input-with-variables>
  </div>
</div>
