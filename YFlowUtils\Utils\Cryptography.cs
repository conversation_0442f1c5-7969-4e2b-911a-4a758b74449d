﻿using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace YFlowUtils.Utils
{
	public class Cryptography
	{
		private RijndaelManaged myRijndael = new RijndaelManaged();
		private readonly PaddingMode padding = PaddingMode.ANSIX923;
		private readonly CipherMode mode = CipherMode.CBC;
		private readonly int iterations = 100;
		private static readonly byte[] Salt = new System.Text.UTF8Encoding().GetBytes("Y01z3n2020*");
		private byte[] GenerateKey(string strPassword)
		{
			var keyGenerator = new Rfc2898DeriveBytes(strPassword, Salt, iterations);
			return keyGenerator.GetBytes(32);
		}

		private byte[] GenerateIv(string strPassword)
		{
			var keyGenerator = new Rfc2898DeriveBytes(strPassword, Salt, iterations);
			return keyGenerator.GetBytes(16);
		}

		/// <summary>
		/// Clase para encriptar y desencriptar
		/// </summary>
		/// <param name="strPassword">Contraseña</param>
		/// <param name="iv">IV</param>
		public Cryptography(string strPassword, string iv)
		{
			byte[] key = GenerateKey(strPassword);
			myRijndael.IV = GenerateIv(iv);

			myRijndael.Padding = padding;
			myRijndael.Mode = mode;
			myRijndael.Key = key;
		}

		/// <summary>
		/// Encriptar
		/// </summary>
		/// <param name="strPlainText">frase para encriptar</param>
		/// <returns>Encriptación en hexa</returns>
		public string Encrypt(string strPlainText)
		{
			byte[] strText = new System.Text.UTF8Encoding().GetBytes(strPlainText);
			ICryptoTransform transform = myRijndael.CreateEncryptor();
			byte[] cipherText = transform.TransformFinalBlock(strText, 0, strText.Length);

			return BitConverter.ToString(cipherText).Replace("-", "");
		}

		public string Decrypt(string encryptedText)
		{
			byte[] encryptedBytes = StringToByteArray(encryptedText);
			var decryptor = myRijndael.CreateDecryptor(myRijndael.Key, myRijndael.IV);
			byte[] originalBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

			return Encoding.UTF8.GetString(originalBytes);
		}

		private byte[] StringToByteArray(string hex)
		{
			return Enumerable.Range(0, hex.Length)
							 .Where(x => x % 2 == 0)
							 .Select(x => Convert.ToByte(hex.Substring(x, 2), 16))
							 .ToArray();
		}
	}
}
