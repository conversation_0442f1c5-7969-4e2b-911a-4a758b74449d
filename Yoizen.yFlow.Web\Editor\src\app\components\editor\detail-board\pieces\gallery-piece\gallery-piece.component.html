<div class="gallery card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-images"></span> {{ 'CARD_GALLERY_TITLE' | translate }}
  </div>
  <div class="item-container">
    <div class="item" *ngFor="let galleryImage of model.images; index as i;">
      <div class="trash" (click)="removeImage(galleryImage)"
           *ngIf="model.images !== null && model.images.length > 1 && !readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'CARD_GALLERY_DELETE_ITEM' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
      <div class="reorder left" (click)="moveLeft(i); $event.stopPropagation();" *ngIf="i > 0 && !readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_LEFT' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-caret-left"></span>
      </div>
      <div class="reorder right" (click)="moveRight(i); $event.stopPropagation();" *ngIf="i < (model.images.length - 1) && !readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_RIGHT' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-caret-right"></span>
      </div>
      <div class="image" [ngClass]="{ 'showImg': isStringValid(galleryImage.imageUrl)}">
        <img [src]="galleryImage.imageUrl"
             *ngIf="isStringValid(galleryImage.imageUrl)" />
        <span class="fa fa-camera"></span>
      </div>
      <ul>
        <li>
          <app-input-with-variables
            [placeholder]="'IMAGE_URL' | translate"
            [(value)]="galleryImage.imageUrl"
            [validator]="isUrlValid"
            [wideInput]="true"
            [disabled]="readOnly"
            spellcheck="false">
          </app-input-with-variables>
        </li>
        <li>
          <app-input-with-variables
            [placeholder]="'TITLE' | translate"
            [validator]="isStringValid"
            [(value)]="galleryImage.title"
            [wideInput]="true"
            [disabled]="readOnly">
          </app-input-with-variables>
        </li>
        <li>
          <app-input-with-variables
            [placeholder]="'SUBTITLE' | translate"
            [(value)]="galleryImage.subtitle"
            [wideInput]="true"
            [disabled]="readOnly">
          </app-input-with-variables>
        </li>
        <li *ngIf="model.Channel !== channelTypes.GoogleRBM">
          <app-input-with-variables
            [placeholder]="'ACTION_URL' | translate"
            [(value)]="galleryImage.targetUrl"
            [validator]="isUrlValid"
            [wideInput]="true"
            spellcheck="false"
            [disabled]="readOnly">
          </app-input-with-variables>
        </li>
      </ul>
      <app-button-element *ngFor="let button of galleryImage.buttons let i = index"
                          [Model]="button" [Index]="i" (onDelete)="deleteButtonElement(galleryImage, $event)"
                          (onShowDetail)="onShowButtonDetail($event)"
                          [readOnly]="readOnly"
                          [expandedBtn]="expandButton">
      </app-button-element>
      <div class="addButton" (click)="addButton(galleryImage)" *ngIf="canAddButton(galleryImage)">
        <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
      </div>
    </div>
    <div class="addItem" *ngIf="canAddImage()">
      <div data-toggle="tooltip" (click)="addImage()"
           ngbTooltip="{{ 'CARD_GALLERY_ADD_ITEM' | translate }}" placement="right" tooltipClass="tooltip-add">
        <span class="fa fa-plus"></span>
      </div>
    </div>
  </div>
  <div class="addQuick" *ngIf="canCreateQuickReply()" (click)="addQuickReplyPiece()">
    <span class="fa fa-plus"></span> {{ 'QUICKREPLY_ADD' | translate }}
  </div>
</div>
