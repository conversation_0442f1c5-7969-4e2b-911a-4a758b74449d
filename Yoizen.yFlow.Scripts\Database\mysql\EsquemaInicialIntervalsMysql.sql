DELIMITER $$
CREATE PROCEDURE `daily_createtable`(the_interval VARCHAR(4), the_interval_day VARCHAR(10))
BEGIN
	SET @the_interval_text = the_interval;
    SET @the_interval_day_text = the_interval_day;
    
	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_', @the_interval_day_text, '_', @the_interval_text, ' (
	`date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `data` TEXT,
    `new_cases` INT,
    `transferred` INT,
    `closed_by_yflow` INT,
    `new_messages` INT,
    `hsm_case` INT,
    `case_abandoned` INT,
    `monthly_users` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_abandoned_cases_', @the_interval_day_text, '_',  @the_interval_text, ' (
	`date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `channel` VARCHAR(10) NOT NULL,
    `block_id` VARCHAR(100),
    `data` TEXT,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_blocks_', @the_interval_day_text, '_',  @the_interval_text, ' (
	`date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `block_id` VARCHAR(100) NOT NULL,
    `channel` VARCHAR(255) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_blocks_sequence_', @the_interval_day_text, '_',  @the_interval_text, ' (
	`date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `source_block_id` VARCHAR(100),
    `dest_block_id` VARCHAR(100),
    `type` INT NOT NULL,
    `channel` VARCHAR(10) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_commands_', @the_interval_day_text, '_',  @the_interval_text, ' (
  `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
  `flow_id` int NOT NULL,
  `command_id` int NOT NULL,
  `channel` VARCHAR(255) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_default_answers_', @the_interval_day_text, '_',  @the_interval_text, ' (
    `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `block_id` INT NOT NULL,
    `channel` VARCHAR(255) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_derivation_key_', @the_interval_day_text, '_',  @the_interval_text, ' (
  `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `derivation_key` VARCHAR(255) NOT NULL,
    `channel` VARCHAR(255) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_flow_', @the_interval_day_text, '_',  @the_interval_text, ' (
  `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `channel` VARCHAR(255) NOT NULL,
    `data` TEXT,
    `new_cases` INT,
    `transferred` INT,
    `closed_by_yflow` INT,
    `new_messages` INT,
    `hsm_case` INT,
    `case_abandoned` INT,
    `monthly_users` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_group_', @the_interval_day_text, '_',  @the_interval_text, ' (
      `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `group_id` VARCHAR(50) NOT NULL,
    `channel` VARCHAR(10) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_group_sequence_', @the_interval_day_text, '_',  @the_interval_text, ' (
  `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `source_group_id` VARCHAR(50),
    `dest_group_id` VARCHAR(50),
    `type` INT NOT NULL,
    `channel` VARCHAR(10) NOT NULL,
    `total` INT,
    `version` INT
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_integrations_', @the_interval_day_text, '_',  @the_interval_text, ' (
 `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `integration_id` INT NOT NULL,
    `channel` VARCHAR(255) NOT NULL,
    `total` INT,
    `error` INT,
    `version` INT,
    `total_response_time` DECIMAL(18, 2) NOT NULL,
    `error_response_time` DECIMAL(18, 2) NOT NULL
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;

	SET @create_table_statement = CONCAT('CREATE TABLE IF NOT EXISTS daily_statistic_event_', @the_interval_day_text, '_',  @the_interval_text, ' (
  `date` DATETIME(3) NOT NULL,
    `interval` INT NOT NULL,
    `interval_datetime` DATETIME(3) NOT NULL,
    `flow_id` INT NOT NULL,
    `statistic_event_id` INT NOT NULL,
    `channel` VARCHAR(10) NOT NULL,
    `total` INT,
    `version` INT,
    `block_id` VARCHAR(100)
    `block_group_id` VARCHAR(100)
) ENGINE=InnoDB;');

	PREPARE create_table_query FROM @create_table_statement;
	EXECUTE create_table_query;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_createtables`(the_interval_day VARCHAR(10), the_interval VARCHAR(10))
BEGIN
	SET @the_interval = the_interval;
	SET @the_interval_day = the_interval_day;
    
    REPEAT
      SET @the_interval_text = LPAD(@the_interval, 4, '0');
          
      CALL daily_createtable(@the_interval_text, @the_interval_day);
          
      IF MOD(@the_interval, 100) = 0 THEN
        SET @the_interval = @the_interval + 30;
      ELSE
        SET @the_interval = @the_interval + 70;
      END IF;
    UNTIL @the_interval >= 2400 END REPEAT;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_droptable`(the_interval VARCHAR(4))
BEGIN
	SET @the_interval_text = the_interval;
    
	IF the_interval = 'day' THEN
		DROP TABLE IF EXISTS daily_day;
    ELSE
		SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;
        
		SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS abandoned_cases_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_blocks_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_blocks_sequence_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_commands_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_default_answers_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_derivation_key_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_flow_group_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_group_sequence_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_integrations_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

    SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_statistic_event_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

		SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS detailed_statistic_event_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

		SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS detailed_default_answer_', @the_interval_day_text, '_',  @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;

	END IF;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_droptables`()
BEGIN
	SET @the_interval = 0;
    
    REPEAT
		SET @the_interval_text = LPAD(@the_interval, 4, '0');
        
        SET @drop_table_statement = CONCAT('DROP TABLE IF EXISTS daily_', @the_interval_text, ';');

		PREPARE drop_table_query FROM @drop_table_statement;
		EXECUTE drop_table_query;
        
        IF MOD(@the_interval, 100) = 0 THEN
			SET @the_interval = @the_interval + 30;
		ELSE
			SET @the_interval = @the_interval + 70;
		END IF;
	UNTIL @the_interval >= 2400 END REPEAT;
    
    DROP TABLE IF EXISTS daily_day;
END$$
DELIMITER ;

/*DELIMITER $$
CREATE EVENT daily_createtables_job
ON SCHEDULE
    EVERY 1 DAY
    STARTS CURRENT_TIMESTAMP
    DO
      BEGIN
        DECLARE the_interval_day VARCHAR(10);
        SET the_interval_day = REPLACE(DATE_FORMAT(CURDATE(), '%Y-%m-%d'), '-', '');
        CALL daily_createtables(the_interval_day);
      END$$
DELIMITER ;
*/

DELIMITER $$
CREATE PROCEDURE `daily_abandoned_cases_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, channel, version FROM daily_abandoned_cases_' , intervalDatetime, ' group by flow_id, block_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_blocks_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, channel, version FROM daily_blocks_' , intervalDatetime, ' group by flow_id, block_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_blocks_sequence_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, source_block_id AS sourceBlockId, dest_block_id AS destBlockId, channel, type, version FROM daily_blocks_sequence_' , intervalDatetime, ' group by flow_id, source_block_id, dest_block_id, channel, type, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(new_cases) AS newCase, SUM(transferred) AS transferredToYSocial, SUM(closed_by_yflow) AS closedByYFlow, SUM(new_messages) AS newMessage, SUM(hsm_case) AS hsmCase, SUM(case_abandoned) AS caseAbandoned, SUM(monthly_users) AS monthlyUsers FROM daily_' , intervalDatetime);
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_commands_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, command_id AS commandId, channel, version FROM daily_commands_' , intervalDatetime, ' group by flow_id, command_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_default_answers_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, channel, version FROM daily_default_answers_' , intervalDatetime, ' group by flow_id, block_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_derivation_key_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, derivation_key AS derivationKey, channel, version FROM daily_derivation_key_' , intervalDatetime, ' group by flow_id, derivation_key, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_flow_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(new_cases) AS newCase, SUM(transferred) AS transferredToYSocial, SUM(closed_by_yflow) AS closedByYFlow, SUM(new_messages) AS newMessage, SUM(hsm_case) AS hsmCase, SUM(case_abandoned) AS caseAbandoned, SUM(monthly_users) AS monthlyUsers, MAX(channel) AS channel, flow_id AS flowId FROM daily_flow_' , intervalDatetime, ' group by flow_id');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_group_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, group_id AS groupId, channel, version FROM daily_group_' , intervalDatetime, ' group by flow_id, group_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_group_sequence_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, source_group_id AS sourceGroupId, dest_group_id AS destGroupId, channel, type, version FROM daily_group_sequence_' , intervalDatetime, ' group by flow_id, source_group_id, dest_group_id, channel, type, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_integrations_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, SUM(error) AS error, SUM(total_response_time) AS totalResponseTime, SUM(error_response_time) AS errorResponseTime, flow_id AS flowId, integration_id AS integrationId, channel, version FROM daily_integrations_' , intervalDatetime, ' group by flow_id, integration_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE `daily_statistic_event_calculate`(IN intervalDatetime VARCHAR(100))
BEGIN
	SET @query = CONCAT('SELECT SUM(total) AS total, flow_id AS flowId, statistic_event_id AS statisticEventId, block_id AS blockId, channel, block_group_id as blockGroupId, version FROM daily_statistic_event_' , intervalDatetime, ' group by flow_id, statistic_event_id, block_id, block_group_id, channel, version');
	PREPARE stmt FROM @query;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;