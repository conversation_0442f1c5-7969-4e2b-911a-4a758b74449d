﻿<div class="button" [ngClass]="{ 'persistent': IsPersistentMenuButton, 'with-condition': Model.HasVisibilityCondition }" (clickOutside)="onClose.emit()" [delayClickOutsideInit]="true">
  <div>
    <div class="name">
      <input #nameInput autofocus class="input" type="text" [disabled]="readOnly"
             placeholder="{{'BUTTON_TEXT_INPUT' | translate}}" [ngClass]="{'invalid-input': !isStringValid(Model.Text)}"
             [(ngModel)]="Model.Text" />
    </div>
    <div class="add-condition visibility" (click)="Model.HasVisibilityCondition = true; $event.stopPropagation();"
         *ngIf="!Model.HasVisibilityCondition && !IsPersistentMenuButton && !readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_VISIBILITY_CONDITION' | translate }}
    </div>
    <div class="data visibility" *ngIf="Model.HasVisibilityCondition && !IsPersistentMenuButton">
      <span class="title">{{ 'BUTTON_VISIBILITY_CONDITION' | translate}}</span>
      <app-input-with-variables class="condition"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="Model.FirstOperand"
                                [disabled]="readOnly"
                                [validator]="getFirstValidator()"></app-input-with-variables>
      <select class="select" [(ngModel)]="Model.Operator" [ngClass]="{'validation-error': !Model.isOperatorValid()}" [disabled]="readOnly">
        <option [ngValue]="null">{{ 'Operando' | translate}}</option>
        <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
      </select>
      <app-input-with-variables class="condition" *ngIf="showOperand()"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="Model.SecondOperand"
                                [disabled]="readOnly"
                                [validator]="getSecondValidator()"></app-input-with-variables>
      <div class="trash" (click)="Model.HasVisibilityCondition=false; $event.stopPropagation();"
           *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'BUTTON_VISIBILITY_CONDITION_DELETE_TEXT' | translate }}"
           placement="top" container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>
    <div class="tabs">
      <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)" (clickOutside)="hideBlockSelector = true">
        <ngb-tab id="tab-redirect-to-block" *ngIf="isButtonTypeAllowed('redirect')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-table"></span> {{ 'BUTTON_BLOCK' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="block">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
                  <input #blockInput LimitLength
                         class="input"
                         type="text"
                         placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         autocomplete="nopongasninguno"
                         spellcheck="false"
                         [(ngModel)]="SearchBlockString"
                         (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
            <div class="add-condition condition" (click)="Model.AssignValueToVariable = true; $event.stopPropagation();"
                 *ngIf="!Model.AssignValueToVariable && !IsPersistentMenuButton && !readOnly">
              <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO' | translate }}
            </div>
            <div class="data condition" *ngIf="Model.AssignValueToVariable && !IsPersistentMenuButton">
              <div class="input-row">
                <label class="field-label">{{'EXPRESION' | translate}}:</label>
                <app-input-with-variables [placeholder]="'VALUE' | translate"
                                          [(value)]="Model.AssignValue"
                                          class="expresion-input"
                                          [wideInput]="true"
                                          [disabled]="readOnly"
                                          [validator]="getVariableValidator()"></app-input-with-variables>
              </div>
              <div class="input-row">
                <label class="field-label">{{'SAVE_INTO' | translate}}:</label>
                <app-variable-selector-input [VariableData]="curretVariable"
                                             (setVariable)="setVariableOnOutput($event)"
                                             [validator]="getAssignVariableIdValid()"
                                             [readOnly]="readOnly"
                                             class="expresion-input"></app-variable-selector-input>
              </div>
              <div class="trash" (click)="Model.AssignValueToVariable=false; $event.stopPropagation();"
                   *ngIf="!readOnly"
                   data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO_DELETE_TEXT' | translate }}"
                   placement="top" container="body" tooltipClass="tooltip-trash">
                <span class="fa fa-trash-alt"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-text" *ngIf="isButtonTypeAllowed('text')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-text"></span> {{ 'BUTTON_TEXT' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_TEXT_DESCRIPTION' | translate }}
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-location" *ngIf="isButtonTypeAllowed('location')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-location-arrow"></span> {{ 'BUTTON_LOCATION' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert" *ngIf="!AllowBlockSelectionInLocationButton">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_LOCATION_DESCRIPTION' | translate }}
              </div>
            </div>
            <div class="block" *ngIf="AllowBlockSelectionInLocationButton">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
                  <input #blockInput LimitLength
                         class="input"
                         type="text"
                         placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         autocomplete="nopongasninguno"
                         spellcheck="false"
                         [(ngModel)]="SearchBlockString"
                         (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-locationwithmap" *ngIf="isButtonTypeAllowed('location_with_map')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-map-marker"></span> {{ 'BUTTON_LOCATION_WITH_MAP' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert" *ngIf="!AllowBlockSelectionInLocationWithMapButton">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_LOCATION_WITH_MAP_DESCRIPTION' | translate }}
              </div>
            </div>
            <div class="block" *ngIf="AllowBlockSelectionInLocationWithMapButton">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
                  <input #blockInput LimitLength
                         class="input"
                         type="text"
                         placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         autocomplete="nopongasninguno"
                         spellcheck="false"
                         [(ngModel)]="SearchBlockString"
                         (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-userphonenumber" *ngIf="isButtonTypeAllowed('user_phone_number')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-phone"></span> {{ 'BUTTON_USERPHONENUMBER' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_USERPHONENUMBER_DESCRIPTION' | translate }}
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-useremail" *ngIf="isButtonTypeAllowed('user_email')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-at"></span> {{ 'BUTTON_USERMEAIL' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_USERMEAIL_DESCRIPTION' | translate }}
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-url-input" *ngIf="isButtonTypeAllowed('url')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-link"></span> {{ 'BUTTON_URL' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="data url">
              <div class="input-row">
                <label class="field-label">{{'INPUT_URL' | translate}}:</label>
                <app-input-with-variables class="expresion-input"
                                          [placeholder]="'INPUT_URL' | translate"
                                          [(value)]="Model.Url"
                                          [validator]="isValidURL"
                                          [wideInput]="true"
                                          [disabled]="readOnly"></app-input-with-variables>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
      </ngb-tabset>
    </div>
    <div class="close-button" (click)="onClose.emit()">
      <span class="fa fa-window-close"></span>
    </div>
  </div>
</div>
