import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";

@Component({
  selector: 'app-rich-link-piece',
  templateUrl: './rich-link-piece.component.html',
  styleUrls: ['./rich-link-piece.component.scss']
})
export class RichLinkPieceComponent extends BasePieceVM implements OnInit {

  model : RichLinkPiece;
  richLinkTypes = RichLinkTypes;
  loading: boolean = false;

  constructor( editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as RichLinkPiece;
  }

  tryToRetrieveOpenGraph() {
    if (!containsVariables(this.model.url) &&
      !containsExpressions(this.model.url) &&
      isUrlValid(this.model.url)) {

      this.serverService.getOpenGraphInfoFromUrl(this.model.url)
        .pipe(finalize(() => {
          this.loading = false;
        }))
        .subscribe((response: StatusResponse) => {
            console.log(response.data);
            this.loading = false;

            this.model.title = response.data.title;
            this.model.image.url = response.data.image;
          },
          error => {
            this.loading = false;
            var errorDesc: any = {};
            if (error.status == 403) {
              errorDesc.Title = 'CANNOT_DELETE_FLOW_TITLE';
              errorDesc.Desc = error.error.code == 1002 ? 'CANNOT_DELETE_FLOW_DESC' : 'CANNOT_DELETE_FLOW_USED_DESC';
            }
            this.modalService.init(ErrorPopupComponent, errorDesc, {});
          });
    }

  }
}
