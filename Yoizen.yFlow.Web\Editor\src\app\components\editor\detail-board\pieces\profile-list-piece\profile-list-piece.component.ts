import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from 'src/app/services/editor.service';
import { ProfileListActionTypes, ProfileListData, ProfileListPiece } from 'src/app/models/pieces/ProfileListPiece';
import { ModalService } from 'src/app/services/Tools/ModalService';

@Component({
  selector: 'app-profile-list-piece',
  templateUrl: './profile-list-piece.component.html',
  styleUrls: ['./profile-list-piece.component.scss']
})
export class ProfileListPieceComponent extends BasePieceVM implements OnInit {
  model: ProfileListPiece;
  profileListActionTypes = ProfileListActionTypes;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as ProfileListPiece;
    if (!this.model) {
      this.model = new ProfileListPiece();
    }
  }

  isValid(): boolean {
    return this.model.isValid(this.editorService);
  }

  isKeyValid(key: string): boolean {
    return key !== null && key.trim() !== '';
  }

  isActionValid(action: ProfileListActionTypes): boolean {
    return action !== null && Object.values(ProfileListActionTypes).includes(action);
  }

  deleteProfileListData(index: number) {
    if (this.model.ProfileListData.length > 1) {
      this.model.ProfileListData.splice(index, 1);
    }
  }

  addProfileListData() {
    const initialData = new ProfileListData();
    initialData.Action = ProfileListActionTypes.Add;
    this.model.ProfileListData.push(initialData);
  }
}
