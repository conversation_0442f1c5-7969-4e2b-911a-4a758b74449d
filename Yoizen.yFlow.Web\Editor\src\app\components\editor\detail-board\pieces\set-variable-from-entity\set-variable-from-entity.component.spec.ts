import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SetVariableFromEntityComponent } from './set-variable-from-entity.component';

describe('SetVariableFromEntityComponent', () => {
  let component: SetVariableFromEntityComponent;
  let fixture: ComponentFixture<SetVariableFromEntityComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SetVariableFromEntityComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SetVariableFromEntityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
