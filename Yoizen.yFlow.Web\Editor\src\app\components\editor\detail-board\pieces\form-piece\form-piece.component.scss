@import "_variables";
@import "_mixins";

.form {
  min-width: 350px;
  max-width: 800px;

  @media screen and (min-width: 1400px) {
    max-width: 1200px;
  }

  .option {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    align-items: center;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables, .input {
      flex-grow: 1;
      flex-shrink: 1;
    }

    &.with-info {
      .info {
        margin-left: 3px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .contents {
    margin-top: 5px;

    .pages {
      margin-top: 5px;

      & > .title {
        font-family: $fontFamilyTitles;
        font-size: 1.4em;
        font-weight: bold;
      }

      & > .info {
        font-style: italic;
        color: #aaaaaa;
      }

      .pages-container {
        flex-direction: row;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        margin-top: 5px;
        overflow-x: auto;
        padding-top: 15px;

        @include scrollbar;

        .page {
          min-width: 320px;
          margin-bottom: 20px;
          background-color: #fff;
          display: inline-block;
          margin-right: 20px;
          position: relative;
          height: max-content;
          border: 1px solid $cardSeparatorBorderColor;
          border-radius: 7px;

          &.input {
            min-width: 350px;
          }

          &.invalid-page {
            border-color: red;
          }

          .trash {
            @include trash;
            position: absolute;
            right: -16px;
            top: -16px;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }

          .reorder {
            position: absolute;
            top: -16px;
            color: #bbb;
            cursor: pointer;
            opacity: 0;

            &.left {
              right: calc(50% + 10px);
            }

            &.right {
              right: calc(50% - 10px);
            }

            &:hover {
              color: #555;
            }
          }

          &:hover {
            .trash, .reorder {
              @include trashOver;
            }
          }
        }

        .addItem {
          @include addButton;
        }
      }
    }

    .page {
      border: 1px solid $sidebarBorderColor;
      padding: 5px;

      & > .title {
        font-size: 120%;
        font-weight: bold;
        margin-bottom: 5px;
      }

      &.splash {
        width: 300px;
      }
    }
  }
}
