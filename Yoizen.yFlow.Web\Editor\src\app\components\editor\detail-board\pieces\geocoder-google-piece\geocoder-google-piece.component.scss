@import '_variables';
@import '_mixins';

.geocoder-google{
	width: 550px;
	background-color: #fff;

  .latitude, .longitude, .indexArray, .street, .department, .street-number, .locality, .sublocality, .province, .country, .zip-code, .zip-code-suffix, .address, .object, .place-id, .next{
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;
    margin-left: 10px;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }

    .input {
      flex-grow: 1;
      flex-shrink: 1;
    }
    .indexArray {
      flex-grow: initial;
      flex-shrink: initial;
      width: 80px;
    }
  }

  .border-bottom{
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
