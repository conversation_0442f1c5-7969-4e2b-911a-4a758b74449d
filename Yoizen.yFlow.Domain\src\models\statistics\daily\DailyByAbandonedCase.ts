import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByAbandonedCase extends DailyBase {
    flowId: number;
    channel: string;
    blockId: string;
    total: number;
    version: number;
    data: { messages: any[]; };

    constructor(datetime: Moment, data?: { flowId: number, channel: string, blockId: string, version: number }) {
        super(datetime);
        if (data) {
            this.flowId = data.flowId;
            this.channel = data.channel;
            this.blockId = data.blockId;
            this.version = data.version;
        }
        this.total = 0;
        this.data = {
            messages: []
        }
    }

    getType() {
        return 'daily_abandoned_cases';
    }

    type() {
        return DailyInfoTypes.AbandonedCases;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "block_id", "channel", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.blockId, this.channel, this.version];
    }

    setMessage(caseId, msgId, msgBody) {
        this.data.messages.push(`${caseId};${msgId};${msgBody}`);
        this.total++;
    }

}
