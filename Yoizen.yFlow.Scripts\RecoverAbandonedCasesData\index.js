const sql = require('mssql');

// Configuración para la primera base de datos
const configYSocial = {
    user: 'clarocodbuser',
    password: '<PERSON><PERSON><PERSON><PERSON>!2018.',
    server: '172.191.181.81',
    database: 'ClarocoDB',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        requestTimeout: 300000,
        connectionTimeout: 300000
    }
};

const configYFlow = {
    user: 'clarocoyFlowuser',
    password: 'Ma<PERSON><PERSON><PERSON>!2018.',
    server: '10.0.0.36',
    database: 'ClarocoyFlowDB',
    port: 1433,
    options: {
        encrypt: false,
        trustServerCertificate: true,
        requestTimeout: 300000,
        connectionTimeout: 300000
    }
};

const executeGlobal = parseInt(process.argv[2], 10);
const ServiceId = parseInt(process.argv[3], 10);
const FlowId = parseInt(process.argv[4], 10);

if (isNaN(executeGlobal) || isNaN(ServiceId) || isNaN(FlowId)) {
    console.log('Por favor, proporciona todos los parámetros correctamente: executeGlobal, ServiceId, FlowId');
    process.exit(1);
}

async function recoverMauGlobal() {
    try {
        const queryYSocial = `SELECT * from AprilAbandonedCasesClaroDBGlobal ORDER BY dayNumber, hourOfDay, halfHour`;
        //const queryYFlow = `INSERT INTO history_daily (date, interval, interval_datetime, case_abandoned) VALUES (@date, @interval, @interval_datetime, @case_abandoned)`;
        
        console.log("Intentando conectarme")
        let poolYSocial = await sql.connect(configYSocial);
        console.log("Me conecte")

        let result = await poolYSocial.request().query(queryYSocial); 
        let data = result.recordset;
        console.log('Datos obtenidos de ySocial');
        await sql.close();
        
        console.log("Intentando conectarme a YFlow")
        let poolYFlow = await sql.connect(configYFlow);
        console.log("Me conecte a YFlow")

        for (let row of data) {
            let queryYFlow = `UPDATE history_daily SET date = @date, interval = @interval, interval_datetime = @interval_datetime, case_abandoned = @case_abandoned WHERE FORMAT(date, 'yyyy-MM-dd') = FORMAT(@date, 'yyyy-MM-dd') AND interval = @interval`;

            let halfHourValue = row.halfHour == 1 ? 30 : 0;
            let interval = (row.hourOfDay * 100) + halfHourValue;
            let intervalDatetime = getIntervalDatetime(2024, 4, row.dayNumber, row.hourOfDay, row.halfHour);
            await poolYFlow.request()
                .input('date', sql.DateTime2, intervalDatetime)
                .input('interval', sql.Int, interval)
                .input('interval_datetime', sql.DateTime2, intervalDatetime)
                .input('case_abandoned', sql.Int, row.TotalCLosedCasePerHalfHour)
                .query(queryYFlow);
        }

        console.log('Los datos fueron insertados en yFlow');
        await sql.close();
    } catch (err) {
        console.error('Error al transferir datos:', err);
    }
}

// Todo: Revisar con Maxi
async function recoverMauByService(serviceId, flowId) {
    try {
        const queryYSocial = `SELECT * from AprilAbandonedCasesClaroDB WHERE ServiceID = ${serviceId} ORDER BY dayNumber, hourOfDay, halfHour`;
        let poolYSocial = await sql.connect(configYSocial);
        let result = await poolYSocial.request().query(queryYSocial); 
        let data = result.recordset;
        console.log('Datos obtenidos de ySocial:', data);
        await sql.close();

        console.log("Intentando conectarme a YFlow")
        let poolYFlow = await sql.connect(configYFlow);
        console.log("Me conecte a YFlow")

        for (let row of data) {
            //let queryYFlow = `INSERT INTO abandoned_case (date, interval, total, block_id, flow_id, channel) VALUES (@date, @interval, @total, 1, @flow_id, @channel)`;
        
            let halfHourValue = row.halfHour == 1 ? 30 : 0;
            let interval = (row.hourOfDay * 100) + halfHourValue;
            let intervalDatetime = getIntervalDatetime(2024, 4, row.dayNumber, row.hourOfDay, row.halfHour);
            let queryYFlow = `UPDATE history_daily_by_flow SET date = @date, interval = @interval, total = @total, flow_id = @flow_id, channel = @channel WHERE FORMAT(date, 'yyyy-MM-dd') = FORMAT(@date, 'yyyy-MM-dd') AND interval = @interval AND flow_id = @flow_id`;
            await poolYFlow.request()
                .input('date', sql.DateTime2, intervalDatetime)
                .input('interval', sql.Int, interval)
                .input('channel', sql.VarChar, '16')
                .input('total', sql.Int, row.TotalClosedPerHalfHour)
                .input('flow_id', sql.Int, flowId)
                .query(queryYFlow);
        }

        console.log('Los datos fueron insertados en yFlow');
        await sql.close();
    } catch (err) {
        console.error('Error al transferir datos:', err);
    }
}

function getIntervalDatetime(year, month, day, hour, half) {
    let minutes = half ? 30 : 0;
    let date = new Date(year, month - 1, day, hour, minutes, 0);
    return date;
}

async function main() {
    if (executeGlobal == 1) {
        await recoverMauGlobal();
    } else {
        await recoverMauByService(ServiceId, FlowId);
    }
}

main();