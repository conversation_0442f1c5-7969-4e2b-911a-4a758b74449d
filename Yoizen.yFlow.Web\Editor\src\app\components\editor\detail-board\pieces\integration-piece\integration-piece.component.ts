import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { IntegrationPiece, InputVariableMap, OutputVariableMap } from '../../../../../models/pieces/IntegrationPiece';
import {Integration, OutputType} from '../../../../../models/integration/Integration';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { isStringValid } from 'src/app/urlutils.module';
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {OperatorDefinitions} from "../../../../../models/OperatorType";


@Component({
  selector: 'app-integration-piece',
  templateUrl: './integration-piece.component.html',
  styleUrls: ['./integration-piece.component.scss']
})
export class IntegrationPieceComponent extends BasePieceVM implements OnInit {

  @ViewChild('integrationDropdown', { static: false }) integrationDropdown : ElementRef;

  getVariablesTypes() {
    return VariableDefinition.variableType;
  }

  model : IntegrationPiece;
  integrationDetail : Integration;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as IntegrationPiece;
  }

  getAvailableIntegrations() {
    return this.editorService.getIntegrations();
  }

  OutputValidator(){
    return false;
  }

  onSelectIntegration(id : number) {
    if( this.integrationDetail != null && this.model.integrationId === this.integrationDetail.id) {
      return;
    }
    const integrations = this.getAvailableIntegrations();
    this.integrationDetail = null;
    integrations.forEach( integration => {
      if(integration.id === this.model.integrationId) {
        this.integrationDetail = integration;
      }
    });
    this.regeneratePieceInfo();
  }

  regeneratePieceInfo() {
    this.model.inputs = [];
    this.model.outputs = [];
    if (this.integrationDetail != null) {
      this.integrationDetail.inputs.forEach( input => {
        let inputMap = new InputVariableMap();
        inputMap.integrationVriableId = input.Id;
        inputMap.integrationVariableName = input.Name;
        inputMap.type = input.Type;
        inputMap.isMasked = input.IsMasked;
        this.model.inputs.push(inputMap)
      });

      if (this.integrationDetail.outputType === OutputType.Json) {
        this.integrationDetail.outputFields.forEach(output => {
          let outputMap = new OutputVariableMap();
          outputMap.integrationVariableName = output.name;
          outputMap.type = output.type;
          outputMap.isMasked = output.IsMasked;
          this.model.outputs.push(outputMap);
        });
      }
      else {
        this.integrationDetail.fileOutputFields.forEach(output => {
          let outputMap = new OutputVariableMap();
          outputMap.integrationVariableName = output.name;
          outputMap.type = output.type;
          outputMap.isMasked = output.IsMasked;
          this.model.outputs.push(outputMap);
        });
      }
    }
  }

  getVariableRef(id: number) {
    return this.editorService.getVariableWithId(id);
  }

  setVariableOnOutput(output : OutputVariableMap, variable : VariableDefinition) {
    if(variable == null) {
      output.variableRefId = null;
    }
    else {
      output.variableRefId = variable.Id;
    }
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.errorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.errorBlockId = null;
  }

  isInputValid(str) {
    return isStringValid(str);
  }

  getVariableType(variable: TypeDefinition) : string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }
}
