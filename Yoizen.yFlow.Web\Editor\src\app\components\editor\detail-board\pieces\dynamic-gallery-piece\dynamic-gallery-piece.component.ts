import {Component, OnDestroy, OnInit} from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { GalleryPiece, GalleryImage } from '../../../../../models/pieces/GalleryPiece';
import * as _ from 'lodash';
import { BasePiece } from '../../../../../models/pieces/BasePiece';
import { EventEmitter } from "@angular/core";
import { DeleteCardQuestionComponent } from '../../../popups/delete-card-question/delete-card-question.component';
import { isUrlValid, isStringValid } from '../../../../../urlutils.module'
import { ButtonPiece } from '../../../../../models/pieces/ButtonPiece';
import {DynamicGalleryPiece} from "../../../../../models/pieces/DynamicGalleryPiece";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {OutputVariableMap} from "../../../../../models/pieces/IntegrationPiece";
import {Concatenate} from "../../../../../models/pieces/Concatenate";
import {OperatorDefinitions} from "../../../../../models/OperatorType";
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-dynamic-gallery-piece',
  templateUrl: './dynamic-gallery-piece.component.html',
  styleUrls: ['./dynamic-gallery-piece.component.scss']
})
export class DynamicGalleryPieceComponent extends BasePieceVM implements OnInit, OnDestroy {
  model : DynamicGalleryPiece;
  expandButton : ButtonPiece;
  variableFilter = [ TypeDefinition.Array ];
  subs = new Subscription();

  constructor( editorService : EditorService, public modalService : ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as DynamicGalleryPiece;
  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.subs.unsubscribe();
  }

  isStringValid(url) {
    return isStringValid(url);
  }

  get customVariables() : VariableDefinition[] {
    return Concatenate.SpecialVar;
  }

  get InputVariableData(): VariableDefinition {
    return this.editorService.getVariableWithId( this.model.SourceVariableId);
  }

  setInputVariable(variable : VariableDefinition) {
    if( variable != null) {
      this.model.SourceVariableId = variable.Id;
    }
    else {
      this.model.SourceVariableId = null;
    }
  }

  validateVariable(value:string) {
    return false;
  }

  isUrlValid(url) {
    if (url === null || url.length === 0) {
      return true;
    }
    return isUrlValid(url);
  }

  addButton() {
    this.expandButton = new ButtonPiece();
    this.model.image.buttons.push(this.expandButton);
  }

   canAddButton() : boolean {
    if (this.readOnly) {
      return false;
    }
    var value = this.model.image.buttons.length < 20;
    return value;
  }

   deleteButtonElement(element) {
     this.model.image.buttons.splice(element, 1);
  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.expandButton = btn;
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
      this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
      this.model
    );
  }

  getNextPiece() : BasePiece {
    return this.editorService.getEditorState().SelectedBlock.Pieces[this.index +1];
  }

  canCreateQuickReply() {
    if (this.readOnly) {
      return false;
    }

    if (!this.InputVariableData) {
      return false;
    }

    let next = this.getNextPiece();
    if( next != null ) {
      if(next.type == 'quick-reply-piece') {
        return false;
      }
    }
    return true;
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  getVariableType(variable: VariableDefinition) : string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }
}
