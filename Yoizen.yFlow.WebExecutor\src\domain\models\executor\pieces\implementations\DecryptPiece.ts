import { Base<PERSON>iece, BasePieceExecutor } from "../BasePiece";
import { Control } from "../../Control";
import { FlowExecutor } from "../../../../../application/executors/FlowExecutorBase";
import CipherExecutorClass from "../../../../../application/utils/CipherExecutor";
import { TextUtils } from "../../../../../application/utils/textUtils";
import { logger } from "../../../../../../../Yoizen.yFlow.Helpers/src/Logger";

const CipherExecutor = CipherExecutorClass.Class;

export class DecryptPiece extends BasePiece {
    DecryptKey: string;
    VariableToDecryptId: string;
    VariableDecryptedId: string;
    DecryptType: number;
    DecryptModeType: number;
    DecryptPaddingType: number;
    ErrorMessage: string;
    ErrorBlockId: string;
    // New properties for custom method support
    CustomMethod: boolean;
    Delimiter: string;
    IvBytesLength: number;
    ReplaceCharacters: boolean;
    CharactersToReplace: Array<{ value: string; replaceWith: string }>;

}
export class DecryptPieceExecutor extends BasePieceExecutor {
    async execute(piece: DecryptPiece, control: Control, executor: FlowExecutor, definition: any, jumpedToAnotherBlock: { value: boolean }, block: any, pieceIndex: number): Promise<void> {
        let decryptKey = TextUtils.replaceVariablesInText(piece.DecryptKey, control);
        let variableToDecryptDefinition = definition.VariableList.find(variable => variable.Id === piece.VariableToDecryptId);
        let variableToDecrypt = null;
        if (variableToDecryptDefinition && variableToDecryptDefinition.Name in control.runtimeVariables) {
            variableToDecrypt = control.runtimeVariables[variableToDecryptDefinition.Name];
        }

        let variableDecryptedDefinition = definition.VariableList.find(variable => variable.Id === piece.VariableDecryptedId);

        // Set default values for custom method parameters if not provided
        const customMethod = piece.CustomMethod || false;
        const delimiter = piece.Delimiter || null;
        const ivBytesLength = piece.IvBytesLength || null;
        const replaceCharacters = piece.ReplaceCharacters || false;
        const charactersToReplace = piece.CharactersToReplace || [];


        if (variableDecryptedDefinition) {
            try {
                let cipher;

                const decryptType = typeof piece.DecryptType === 'string' ?
                    piece.DecryptType === 'rabbit' ? 2 : parseInt(piece.DecryptType, 10) :
                    piece.DecryptType;

                //@ts-ignore
                if (piece.DecryptType === 'rabbit' || piece.DecryptType === 2) {
                    // Rabbit doesn't require mode or padding, but still needs all constructor parameters
                    cipher = new CipherExecutor(
                        piece.DecryptType,
                        null,
                        null,
                        decryptKey,
                        customMethod,
                        delimiter,
                        ivBytesLength,
                        replaceCharacters,
                        charactersToReplace
                    );
                } else {
                    // AES and TripleDES require all parameters
                    cipher = new CipherExecutor(
                        piece.DecryptType,
                        piece.DecryptModeType,
                        piece.DecryptPaddingType,
                        decryptKey,
                        customMethod,
                        delimiter,
                        ivBytesLength,
                        replaceCharacters,
                        charactersToReplace
                    );
                }
                if (!variableToDecrypt) {
                    throw new Error('El valor a descifrar es nulo o indefinido');
                }


                let decryptedValue = cipher.decrypt(variableToDecrypt);

                control.runtimeVariables[variableDecryptedDefinition.Name] = decryptedValue;

            } catch (error) {
                logger.error({
                    caseId: control.body.case.id,
                    messageId: control.body.message.id,
                    userId: control.body.user.id,
                    error: error,
                    decryptType: piece.DecryptType,
                    valueType: typeof variableToDecrypt,
                    valueLength: variableToDecrypt ? variableToDecrypt.length : 0,
                    valuePreview: variableToDecrypt ? variableToDecrypt.substring(0, 20) + '...' : 'NULL'
                }, `[${control.body.message.id}] Error al desencriptar los datos: ${error}`);

                let errorMessage = piece.ErrorMessage
                    ? TextUtils.replaceVariablesInText(piece.ErrorMessage, control)
                    : 'Error al desencriptar los datos.';

                let pieceMessage = executor.createTextMessage(errorMessage, control);
                if (pieceMessage !== null) {
                    if (!Array.isArray(control.messages)) {
                        control.messages = [];
                    }
                    control.messages.push(pieceMessage);
                    control.conversationMessagesToSave.push(errorMessage);
                }

                if (piece.ErrorBlockId && piece.ErrorBlockId !== '-1') {
                    control.stoppedOnPiece = null;
                    control.inputPieceInfo = null;
                    control.startOnPiece = null;

                    let errorBlock = definition.findBlockById(piece.ErrorBlockId);
                    if (errorBlock) {
                        await executor.execute(definition, errorBlock, control);
                        return;
                    } else {
                        logger.info({ caseId: control.body.case.id, messageId: control.body.message.id, userId: control.body.user.id },
                            `[${control.body.message.id}] El bloque de error con ID ${piece.ErrorBlockId} no existe.`);
                        throw error;
                    }
                } else {
                    throw error;
                }
            }
        }
    }
}

