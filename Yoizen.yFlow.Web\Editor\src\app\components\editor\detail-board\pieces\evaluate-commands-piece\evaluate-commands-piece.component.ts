import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { EvaluateCommandsPiece } from '../../../../../models/pieces/EvaluateCommandsPiece';

@Component({
  selector: 'app-evaluate-commands-piece',
  templateUrl: './evaluate-commands-piece.component.html',
  styleUrls: ['./evaluate-commands-piece.component.scss']
})
export class EvaluateCommandsPieceComponent extends BasePieceVM implements OnInit {
  model: EvaluateCommandsPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as EvaluateCommandsPiece;
  }
}
