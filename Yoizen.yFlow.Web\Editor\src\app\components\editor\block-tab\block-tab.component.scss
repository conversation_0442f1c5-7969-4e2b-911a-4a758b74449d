@import "_variables";
@import "_mixins";

.block-tab {
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  position: relative;

  .block-list {
    background-color: $sidebarBackgroundColor;
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    padding: 20px 10px;
    height: 100%;
    overflow-y: auto;
    z-index: 100;

    @include scrollbar;

    @media (min-width:768px) {
      width: 250px;
    }

    @media (min-width:992px) {
      width: 350px;
    }

    @media (min-width:1200px) {
      width: 500px;
    }
  }

  .block-detail {
    background-color: rgb(249, 248, 248);
    width: 100%;
    height: 100%;
    overflow: auto;

    @include scrollbar;

    @media (min-width:768px) {
      padding-left: 250px;
    }

    @media (min-width:992px) {
      padding-left: 350px;
    }

    @media (min-width:1200px) {
      padding-left: 500px;
    }
  }
}
