<div class="multimediaAnalysisPiece card" [ngClass]="{'invalid-piece': !isValid()}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>

  <div class="card-title">
    <span class="fa fa-file-image"></span> {{ 'MULTIMEDIA_ANALYSIS_TITLE' | translate }}
  </div>

  <div class="definition">
    <div class="name">
      <span class="title">{{ 'MULTIMEDIA_ANALYSIS_IMAGE_URL' | translate }}:</span>
      <app-input-with-variables class="input" [(value)]="model.imageUrl" [disabled]="readOnly"
        [validator]="isImageUrlValid.bind(this)">
      </app-input-with-variables>
    </div>

    <div class="name">
      <span class="title">{{ 'MULTIMEDIA_ANALYSIS_EXTRACTION_FORMAT' | translate }}:</span>
      <select class="input-variable-area" [(ngModel)]="model.extractionFormat" [disabled]="readOnly">
        <option *ngFor="let format of extractionFormats" [value]="format.Id">{{ format.Name }}</option>
      </select>
    </div>

    <div class="form-group">
      <div class="d-flex align-items-center">
        <div class="name">
          <span class="title">{{ 'MULTIMEDIA_ANALYSIS_ENABLE_CLIENT_PROMPTS' | translate }}:</span>
          <ui-switch [(ngModel)]="enableClientPrompts" [disabled]="readOnly" color="#45c195" size="small"
            defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
        </div>
        <div class="name" *ngIf="enableClientPrompts">
          <app-input-with-variables class="input" [(value)]="model.clientPrompts"
            [disabled]="readOnly" [wideInput]="true"
            ngbTooltip="{{ 'MULTIMEDIA_ANALYSIS_CLIENT_PROMPTS_TOOLTIP' | translate }}" placement="top">
          </app-input-with-variables>
        </div>
      </div>
    </div>
    
    <div class="form-group">
      <span class="title">{{ 'SAVE_INTO' | translate }}:</span>
      <app-variable-selector-input class="input" [VariableData]="outputVariable"
        (setVariable)="setOutputVariable($event)" [canSelectConstants]="false" [readOnly]="readOnly"
        [validator]="isOutputVariableValid.bind(this)" [typeFilters]="outputVariableFilter">
      </app-variable-selector-input>
    </div>

    <div class="next">
      <span class="title">{{ 'MULTIMEDIA_ANALYSIS_ON_ERROR_GO_TO' | translate }}:</span>
      <app-block-picker class="input" [blockId]="model.errorBlockId" (onSelectNewBlock)="onSelectBlock($event)"
        (onDeleteBlock)="onDeleteBlock()" [readOnly]="readOnly" [isInvalid]="!isErrorBlockValid()">
      </app-block-picker>
    </div>

    <div class="next">
      <span class="title">{{ 'MULTIMEDIA_ANALYSIS_ON_INTEGRITY_ERROR_GO_TO' | translate }}:</span>
      <app-block-picker class="input" [blockId]="model.integrityErrorBlockId"
        (onSelectNewBlock)="onSelectIntegrityErrorBlock($event)" (onDeleteBlock)="onDeleteIntegrityErrorBlock()"
        [readOnly]="readOnly" [isInvalid]="!isIntegrityErrorBlockValid()">
      </app-block-picker>
    </div>
  </div>
</div>