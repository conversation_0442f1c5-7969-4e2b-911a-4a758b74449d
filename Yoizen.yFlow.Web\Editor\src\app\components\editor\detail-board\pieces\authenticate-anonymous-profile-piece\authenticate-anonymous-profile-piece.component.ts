import { Component, OnInit } from '@angular/core';
import { AuthenticateAnonymousProfilePiece } from 'src/app/models/pieces/AuthenticateAnonymousProfilePiece';
import { OutputVariableMap } from 'src/app/models/pieces/IntegrationPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';
import { BlockDefinition } from 'src/app/models/BlockDefinition';

@Component({
  selector: 'app-authenticate-anonymous-profile-piece',
  templateUrl: './authenticate-anonymous-profile-piece.component.html',
  styleUrls: ['./authenticate-anonymous-profile-piece.component.scss']
})
export class AuthenticateAnonymousProfilePieceComponent extends BasePieceVM implements OnInit {
  model: AuthenticateAnonymousProfilePiece;
  variableFilter = [TypeDefinition.Text];

  variableDefinition(type): VariableDefinition {
    switch(type) {
      case "AuthClientCode":
        return this.editorService.findVariableWithId(this.model.AuthClientCodeVariableId);
      case "AuthName":
        return this.editorService.findVariableWithId(this.model.AuthNameVariableId);
      case "AuthEmail":
        return this.editorService.findVariableWithId(this.model.AuthEmailVariableId);
      case "AuthPhone":
        return this.editorService.findVariableWithId(this.model.AuthPhoneVariableId);
      default:
        return null;
    }
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AuthenticateAnonymousProfilePiece;

  }

  setVariableOnOutput(type: string, variable: VariableDefinition) {
    switch(type) {
      case "AuthClientCode":
        this.model.AuthClientCodeVariableId = variable !== null ? variable.Id : null;
        break;
      case "AuthName":
        this.model.AuthNameVariableId = variable !== null ? variable.Id : null;
        break;
      case "AuthEmail":
        this.model.AuthEmailVariableId = variable !== null ? variable.Id : null;
        break;
      case "AuthPhone":
        this.model.AuthPhoneVariableId = variable !== null ? variable.Id : null;
        break;
      default:
        break;
    }
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

}
