@import "_variables";
@import "_mixins";

.button-content {
  position: relative;
  cursor: pointer;

  & > .invalid {
    display: none;
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: red;
    font-size: 18px;
  }

  &.invalid {
    & > .invalid {
      display: inline-block;
    }
  }

  .addName {
    border-bottom: 1px solid $cardSeparatorBorderColor;
    padding: 10px;
    position: relative;
    text-align: center;
    display: flex;
    flex-direction: column;

    .name {
      font-weight: bold;
      text-overflow: ellipsis;
      overflow-x: hidden;
      white-space: nowrap;
      word-break: break-all;

      ::ng-deep.variable {
        background-color: $inputVariableBackgroundColor;
        color: $inputVariableColor;
        border-radius: 3px;
        line-height: 16px;
      }
      ::ng-deep.script {
        background-color: $inputScriptBackgroundColor;
        color: $inputScriptColor;
        border-radius: 3px;
        line-height: 16px;
      }
    }

    .name-invalid {
      font-style: italic;
      color: red;
    }

    .block {
      color: $linkActionColor;

      .goto {
        display: inline-block;
        margin-left: 10px;
        transform: scale(0.3);
        opacity: 0;
        transition: opacity 200ms cubic-bezier(0.2, 0.7, 0.5, 1), transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1), -webkit-transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1);
        cursor: pointer;
      }

      &:hover {
        .goto {
          transform: scale(1);
          opacity: 1;
        }
      }
    }

    .url, .login, .dial {
      text-decoration: underline;
      max-width: 100%;
      white-space: nowrap;
      word-break: break-all;
      overflow-x: hidden;
      text-overflow: ellipsis;
    }

    .trash {
      @include trash;
      position: absolute;
      right: -13px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        opacity: 1;
      }
    }
  }
}
