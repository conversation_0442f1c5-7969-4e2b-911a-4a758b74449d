import { Op, TableHints } from 'sequelize';
import Report from '../../../../Yoizen.yFlow.Web/models/report';
import User from '../../../../Yoizen.yFlow.Web/models/user';
import { IReportPort } from '../../ports/IReportPort';

export class SequelizeReportAdapter implements IReportPort {

    async GetAll(status: string) {
        const reports = await Report.findAll({
            where: { status: status },
            attributes: ['id', 'report_type', 'date_start', 'date_end', 'user_id', 'flow_id', 'events'],
            include: [{
                model: User,
                attributes: ['lang'],
            }],
            tableHint: TableHints.NOLOCK

        });
        return reports;
    }

    async GetAllFrom(dateFrom: Date, status: string) {
        const reports = await Report.findAll({
            where: {
                created_at: {
                    [Op.lte]: dateFrom
                },
                status: 'FINISHED'
            },
            attributes: ['id', 'report_type', 'date_start', 'date_end', 'user_id', 'flow_id', 'status', 'message', 'created_at', 'updated_at'],
            include: {
                model: User,
                attributes: ['name']
            },
            order: [
                ['id', 'DESC']
            ],
            tableHint: TableHints.NOLOCK
        });
        return reports;
    }

    async ChangeStatus(id: number, newStatus: string) {
        await Report.update({ status: newStatus }, {
            where: { id }
        });
    }

    async SetError(id: number, message: string) {
        await Report.update({
            status: 'ERROR',
            message: message
        }, {
            where: {
                id: id
            }
        });
    }

}