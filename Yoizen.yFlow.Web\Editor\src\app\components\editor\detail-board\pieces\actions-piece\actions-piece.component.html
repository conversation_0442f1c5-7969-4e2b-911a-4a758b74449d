<div class="action card">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-comment-dots"></span> {{ 'CARD_ACTIONS_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_ACTIONS_INFO' | translate }}
  </div>
  <p>Delay: {{model.Duration}}s</p>
  <input class="slider" type="range" min="1" max="5" [(ngModel)]="model.Duration" [disabled]="readOnly" />
  <div class="next block-picker">
    <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
      <input class="input" type="text" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchBlockString" LimitLength (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
      <div #blockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchBlockString
                            (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
        <span class="block-display">{{BlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>

      </span>
  </div>
</div>
