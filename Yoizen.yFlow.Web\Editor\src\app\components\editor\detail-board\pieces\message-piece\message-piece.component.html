<div class="card text {{ editorService.getChannelTypeName(model.Channel) }}" [ngClass]="{'invalid-piece': !model.isValid(editorService), 'buttons-warning': containsMoreButtonsThanAllowed() }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-align-left"></span> {{ 'CARD_MESSAGE_TITLE' | translate }}
  </div>
  <div class="more-buttons-than-allowed" role="alert">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'CARD_MESSAGE_MORE_BUTTONS_THAN_ALLOWED' | translate:{maxButtons: maxButtonsAllowed() } }}
    </div>
  </div>
  <div class="max-length" role="alert" *ngIf="!readOnly">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      <span class="messenger">{{ 'CARD_MESSAGE_MESSENGER_MAX_LENGTH' | translate }}</span>
      <span class="twitter">{{ 'CARD_MESSAGE_TWITTER_MAX_LENGTH' | translate }}</span>
      <span class="whatsapp" *ngIf="model.Buttons.length === 0">{{ 'CARD_MESSAGE_WHATSAPP_MAX_LENGTH' | translate }}</span>
      <span class="whatsapp" *ngIf="model.Buttons.length > 0">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_MAX_LENGTH' | translate }}</span>
    </div>
  </div>
  <div class="stats">
    <div class="title"><span class="fa fa-chart-line"></span>{{ 'PIECE_STATS_TITLE' | translate }}</div>
    <div class="stats-empty" *ngIf="stats === null">
      <div class="alert alert-info">
        {{ 'PIECE_STATS_DESC_EMPTY' | translate }}
      </div>
    </div>
    <div class="stats-info" *ngIf="stats !== null">
      <ul>
        <li><span class="title">{{ 'BLOCK_STATS_FIRSTEXECUTION' | translate }}:</span><span class="value">{{ stats.firstExecution | dateFormat: 'LLL' }}</span></li>
        <li><span class="title">{{ 'BLOCK_STATS_LASTEXECUTION' | translate }}:</span><span class="value">{{ stats.lastExecution | dateFormat: 'LLL' }}</span></li>
        <li><span class="title">{{ 'BLOCK_STATS_COUNT' | translate }}:</span><span class="value">{{ stats.count }}</span></li>
      </ul>
    </div>
  </div>
  <div class="messages">
    <app-text-list-message *ngFor="let text of model?.TextList let i = index"
                           [(Text)]="text.text"
                           [Index]="i"
                           [CanDelete]="canDelete()"
                           [readOnly]="readOnly"
                           [Validator]="isTextValid(i)"
                           (onDelete)="deleteElement($event)"></app-text-list-message>
    <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
      <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
           placement="right" tooltipClass="tooltip-add">
        <span class="fa fa-plus"></span>
      </div>
    </div>
  </div>
  <ng-container *ngIf="supportButtons()">
    <div class="buttons" [dragula]="dragulaGroupName" [(dragulaModel)]="model.Buttons">
        <app-button-element *ngFor="let button of model?.Buttons let i = index"
                            [Model]="button"
                            [Index]="i"
                            [stats]="getButtonStats(button)"
                            (onDelete)="deleteButtonElement($event)"
                            [expandedBtn]="expandButton"
                            [readOnly]="readOnly"
                            [MaxLength]="buttonsMaxLength"
                            [EmojisAllowed]="buttonsAllowEmojis"
          (onShowDetail)="onShowButtonDetail($event)">
      </app-button-element>
    </div>
  </ng-container>
  <div class="addButton" (click)="addNewButton(); $event.stopPropagation();" *ngIf="supportButtons() && canAddButton() && !readOnly">
    <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
  </div>
  <div *ngIf="supportButtons() && !canAddButton() && !readOnly">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      {{'MAX_BUTTON_REACHED' | translate}}
    </div>
  </div>
  <div class="quick" *ngIf="canCreateQuickReply() && !readOnly" (click)="addQuickReplyPiece()">
    <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_QUICKREPLY' | translate }}
  </div>
</div>
