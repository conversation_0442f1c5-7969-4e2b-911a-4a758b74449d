import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByIntegrations extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare integration: { id: string; name: string; };
    declare integrationId: string;
    declare total: number;
    declare totalResponseTime: number;
    declare errorResponseTime: number;
    declare error: number;

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.integrationId = data.integrationId;
        this.version = data.version;
        this.totalResponseTime = data.totalResponseTime;
        this.errorResponseTime = data.errorResponseTime;
        this.total = data.total;
        this.error = data.error;
        this.integration = {
            id: data.integrationId,
            name: undefined
        }
    }

    type() {
        return HistoryDailyInfoTypes.Integrations;
    }
}

HistoryDailyByIntegrations.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    error: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    totalResponseTime: {
        type: DataTypes.DECIMAL(18, 2),
        field: 'total_response_time',
    },
    errorResponseTime: {
        type: DataTypes.DECIMAL(18, 2),
        field: 'error_response_time',
    },
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    integrationId: {
        type: DataTypes.INTEGER,
        field: 'integration_id',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_integrations',
    tableName: 'history_daily_by_integrations',
    timestamps: false
});