module.exports = {
    'env': {
        'browser': true,
        'commonjs': true,
        'es2021': true
    },
    'extends': [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
    ],
    'overrides': [
        {
            'env': {
                'node': true
            },
            'files': [
                '.eslintrc.{js,cjs}'
            ],
            'parserOptions': {
                'sourceType': 'script'
            }
        }
    ],
    'parser': '@typescript-eslint/parser',
    'parserOptions': {
        'ecmaVersion': 'latest'
    },
    'plugins': [
        '@typescript-eslint',
        'import'
    ],
    'rules': {
        // Reglas generales
        'indent': ['error', 2],
        'quotes': ['error', 'single'],
        'semi': ['error', 'always'],

        // Reglas específicas de TypeScript
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }],

        // Reglas para imports
        'import/order': ['error', { 'alphabetize': { 'order': 'asc', 'caseInsensitive': true } }],
        // 'import/no-unresolved': 'error',
        'import/extensions': 'off'

    }
};
