<app-nav-bar
  [leftButtonLink]="homeUrl"
  [mainText]="'WELCOME' | translate"
  [showLogoutButton]="false"
  [showSettingsButton]="false"
  [showTestButton]="false"
  [showConfigurationButton]="false"
  [showDashboardButton]="true"
  (onDashboardButtonClick)="showDashboard()"
>
</app-nav-bar>

<div class="configuration-wrapper">
  <div class="configuration-container">
    <h2 class="main-title">{{ "SETTINGS" | translate }}</h2>

    <div class="config-card" *ngIf="isAdmin">
      <h3 class="section-title">{{ "CHANGE_LANGUAGE" | translate }}</h3>
      <div class="field-row">
        <span class="label">{{ "LANGUAGE" | translate }}</span>
        <select class="form-control" [(ngModel)]="currentLang">
          <option *ngFor="let lang of langs" [ngValue]="lang">
            {{ lang | uppercase | translate }}
          </option>
        </select>
      </div>
      <div class="button-row">
        <button class="btn btn-primary" (click)="changeLang()">
          {{ "ACCEPT" | translate }}
        </button>
      </div>
    </div>

    <div class="config-card">
      <h3 class="section-title">{{ "CHANGE_PASSWORD" | translate }}</h3>
      <div class="field-row">
        <span class="label">{{ "CHANGE_PASSWORD_CURRENT" | translate }}</span>
        <input
          type="password"
          class="form-control"
          [(ngModel)]="currentPassword"
          placeholder="{{ 'CHANGE_PASSWORD_CURRENT' | translate }}"
        />
      </div>
      <div class="field-row">
        <span class="label">{{ "CHANGE_PASSWORD_NEW" | translate }}</span>
        <div class="password-strength">
          <input
            type="password"
            class="form-control"
            [(ngModel)]="newPassword"
            (input)="calcPasswordStrength($event)"
          />
          <meter [value]="passwordStrengthValue" min="0" max="4"></meter>
        </div>
      </div>
      <div class="field-row">
        <span class="label">{{
          "USER_PASSWORD_CONFIRMATION" | translate
        }}</span>
        <input
          type="password"
          class="form-control"
          [(ngModel)]="confirmationPassword"
        />
      </div>
      <div class="button-row">
        <button class="btn btn-primary" (click)="changePassword()">
          {{ "ACCEPT" | translate }}
        </button>
      </div>
    </div>

    <div class="config-card reports-section" *ngIf="canEditFtp">
      <div class="card-header">
        <h3>{{ "AUTOMATIC_REPORT_GENERATION" | translate }}</h3>
        <ui-switch
          [(ngModel)]="ftpConfig.enabled"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div class="card-content" *ngIf="ftpConfig.enabled">
        <div class="connection-settings">
          <div class="field-row">
            <span class="label">{{
              "GENERATE_AUTO_REPORT_CONNECTION_TYPE" | translate
            }}</span>
            <div class="input-with-action">
              <select class="form-control" [(ngModel)]="ftpConfig.protocol">
                <option value="ftp">FTP</option>
                <option value="sftp">SFTP</option>
              </select>
              <button
                class="btn btn-primary"
                (click)="
                  ftpConfig.protocol === 'ftp'
                    ? openFTPModal()
                    : openSFTPModal()
                "
              >
                <i class="fas fa-cog"></i>
                {{ "EDIT" | translate }}
              </button>
            </div>
          </div>
        </div>

        <div class="reports-grid">
          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-cube"></i>
              <span>{{ "BLOCKS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.blocksReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-brain"></i>
              <span>{{ "COMMANDS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.commandsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-reply-all"></i>
              <span>{{ "DEFAULT_ANSWERS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.defaultAnswersReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-random"></i>
              <span>{{ "DERIVATIONS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.derivationsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-calendar-alt"></i>
              <span>{{ "EVENTS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.eventsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item">
            <div class="report-header">
              <i class="fas fa-chart-line"></i>
              <span>{{ "DETAILED_STATISTICS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.detailedStatisticEventReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-globe"></i>
              <span>{{ "GLOBALS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.globalsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-users"></i>
              <span>{{ "GROUPS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.groupsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-plug"></i>
              <span>{{ "INTEGRATIONS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.integrationsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <div class="report-item" *ngIf="!isContingencyBot">
            <div class="report-header">
              <i class="fas fa-user-slash"></i>
              <span>{{ "ABANDONED_CASES" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.abandonedCasesReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <!-- New user sessions report option -->
          <div class="report-item">
            <div class="report-header">
              <i class="fas fa-file-alt"></i>
              <span>{{ "USER_SESSIONS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.userSessionsReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>

          <!-- Nuevo reporte de Detallado de Eventos -->
          <div class="report-item">
            <div class="report-header">
              <i class="fas fa-file-alt"></i>
              <span>{{ "DETAILED_EVENTS" | translate }}</span>
            </div>
            <ui-switch
              [(ngModel)]="ftpConfig.detailedEventReport"
              [disabled]="!ftpConfig.enabled"
            ></ui-switch>
          </div>
        </div>

        <div class="button-row">
          <button class="btn btn-primary" (click)="saveAutoReportConfig()">
            {{ "ACCEPT" | translate }}
          </button>
        </div>
      </div>
    </div>

    <div
      class="config-card users-section"
      *ngIf="showUsersPanel || userCanValidatePasswords"
    >
      <div class="card-header">
        <h3>{{ "USERS" | translate }}</h3>
        <button
         class="btn btn-primary new-user-btn"
          *ngIf="showUsersPanel"
          (click)="createUser()"
        >
          <i class="fas fa-plus"></i>
          <span>{{ "USER_NEW" | translate }}</span>
        </button>
      </div>

      <div class="users-table-wrapper">
        <table class="users-table">
          <thead>
            <tr>
              <th class="col-name">{{ "USER_NAME" | translate }}</th>
              <th class="col-login">{{ "LOGIN_TYPE" | translate }}</th>
              <th class="col-permission">{{ "USER_ADMIN" | translate }}</th>
              <th class="col-permission">{{ "USER_ENABLED" | translate }}</th>
              <th class="col-permission">{{ "USER_CAN_EDIT" | translate }}</th>
              <th class="col-permission">{{ "USER_CAN_DELETE" | translate }}</th>
              <th class="col-permission">
                {{ "USER_CAN_PUBLISH" | translate }}
              </th>
              <th class="col-permission">
                {{ "USER_SEE_STATISTICS" | translate }}
              </th>
              <th class="col-permission">
                {{ "USER_CAN_VALIDATE_PASSWORDS" | translate }}
              </th>
              <th class="col-permission" *ngIf="ySmartEnabled">
                {{ "USER_CAN_ACCESS_YSMART" | translate }}
              </th>
              <th class="col-actions">{{ "ACTIONS" | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let user of filteredUsers">
              <tr *ngIf="!user.is_deleted">
                <td class="user-info">
                  <i class="fas fa-user"></i>
                  <span>{{ user.name | truncate:40 }}</span>
                </td>
                <td>{{ user.login_type }}</td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.is_admin
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.enabled
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_edit
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_delete
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_publish
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_see_statistics
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_validate_passwords
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="center" *ngIf="ySmartEnabled">
                  <i
                    class="fas"
                    [ngClass]="
                      user.can_access_ysmart
                        ? 'fa-check-circle success'
                        : 'fa-times-circle error'
                    "
                  ></i>
                </td>
                <td class="actions">
                  <button
                    class="btn btn-icon"
                    (click)="editUser(user)"
                    *ngIf="showUserModal(user)"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-icon" (click)="deleteUser(user)" *ngIf="showDeleteModal(user)">
                    <i class="fa fa-trash"></i>
                  </button>
                  <div class="no-actions" *ngIf="!showDeleteModal(user) && !showUserModal(user)"></div>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>

    <div class="config-card" *ngIf="canEditSmtp">
      <h3 class="section-title">{{ "SMTP_SETTINGS" | translate }}</h3>
      <div class="field-row">
        <span class="label">{{ "SMTP_SETTINGS_USE" | translate }}</span>
        <ui-switch
          [(ngModel)]="smtpConfig.enabled"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div *ngIf="smtpConfig.enabled">
        <div class="field-row">
          <span class="label">{{ "SMTP_SERVER" | translate }}</span>
          <input
            type="text"
            class="form-control"
            [(ngModel)]="smtpConfig.server"
          />
        </div>
        <div class="field-row">
          <span class="label">{{ "SMTP_PORT" | translate }}</span>
          <input
            type="number"
            class="form-control"
            [(ngModel)]="smtpConfig.port"
            min="0"
          />
        </div>
        <div class="field-row">
          <span class="label">{{ "SMTP_SECURE" | translate }}</span>
          <ui-switch
            [(ngModel)]="smtpConfig.secure"
            color="#45c195"
            size="small"
          ></ui-switch>
        </div>
        <div class="field-row">
          <span class="label">{{ "SMTP_STARTTLS" | translate }}</span>
          <ui-switch
            [(ngModel)]="smtpConfig.startTls"
            color="#45c195"
            size="small"
          ></ui-switch>
        </div>
        <div class="field-row">
          <span class="label">{{ "SMTP_ANONYMOUS" | translate }}</span>
          <ui-switch
            [(ngModel)]="smtpConfig.anonymous"
            color="#45c195"
            size="small"
          ></ui-switch>
        </div>

        <div *ngIf="!smtpConfig.anonymous">
          <div class="field-row">
            <span class="label">{{ "SMTP_USER" | translate }}</span>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="smtpConfig.username"
            />
          </div>
          <div class="field-row">
            <span class="label">{{ "SMTP_PASSWORD" | translate }}</span>
            <input
              type="password"
              class="form-control"
              [(ngModel)]="smtpConfig.password"
            />
          </div>
        </div>

        <div class="field-row">
          <span class="label">{{ "SMTP_SENDER" | translate }}</span>
          <input
            type="email"
            class="form-control"
            [(ngModel)]="smtpConfig.sender"
          />
        </div>
      </div>

      <div class="button-row">
        <button
          class="btn btn-secondary"
          *ngIf="smtpConfig.enabled"
          (click)="testSmtpConfig()"
        >
          {{ "TEST_SFTP_CONNECTION" | translate }}
        </button>
        <button class="btn btn-primary" (click)="saveSmtpConfig()">
          {{ "ACCEPT" | translate }}
        </button>
      </div>
    </div>

    <div class="config-card"*ngIf="isAdmin">
      <h3 class="section-title">{{ "SAML_CONFIGURATION" | translate }}</h3>
      <div class="field-row">
        <span class="label">{{ "ENABLE_SAML" | translate }}</span>
        <ui-switch
          [(ngModel)]="samlConfig.enabled"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div *ngIf="samlConfig.enabled">
        <div class="field-row">
          <span class="label">{{ "SAML_ENDPOINT" | translate }}</span>
          <input
            type="text"
            class="form-control"
            [(ngModel)]="samlConfig.endpoint"
          />
        </div>
        <div class="field-row">
          <span class="label">{{ "SAML_CLIENT_NAME" | translate }}</span>
          <input
            type="text"
            class="form-control"
            [(ngModel)]="samlConfig.clientName"
          />
        </div>
        <div class="field-row">
          <span class="label">{{ "SAML_REALM_NAME" | translate }}</span>
          <input
            type="text"
            class="form-control"
            [(ngModel)]="samlConfig.realmName"
          />
        </div>
      </div>

      <div class="field-row">
        <span class="label">{{ "SAML_ALLOW_LOCAL_USERS" | translate }}</span>
        <ui-switch
          [(ngModel)]="samlConfig.allowLocalUsers"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div class="button-row">
        <button class="btn btn-primary" (click)="saveSamlConfig()">
          {{ "ACCEPT" | translate }}
        </button>
      </div>
    </div>

    <div class="config-card" *ngIf="isYoizenAdmin">
      <h3 class="section-title">{{ "STATISTICS_CONFIGURATION" | translate }}</h3>

      <div class="field-row">
        <span class="label">{{ "ENABLE_CONSOLIDATED_STATISTICS" | translate }}</span>
        <ui-switch
          [(ngModel)]="statisticsConfig.consolidatedEnabled"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div class="field-row">
        <span class="label">{{ "ENABLE_DETAILED_STATISTICS" | translate }}</span>
        <ui-switch
          [(ngModel)]="statisticsConfig.detailedEnabled"
          color="#45c195"
          size="small"
        ></ui-switch>
      </div>

      <div class="button-row">
        <button class="btn btn-primary" (click)="saveStatisticsConfig()">
          {{ "ACCEPT" | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<div class="overlay" *ngIf="loading">
  <action-spinner class="spinner"></action-spinner>
</div>
