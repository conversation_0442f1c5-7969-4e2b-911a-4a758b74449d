<div class="configuration-tab">
  <app-high-availability-configuration class="configurable-item" [readOnly]="readOnly"></app-high-availability-configuration>
  <app-ysocial-settings class="configurable-item" *ngIf="showYSocialSettingsConfigSection" [readOnly]="readOnly"></app-ysocial-settings>
  <app-cognitivity-configuration class="configurable-item" *ngIf="ySmartEnabled && isBot && !isContingencyBot" [readOnly]="readOnly"></app-cognitivity-configuration>
  <app-excel-import class="configurable-item" *ngIf="showImportExcel && !isContingencyBot"></app-excel-import> <!-- Mover abajo de todo antes de pushear -->
  <app-hsm-jump class="configurable-item" *ngIf="showHsmJumpConfigSection" [readOnly]="readOnly"></app-hsm-jump>
  <app-applepay-merchants-configuration class="configurable-item" *ngIf="showApplePayMerchantsConfigSection" [readOnly]="readOnly"></app-applepay-merchants-configuration>
  <app-google-configuration class="configurable-item" *ngIf="showGoogleConfigurationConfigSection && isBot && !isContingencyBot" [readOnly]="readOnly"></app-google-configuration>
  <app-persistent-menu class="configurable-item" *ngIf="showPersistentMenuConfigSection" [readOnly]="readOnly"></app-persistent-menu>
  <app-ice-breakers class="configurable-item" *ngIf="showIceBreakersConfigSection" [readOnly]="readOnly"></app-ice-breakers>
  <app-greetings class="configurable-item" *ngIf="showGreetingsConfigSection" [readOnly]="readOnly"></app-greetings>
  <app-statistics-events-configuration class="configurable-item" *ngIf="showStatisticsEventsConfigSection && isBot" [readOnly]="readOnly"></app-statistics-events-configuration>
  <app-account-linking class="configurable-item" *ngIf="showAccountLinkingConfigSection" [readOnly]="readOnly"></app-account-linking>
  <app-string-format-selection class="configurable-item" [readOnly]="readOnly"></app-string-format-selection>
  <app-business-availability class="configurable-item" [readOnly]="readOnly" [isInsidePiece]="false"></app-business-availability>
  <app-users-permissions class="configurable-item" *ngIf="isAdmin"></app-users-permissions>
  <app-function-table class="configurable-item"></app-function-table>
</div>
