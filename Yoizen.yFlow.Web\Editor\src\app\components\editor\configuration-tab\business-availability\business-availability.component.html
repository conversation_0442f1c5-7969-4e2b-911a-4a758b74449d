<div class="configurable-item">
  <div class="title" *ngIf="!isInsidePiece">
    {{ 'CONFIGURATION_BUSINESS_AVAILABILITY_TITLE' | translate }}
  </div>
  <div class="description" *ngIf="!isInsidePiece">
    {{ 'CONFIGURATION_BUSINESS_AVAILABILITY_DESCRIPTION' | translate }}
  </div>
  <div class="days">
    <div class="title" *ngIf="!isInsidePiece">
      {{ 'CONFIGURATION_BUSINESS_AVAILABILITY_DAYS_TITLE' | translate }}
    </div>
    <div class="card-info">
      {{ 'CARD_CALENDAR_INFO' | translate }} {{ GMT }}
    </div>
    <div class="timetable-container">
      <table class="timetable" cellspacing="0" border="0">
        <thead>
          <tr class="header">
            <th></th>
            <th class="day"><a (click)="toggleWorkingDate(0)">{{ getDayName(0) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(1)">{{ getDayName(1) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(2)">{{ getDayName(2) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(3)">{{ getDayName(3) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(4)">{{ getDayName(4) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(5)">{{ getDayName(5) | translate }}</a></th>
            <th class="day"><a (click)="toggleWorkingDate(6)">{{ getDayName(6) | translate }}</a></th>
          </tr>
        </thead>
        <tbody (mouseleave)="setMouseDownCalendar(false)">
          <tr class="hours" *ngFor="let interval of intervals; let i = index;"
              [ngClass]="{'interval': (i % 2 != 0)}">
            <td class="time" (mouseover)="setMouseDownCalendar(false)">{{ ((interval % 2 === 0) ? interval : interval - 1) / 2 }}:{{ interval % 2 === 0 ? '00' : '30' }}</td>
            <td class="selectable-time" day="0" dayname="Sunday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(0, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(0, interval)"
                (click)="toggleWorkingDateInterval(0, interval)"><span></span></td>
            <td class="selectable-time" day="1" dayname="Monday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(1, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(1, interval)"
                (click)="toggleWorkingDateInterval(1, interval)"><span></span></td>
            <td class="selectable-time" day="2" dayname="Tuesday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(2, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(2, interval)"
                (click)="toggleWorkingDateInterval(2, interval)"><span></span></td>
            <td class="selectable-time" day="3" dayname="Wednesday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(3, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(3, interval)"
                (click)="toggleWorkingDateInterval(3, interval)"><span></span></td>
            <td class="selectable-time" day="4" dayname="Thursday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(4, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(4, interval)"
                (click)="toggleWorkingDateInterval(4, interval)"><span></span></td>
            <td class="selectable-time" day="5" dayname="Friday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(5, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(5, interval)"
                (click)="toggleWorkingDateInterval(5, interval)"><span></span></td>
            <td class="selectable-time" day="6" dayname="Saturday" attr.time="{{interval}}"
                [ngClass]="{'selected': isWorkingDateIntervalSelected(6, interval)}"
                (mousedown)="setMouseDownCalendar(true)"
                (mouseup)="setMouseDownCalendar(false)"
                (mouseover)="toggleWorkingDateIntervalIfClicked(6, interval)"
                (click)="toggleWorkingDateInterval(6, interval)"><span></span></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="non-working-days" *ngIf="!isInsidePiece">
    <div class="title">
      {{ 'CONFIGURATION_BUSINESS_AVAILABILITY_NONWORKINGDAYS_TITLE' | translate }}
    </div>
    <div class="info">
      {{ 'CONFIGURATION_BUSINESS_AVAILABILITY_NONWORKINGDAYS_INFO' | translate }}
    </div>
    <div class="calendar">
      <div>
        <ngb-datepicker #dp [displayMonths]="3" [minDate]="minDate" [outsideDays]="'visible'"
                        [dayTemplate]="t"
                        (select)="onSelect($event)">
        </ngb-datepicker>
        <ng-template #t let-date let-disabled="disabled">
          <span class="custom-day"
                [class.selected]="isSelected(date)"
                [class.exception]="isException(date)"
                [class.disabled]="disabled">
            {{ date.day }}
          </span>
        </ng-template>
      </div>
    </div>
  </div>
</div>
