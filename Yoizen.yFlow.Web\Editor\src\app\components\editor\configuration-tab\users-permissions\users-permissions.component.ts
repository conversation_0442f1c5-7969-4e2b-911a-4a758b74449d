import { Permission } from './../../../../models/Permission';
import { EditorService } from 'src/app/services/editor.service';
import { ErrorPopupComponent } from './../../../error-popup/error-popup.component';
import { ModalService } from './../../../../services/Tools/ModalService';
import { StatusResponse } from './../../../../models/StatusResponse';
import { ServerService } from 'src/app/services/server.service';
import { getTokenPayload } from 'src/app/Utils/window';
import { UserPublicInfo } from './../../../../models/UserPublicInfo';
import { Component, OnInit, Input } from '@angular/core';
import {finalize} from 'rxjs/operators';
import { ToasterService } from 'angular2-toaster';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-users-permissions',
  templateUrl: './users-permissions.component.html',
  styleUrls: ['./users-permissions.component.scss']
})
export class UsersPermissionsComponent implements OnInit {

  usersPermissions: Permission[] = [];
  loadingUsers: boolean = false;

  constructor(private serverService : ServerService, 
              private modalService : ModalService, 
              private editorService : EditorService, 
              private toasterService: ToasterService,
              private translateService: TranslateService) { }

  ngOnInit() {
    let {uid: currentUid} = getTokenPayload();
    this.loadingUsers = true;
    this.serverService.getFlowPermissions(this.editorService.getCurrentFlowId())
    .pipe(finalize(() => {
      this.loadingUsers = false;
    }))
    .subscribe(({success, data}: StatusResponse) => {
      if (success) {
        data.forEach((p) => {
          let {canEdit, canPublish, canSeeStatistics} = p;
          let {id: uid, name: username} = p.User;
          if (currentUid !== uid) {
            let permission = new Permission();
            permission.uid = uid;
            permission.username = username;
            permission.canEdit = canEdit;
            permission.canPublish = canPublish;
            permission.canSeeStatistics = canSeeStatistics;
            this.usersPermissions.push(permission);
          }
        });
      } else {
        var errorDesc: any = {
          Title: 'CANNOT_RETRIEVE_PERMISSIONS_TITLE',
          Desc: 'CANNOT_RETRIEVE_PERMISSIONS_DESC'
        };
        this.modalService.init(ErrorPopupComponent, errorDesc, {});
      }
    }, (error) => {
      var errorDesc: any = {
        Title: 'CANNOT_RETRIEVE_PERMISSIONS_TITLE',
        Desc: 'CANNOT_RETRIEVE_PERMISSIONS_DESC'
      };
      this.modalService.init(ErrorPopupComponent, errorDesc, {});
    });
  }

  toggleEdit(userPermission: Permission) {
    console.log(`${userPermission.username}: ${userPermission.canEdit} -> ${!userPermission.canEdit}`);
    userPermission.canEdit = !userPermission.canEdit;
    this.updateFlowPermissions(userPermission).catch(_ => {
      userPermission.canEdit = !userPermission.canEdit;
    });
  }

  private async updateFlowPermissions(userPermission: Permission) {
    return new Promise((resolve, reject) => {
      this.serverService.setFlowPermissions(this.editorService.getCurrentFlowId(), userPermission)
      .pipe(finalize(() => {
        this.loadingUsers = false;
      }))
      .subscribe(({success, data}: StatusResponse) => {
        if (success) {
          this.toasterService.pop('success', this.translateService.instant('UPDATED_PERMISSIONS'));
          resolve(true);
        }else{
          reject();
        }
      }, (error) => {
        var errorDesc: any = {
            Title: 'CANNOT_UPDATE_PERMISSIONS_TITLE',
            Desc: 'CANNOT_UPDATE_PERMISSIONS_DESC'
          };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
          reject();
      });
    });
  }

  togglePublish(userPermission: Permission) {
    console.log(`${userPermission.username}: ${userPermission.canPublish} -> ${!userPermission.canPublish}`);
    userPermission.canPublish = !userPermission.canPublish;
    this.updateFlowPermissions(userPermission).catch(_ => {
      userPermission.canPublish = !userPermission.canPublish;
    });
  }

  toggleStatistics(userPermission: Permission) {
    console.log(`${userPermission.username}: ${userPermission.canSeeStatistics} -> ${!userPermission.canSeeStatistics}`);
    userPermission.canSeeStatistics = !userPermission.canSeeStatistics;
    this.updateFlowPermissions(userPermission).catch(_ => {
      userPermission.canSeeStatistics = !userPermission.canSeeStatistics;
    });
  }
}
