@import '_variables';
@import '_mixins';

.body-content {
  width: 100%;
  height: calc(100% - 50px);
  .container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    h1 {
      margin-top: 0;
      margin-bottom: 0;
      flex-grow: 0;
      flex-shrink: 0;
      font-family: $fontFamily;
      height: 40px;
      line-height: 40px;
    }

    h3 {
      flex-grow: 1;
      flex-shrink: 1;
      font-family: $fontFamily;
    }

    .signature-container {
      border-style: dashed;
      border-width: 1px;
      width: 100%;
      background-color: #fff;
      flex-grow: 1;
      flex-shrink: 1;
    }

    .buttons-container {
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding: 5px 0;

      button {
        @include popupButton();
      }
    }
  }
}


.overlay {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #00000044;
  z-index: 100000;

  .spinner {
    display: inline-block;
    position: absolute;
    left: calc(50% - 32px);
    top: calc(50% - 32px);
  }
}
