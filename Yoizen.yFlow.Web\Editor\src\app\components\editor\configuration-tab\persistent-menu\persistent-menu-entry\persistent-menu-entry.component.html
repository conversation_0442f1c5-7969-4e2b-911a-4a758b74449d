<div class="button-content" >
  <div class="addName" (click)="onClick(); $event.stopPropagation();">
    <div class="trash" (click)="deleteElement()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'BUTTON_DELETE_TEXT' | translate }}" placement="right" container="body">
      <span class="fa fa-trash-alt"></span>
    </div>
    <div class="name" *ngIf="withName()">{{ getButtonText() }}</div>
    <div class="name-invalid" *ngIf="!withName()">{{ getButtonText() }}</div>
    <div class="block" *ngIf="withRedirect()"><span class="fa fa-table"></span> {{ getBlockName() }}</div>
    <div class="url" *ngIf="withUrl()"><span class="fa fa-link"></span> {{ Model.Url }}</div>
    <div class="login" *ngIf="withAuthLoginUrl()"><span class="fa fa-certificate"></span> {{ Model.AuthLoginUrl}}</div>
    <div class="submenu-selection" *ngIf="hasSubMenu()"
         [ngClass]="{ 'invalid': withInvalidSubmenu() }"
         (click)="$event.stopPropagation(); onExpandMenu.emit(Model.PersistentMenuId);">
      <span class="fa fa-exclamation-triangle invalid"></span>
      <div>{{'OPEN_SUB_MENU' | translate }}</div>
      <span class="fa fa-chevron-right open"></span>
    </div>
  </div>
  <app-add-button-piece #buttonDetail class="button-details"
                        (onClose)="closePopup()"
                        [Model]="Model"
                        *ngIf="!HideDetail && !readOnly"
                        [IsPersistentMenuButton]="true"
                        [AllowedButtonTypes]="AllowedButtonTypes"
                        [DisplayCreateMenuOption]="DisplayCreateMenuOption"></app-add-button-piece>
</div>
