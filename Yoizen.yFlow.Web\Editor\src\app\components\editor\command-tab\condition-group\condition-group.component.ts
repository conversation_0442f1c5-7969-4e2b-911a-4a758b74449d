import { Component, OnInit, Input } from '@angular/core';
import { ConditionGroup, ConcatenationOperator } from 'src/app/models/commands/ConditionGroup';
import { BaseCondition, ConditionTypes } from 'src/app/models/commands/BaseCondition';
import { SingleCondition } from 'src/app/models/commands/SingleCondition';

@Component({
  selector: 'app-condition-group',
  templateUrl: './condition-group.component.html',
  styleUrls: ['./condition-group.component.scss']
})
export class ConditionGroupComponent implements OnInit {

  get operators() : ConcatenationOperator[] {
    return [ ConcatenationOperator.And, ConcatenationOperator.OR];
  }

  @Input() conditionGroup: ConditionGroup;
  @Input() extendedStyles : string = null;
  @Input() totalDepth : number = 1;
  @Input() currentDepth : number = 1;
  @Input() readOnly : boolean = false;

  constructor() { }

  ngOnInit() {
  }


  isGroup(condition:BaseCondition) {
    return condition.type === ConditionTypes.Group;
  }

  isSingle(condition:BaseCondition) {
    return condition.type === ConditionTypes.Single;
  }


  onAddGroup() {
    let group = new ConditionGroup();
    this.conditionGroup.conditionList.push(group);
  }

  onAddSingle() {
    let single = new SingleCondition();
    this.conditionGroup.conditionList.push(single);
  }

  onDelete(index: number) {
    this.conditionGroup.conditionList.splice(index,1);
  }

  getColorForCondition(index: number, length: number) {
    let color = 'rgba(100, 100, 100, ' + (1 - ((index + 1) / length)).toString() + ')';
    return color;
  }
}
