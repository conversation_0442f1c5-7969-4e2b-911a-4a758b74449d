import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";
import {
  AppleInteractiveMessageAuthenticationPiece, AppleInteractiveMessageAuthenticationResponseTypes
} from "../../../../../models/pieces/AppleInteractiveMessageAuthenticationPiece";
import {
  AppleInteractiveMessageIMessageAppPiece
} from "../../../../../models/pieces/AppleInteractiveMessageIMessageAppPiece";

@Component({
  selector: 'app-apple-interactive-message-imessage-app-piece',
  templateUrl: './apple-interactive-message-imessage-app-piece.component.html',
  styleUrls: ['./apple-interactive-message-imessage-app-piece.component.scss']
})
export class AppleInteractiveMessageImessageAppPieceComponent extends BasePieceVM implements OnInit {

  model : AppleInteractiveMessageIMessageAppPiece;
  loading: boolean = false;

  constructor( editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AppleInteractiveMessageIMessageAppPiece;
  }
}
