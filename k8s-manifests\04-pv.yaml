apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-yflow-yoizen-qa
  namespace: yflow-yoizen-qa
spec:
  capacity:
    storage: 1Gi
  accessModes:
  - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: azureblob-nfs-premium
  csi:
    driver: blob.csi.azure.com
    readOnly: false
    volumeHandle: yflow-yoizen-qa
    volumeAttributes:
      resourceGroup: YI-Ops
      storageAccount: aksyflow
      containerName: yflow-qa
      protocol: nfs
    nodeStageSecretRef:
      name: azure-secret
      namespace: yflow-yoizen-qa
