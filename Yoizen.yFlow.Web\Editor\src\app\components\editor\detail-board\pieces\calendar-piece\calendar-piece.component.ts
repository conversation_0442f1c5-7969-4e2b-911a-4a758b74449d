import { BasePieceVM } from './../BasePieceVM';
import { Component, OnInit } from '@angular/core';
import { CalendarPiece } from 'src/app/models/pieces/CalendarPiece';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { BlockDefinition } from '../../../../../models/BlockDefinition';

@Component({
  selector: 'app-calendar-piece',
  templateUrl: './calendar-piece.component.html',
  styleUrls: ['./calendar-piece.component.scss']
})
export class CalendarPieceComponent extends BasePieceVM implements OnInit {
  model: CalendarPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as CalendarPiece;
    if (!this.model.Calendar) {
      this.model.Calendar = this.editorService.createBusinessAvailability();
    }
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
    console.log(this.model);
  }

  onDeleteBlock() {
    this.model.ErrorBlockId = null;
  }
}
