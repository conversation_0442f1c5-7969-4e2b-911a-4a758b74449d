import { Component, OnInit, Input, OnChanges, Output, EventEmitter } from '@angular/core';
import { PieceType } from '../../../../models/PieceType'
import { EditorService } from '../../../../services/editor.service';
import { FlowDefinition } from "../../../../models/FlowDefinition";
import { ChannelTypes } from "../../../../models/ChannelType";
import { FlowTypes } from "../../../../models/FlowType";
import { BlockDefinition } from "../../../../models/BlockDefinition";
import { ErrorPopupComponent } from "../../../error-popup/error-popup.component";
import { ModalService } from "../../../../services/Tools/ModalService";
import { TranslateService } from "@ngx-translate/core";
import { BasePiece } from "../../../../models/pieces/BasePiece";
import { environment } from "../../../../../environments/environment";
import { updateCasePieceEnabledInChat } from 'src/app/Utils/window';
import { SmtpConfiguration } from "../../../../models/SmtpConfiguration";
import { SystemBlocks } from 'src/app/models/SystemBlock';
import { CognitivityProjectType } from 'src/app/models/cognitivity/CognitivityProject';
import { DISABLED_CONTINGENCY_PIECES } from '@contingency/DisabledContingencyPieces';


@Component({
  selector: 'app-menu-pieces',
  templateUrl: './menu-pieces.component.html',
  styleUrls: ['./menu-pieces.component.scss']
})
export class MenuPiecesComponent implements OnInit {
  Pieces: PieceType[];
  flow: FlowDefinition;
  showAdvancedPieces: boolean = false;
  isLite: boolean;
  searchTerm: string = '';
  filteredPieces: PieceType[] = [];
  isContingency: boolean = false;

  @Input() ShowTitle: boolean = true;
  @Output() PieceSelected: EventEmitter<PieceType> = new EventEmitter<PieceType>();

  constructor(private editorService: EditorService,
    private modalService: ModalService,
    private translateService: TranslateService) {
  }

  filterPieces() {
    let piecesToFilter = this.isContingency ?
        this.Pieces.filter(piece => {
            const isDisabled = Object.keys(DISABLED_CONTINGENCY_PIECES).includes(piece.PieceDefinitionType);
            return !isDisabled;
        }) :
        this.Pieces;

    if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.filteredPieces = piecesToFilter;
        return;
    }

    this.filteredPieces = piecesToFilter.filter(piece =>
        this.translateService.instant(piece.Label)
            .toLowerCase()
            .includes(this.searchTerm.toLowerCase())
    );
  }

  ngOnInit() {
    this.flow = this.editorService.getCurrentFlow();
    this.isContingency = this.editorService.getIsContingencyBot();
    this.isLite = this.flow.type === FlowTypes.Lite;
      this.editorService.onBlockSelected.subscribe((block: BlockDefinition) => {
        let auxPiece = this.Pieces.find(p => p.PieceDefinitionType === 'return-to-last-block-piece');
        if (auxPiece !== undefined && auxPiece !== null) {
          auxPiece.Visible = !block.SystemProtected;
        }

        auxPiece = this.Pieces.find(p => p.PieceDefinitionType === 'validate-frontal-dni-piece');
        if (auxPiece !== undefined) {
          auxPiece.Visible = SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString() !== block.Id;
        }

        auxPiece = this.Pieces.find(p => p.PieceDefinitionType === 'data-entry-piece');
        if (auxPiece !== undefined) {
          auxPiece.Visible = SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString() !== block.Id;
        }

        auxPiece = this.Pieces.find(p => p.PieceDefinitionType === 'multimedia-entry-piece');
        if (auxPiece !== undefined) {
          auxPiece.Visible = SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString() !== block.Id;
        }
        this.filteredPieces = this.Pieces;

        if (this.isContingency) {
          const disabledPieces = Object.keys(DISABLED_CONTINGENCY_PIECES);
          this.filteredPieces = this.Pieces.filter(piece => {
              const isDisabled = disabledPieces.includes(piece.PieceDefinitionType);
              return !isDisabled;
          });
        }
      });
    this.editorService.onSmtpConfigured.subscribe((smtpConfig: SmtpConfiguration) => {
      let mailPiece = this.Pieces.find(p => p.PieceDefinitionType === 'mail-piece');
      if (mailPiece !== undefined) {
        mailPiece.Visible = smtpConfig !== null && smtpConfig.enabled;
      }
    });

    this.Pieces = [];
    this.Pieces.push(this.editorService.createPiece('PIECE_MESSAGE', 'fa fa-align-left', 'message-piece', this.editorService.createMesage));
    this.Pieces.push(this.editorService.createPiece('PIECE_COMMENT', 'fa fa-comment', 'comment-piece', this.editorService.createComment));

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let definition = this.editorService.getYSocialSettings();
      this.Pieces.push(this.editorService.createPiece('PIECE_INVOKE_WHATSAPP_FLOW', 'fa fa-sitemap', 'invoke-whatsapp-flow-piece', this.editorService.createInvokeWhatsappFlow));

      if (definition.WhatsappUseInteractive) {
        this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_LIST', 'fa fa-list-ul', 'interactive-message-list-piece', this.editorService.createInteractiveMesageList));
        this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_BUTTONS', 'fa fa-bars', 'interactive-message-buttons-piece', this.editorService.createInteractiveMessageButtons));
        this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_URLBUTTON', 'fa fa-external-link', 'interactive-message-urlbutton-piece', this.editorService.createInteractiveMessageUrlbutton));

        if (definition.WhatsappUseInteractiveCatalog) {
          this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_PRODUCTLIST', 'fa fa-shopping-cart', 'interactive-message-productlist-piece', this.editorService.createInteractiveMessageProductList));
          this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_PRODUCT', 'fa fa-shopping-cart', 'interactive-message-product-piece', this.editorService.createInteractiveMessageProduct));
        }
      }
    }

    if (this.flow.type !== FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_DB_QUERY', 'fa fa-database', 'db-query-piece', this.editorService.createDbQuery));
    }

    if ((this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.GoogleRBM) &&
      this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_GALLERY', 'fa fa-images', 'gallery-piece', this.editorService.createGallery));
      this.Pieces.push(this.editorService.createPiece('PIECE_DYNAMICGALLERY', 'fa fa-images', 'dynamic-gallery-piece', this.editorService.createDynamicGallery));
    }

    this.Pieces.push(this.editorService.createPiece('PIECE_ATTACHMENT', 'fa fa-paperclip', 'attachment-piece', this.editorService.createAttachment));

    if (this.flow.channel === ChannelTypes.Chat && this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_VIDEO_EMBED', 'fa fa-file-video', 'video-embed-piece', this.editorService.createVideoEmbed));
      this.Pieces.push(this.editorService.createPiece('PIECE_STORE_MESSAGE', 'fa fa-bolt', 'store-message-piece', this.editorService.createStoreMessage));
      this.Pieces.push(this.editorService.createPiece('PIECE_AUTHENTICATE_ANONYMOUS_PROFILE', 'fa fa-user-secret', 'authenticate-anonymous-profile-piece', this.editorService.createAuthenticateAnonymousProfilePiece));
    }

    if (this.flow.channel === ChannelTypes.AppleMessaging && this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_FORM', 'fa fa-ballot', 'form-piece', this.editorService.createForm));
      this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_LIST', 'fa fa-list-ul', 'interactive-message-list-piece', this.editorService.createInteractiveMesageList));
      this.Pieces.push(this.editorService.createPiece('PIECE_INTERACTIVE_MESSAGE_BUTTONS', 'fa fa-bars', 'interactive-message-buttons-piece', this.editorService.createInteractiveMessageButtons));
      this.Pieces.push(this.editorService.createPiece('PIECE_RICH_LINK', 'fa fa-link', 'rich-link-piece', this.editorService.createRichLink, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_TIME_PICKER', 'fa fa-calendar-check', 'time-picker-piece', this.editorService.createTimePicker, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_APPLE_INTERATIVE_MESSAGE_AUTHENTICATION', 'fa fa-sign-in-alt', 'apple-interactive-message-authentication-piece', this.editorService.createAppleInteractiveMessageAuthentication, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP', 'fab fa-app-store-ios', 'apple-interactive-message-imessage-app-piece', this.editorService.createAppleInteractiveMessageIMessageApp, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_APPLE_INTERATIVE_MESSAGE_APPLE_PAY', 'fab fa-apple-pay', 'apple-interactive-message-applepay-piece', this.editorService.createAppleInteractiveMessageApplePay, null, false));
    }

    this.Pieces.push(this.editorService.createPiece('PIECE_TAG', 'fa fa-tag', 'tag-piece', this.editorService.createTag));

    if ((this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Twitter) &&
      this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply, ['message-piece']));
    }

    if ((
      this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.AppleMessaging
    ) &&
      this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_ACTIONS', 'fa fa-comment-dots', 'actions-piece', this.editorService.createAction));
    }

    if ((this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype) &&
      this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_JSON', 'fa fa-code', 'json-piece', this.editorService.createJson, null, true));
    }

    if (this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_INTEGRATION', 'fa fa-server', 'integration-piece', this.editorService.createIntegration, null, true));

      this.Pieces.push(this.editorService.createPiece('PIECE_MAIL', 'fa fa-envelope', 'mail-piece', this.editorService.createMail, null, true));
    }

    if (!environment.standAlone) {
      this.Pieces.push(this.editorService.createPiece('PIECE_OPERATOR', 'fa fa-user', 'derive-piece', this.editorService.createDerivation));
      if (this.flow.type != FlowTypes.Lite) {
        this.Pieces.push(this.editorService.createPiece('PIECE_MARK_MESSAGE_AS_PENDING', 'fa fa-alarm-clock', 'mark-message-as-pending-piece', this.editorService.createMarkMessageAsPendingPiece, null, true));
      }
    }

    this.Pieces.push(this.editorService.createPiece('PIECE_ENCRYPT', 'fa fa-id-card', 'encrypt-piece', this.editorService.createEncryptPiece, null, true));
    this.Pieces.push(this.editorService.createPiece('PIECE_DECRYPT', 'fa fa-id-card', 'decrypt-piece', this.editorService.createDecryptPiece, null, true));
    this.Pieces.push(this.editorService.createPiece('PIECE_DATAENTRY', 'fa fa-edit', 'data-entry-piece', this.editorService.createDataEntry));
    this.Pieces.push(this.editorService.createPiece('PIECE_MULTIMEDIA_ENTRY', 'fa fa-file-image', 'multimedia-entry-piece', this.editorService.createMultiMediaEntry));
    this.Pieces.push(this.editorService.createPiece('PIECE_JUMPTOBLOCK', 'fa fa-external-link-alt', 'jump-to-block-piece', this.editorService.createJumpToBlock));
    this.Pieces.push(this.editorService.createPiece('PIECE_SWITCHJUMPTOBLOCK', 'fa fa-sitemap fa-rotate-270', 'switch-jump-to-block-piece', this.editorService.createSwitchJumpToBlock));
    this.Pieces.push(this.editorService.createPiece('PIECE_VARIABLECONDITION', 'fa fa-check', 'variable-condition-piece', this.editorService.createVariableCondition));

    if (this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_GEOCODER_GOOGLE', 'fas fa-map-marker-alt', 'geocoder-google-piece', this.editorService.createGeocoderGoogle, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_CONDITION', 'fa fa-check', 'condition-piece', this.editorService.createCondition));
      this.Pieces.push(this.editorService.createPiece('PIECE_SETVARIABLE', 'fa fa-sort-numeric-down', 'set-variable', this.editorService.createSetVariable));
      this.Pieces.push(this.editorService.createPiece('PIECE_SHORTENURL', 'fa fa-code', 'shorten-url', this.editorService.createShortenUrlPiece));

      this.Pieces.push(this.editorService.createPiece('PIECE_CALLBLOCKASPROCEDURE', 'fa fa-file-import', 'call-block-as-procedure-piece', this.editorService.createCallBlockAsProcedure));

      let piece = this.editorService.createPiece('PIECE_RETURNTOLASTBLOCK', 'fa fa-undo-alt', 'return-to-last-block-piece', this.editorService.createReturnToLastBlock);
      piece.Visible = false;
      this.Pieces.push(piece);

      this.Pieces.push(this.editorService.createPiece('PIECE_CONCATENATE', 'fa fa-sitemap', 'concatenate-piece', this.editorService.createConcatenate));
      this.Pieces.push(this.editorService.createPiece('PIECE_MULTIPLEMESSAGES', 'fa fa-align-left', 'multiple-messages-piece', this.editorService.createMultipleMessage, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_RESETVARIABLES', 'fa fa-tools', 'reset-variables-piece', this.editorService.createResetVariables));
      this.Pieces.push(this.editorService.createPiece('PIECE_NEARESTCOORDINATES', 'fa fa-map-marked-alt', 'nearest-coordinates-piece', this.editorService.createNearestCoordinates, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_GETELEMENTFROMARRAY', 'fa fa-tasks', 'get-element-from-array-piece', this.editorService.createGetElementFromArray, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_GETELEMENTSFROMARRAY', 'fa fa-tasks', 'get-elements-from-array-piece', this.editorService.createGetElementsFromArray, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_MULTIPLEATTACHMENTS', 'fa fa-paperclip', 'multiple-attachments-piece', this.editorService.createMultipleAttachments, null, true));
    }

    this.Pieces.push(this.editorService.createPiece('PIECE_UPDATEPROFILE', 'fa fa-user-edit', 'update-profile-piece', this.editorService.createUpdateProfile));
    //this.Pieces.push(this.editorService.createPiece('PIECE_PROFILE_LIST', 'fa fa-user-edit', 'profile-list-piece', this.editorService.createProfileListPiece));

    if (!environment.standAlone &&
      this.flow.channel !== ChannelTypes.Chat) {
      this.Pieces.push(this.editorService.createPiece('PIECE_CLOSECASE', 'fa fa-edit', 'close-case-piece', this.editorService.createCloseCase));
      if (this.flow.type != FlowTypes.Lite) {
        this.Pieces.push(this.editorService.createPiece('PIECE_UPDATECASE', 'fa fa-edit', 'update-case-piece', this.editorService.createUpdateCase));
      }
    }
    if (this.flow.channel === ChannelTypes.Chat &&
      updateCasePieceEnabledInChat()) {
      this.Pieces.push(this.editorService.createPiece('PIECE_UPDATECASE', 'fa fa-edit', 'update-case-piece', this.editorService.createUpdateCase));
    }
    if (this.flow.channel === ChannelTypes.Telegram || this.flow.channel === ChannelTypes.WhatsApp) {
      this.Pieces.push(this.editorService.createPiece('PIECE_STICKER', 'fa fa-sticky-note', 'sticker-piece', this.editorService.createStickerPiece));
    }

    if ((this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Telegram ||
      this.flow.channel === ChannelTypes.WhatsApp ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Generic ||
      this.flow.channel === ChannelTypes.AppleMessaging ||
      this.flow.channel === ChannelTypes.GoogleRBM) &&
      (this.flow.type === FlowTypes.Bot)) {
      if (this.flow.channel !== ChannelTypes.Chat) {
        this.Pieces.push(this.editorService.createPiece('PIECE_PAYMENT_GATEWAY', 'fa fa-credit-card', 'payment-gateway-piece', this.editorService.createPaymentGateway, null, true));
        this.Pieces.push(this.editorService.createPiece('PIECE_SIGNATURE_PAD', 'fa fa-file-signature', 'signature-pad-piece', this.editorService.createSignaturePadPiece, null, true));
        this.Pieces.push(this.editorService.createPiece('PIECE_ACCOUNT_LINKING', 'fas fa-user-check', 'account-linking-piece', this.editorService.createAccountLinkingPiece, null, true));
        this.Pieces.push(this.editorService.createPiece('PIECE_ACCOUNT_UNLINKING', 'fas fa-user-times', 'account-unlinking-piece', this.editorService.createAccountUnlinkingPiece, null, true));
        this.Pieces.push(this.editorService.createPiece('PIECE_ENCODE_BASE64_IMAGE', 'fa fa-file-image', 'encode-base64-image', this.editorService.createEncodeBase64ImagePiece, null, true));
        //TODO: Todavía no está implementado, se comenta para que no aparezca
        //this.Pieces.push(this.editorService.createPiece('PIECE_BIOMETRIC', 'fas fa-camera', 'biometric-piece', this.editorService.createBiometricPiece, null, true));
      }
      this.Pieces.push(this.editorService.createPiece('PIECE_VALIDATE_FRONTAL_DNI', 'fa fa-id-card', 'validate-frontal-dni-piece', this.editorService.createValidateFrontalDni, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_VALIDATE_BACK_DNI', 'fa fa-id-card', 'validate-back-dni-piece', this.editorService.createValidateBackDni, null, true));
    }

    if (this.flow.channel !== ChannelTypes.MercadoLibre && this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_COORDINATES', 'fa fa-map-pin', 'coordinates-piece', this.editorService.createCoordinates));
    }

    if (this.flow.channel === ChannelTypes.Chat && this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_MULTIPLECOORDINATES', 'fa fa-map-marked', 'multiple-coordinates-piece', this.editorService.createMultipleCoordinates, null, true));
      this.Pieces.push(this.editorService.createPiece('PIECE_POSTMESSAGE', 'fa fa-inbox-out', 'post-message-piece', this.editorService.createPostMessage, null, false));
    }

    if (this.flow.type != FlowTypes.Lite) {
      this.Pieces.push(this.editorService.createPiece('PIECE_STATISTIC_EVENT', 'fa fa-chart-bar', 'statistic-event-piece', this.editorService.createStatisticEventPiece, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_EVALUATE_COMMANDS', 'fa fa-brain', 'evaluate-commands-piece', this.editorService.createEvaluateCommands, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_LOG', 'fa fa-bug', 'log-piece', this.editorService.createLog, null, true));
      if (this.flow.channel !== ChannelTypes.Chat) {
        this.Pieces.push(this.editorService.createPiece('PIECE_SEND_HSM', 'fas fa-paper-plane', 'send-hsm-piece', this.editorService.createSendHsm, null, false));
      }
    }
    this.Pieces.push(this.editorService.createPiece('PIECE_CALENDAR', 'far fa-calendar-alt', 'calendar-piece', this.editorService.createCalendarCondition));

    if (this.flow.channel === ChannelTypes.WhatsApp || true) { //TODO: Remover el true
      this.Pieces.push(this.editorService.createPiece('PIECE_WA_MENU', 'far fa-list-alt', 'wa-menu-piece', this.editorService.createWAMenu, null, false));
    }

    var cognitivityEnabled = this.editorService.isCognitivityEnabled();
    //ToDo: Filtrar de forma dinamica no HC mediante configuracion de ySmart
    if (cognitivityEnabled) {
      this.Pieces.push(this.editorService.createPiece('PIECE_EVALUATE_COGNITIVITY', 'fas fa-robot', 'evaluate-cognitivity-piece', this.editorService.createEvaluateCognitivity, null, false));
      this.Pieces.push(this.editorService.createPiece('PIECE_SETVARIABLEFROMENTITY', 'fa fa-sort-numeric-down', 'set-variable-from-entity', this.editorService.createSetVariableFromEntity, null, false));
      if (this.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.RASA &&
          this.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.WITAI &&
          this.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.WATSON) {
        this.Pieces.push(this.editorService.createPiece('PIECE_SMART_FORM', 'fas fa-tasks', 'smart-form-piece', this.editorService.createSmartForm, null, false));
        this.Pieces.push(this.editorService.createPiece('PIECE_KNOWLEDGEBASE', 'fa fa-database', 'knowledge-base-piece', this.editorService.createKnowledgeBasePiece, null, false));
        this.Pieces.push(this.editorService.createPiece('PIECE_GETMESSAGEENTITIES', 'fa fa-database', 'get-message-entities-piece', this.editorService.createGetMessageEntitiesPiece, null, false));
        this.Pieces.push(this.editorService.createPiece('PIECE_MULTIMEDIA_ANALYSIS', 'fa fa-file-image', 'multimedia-analysis-piece', this.editorService.createMultimediaAnalysisPiece));
      }
    }

    this.Pieces.sort((a, b) => {
      const translateA = this.translateService.instant(a.Label);
      const translateB = this.translateService.instant(b.Label);
      if (translateB > translateA) {
        return -1;
      }
      if (translateB < translateA) {
        return 1;
      }
      return 0;
    });

    if (this.isContingency) {
      const disabledPieces = Object.keys(DISABLED_CONTINGENCY_PIECES);
      this.filteredPieces = this.Pieces.filter(piece => {
          const isDisabled = disabledPieces.includes(piece.PieceDefinitionType);
          return !isDisabled;
      });
    } else {
        this.filteredPieces = [...this.Pieces];
    }

  }

  public addNewPiece(pieceDefinition: PieceType) {
    this.PieceSelected.emit(pieceDefinition);
  }

  refreshPieces(value: boolean) {
    this.showAdvancedPieces = value;
    this.Pieces.forEach(piece => {
      if (piece.IsAdvancePiece) {
        piece.Visible = this.showAdvancedPieces;
      }
    });

    if (this.isContingency) {
        const disabledPieces = Object.keys(DISABLED_CONTINGENCY_PIECES);
        this.filteredPieces = this.Pieces.filter(piece => {
            const isDisabled = disabledPieces.includes(piece.PieceDefinitionType);
            return !isDisabled;
        });
    } else {
        this.filteredPieces = [...this.Pieces];
    }
  }

}
