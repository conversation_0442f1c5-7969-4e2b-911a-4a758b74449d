import { Component, OnInit, Input } from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';

@Component({
  selector: 'app-hsm-jump',
  templateUrl: './hsm-jump.component.html',
  styleUrls: ['./hsm-jump.component.scss']
})
export class HsmJumpComponent implements OnInit {
  @Input() readOnly: boolean = false;
  isHsmJumpEnabled: boolean = false;


  constructor(public editorService: EditorService) { }

  ngOnInit() {
    this.isHsmJumpEnabled = this.editorService.isHsmJumpEnabled();
  }

  onHsmJumpEnabledChange(value: boolean) {
    if (value) {
      this.editorService.enableHsmJump();
    } else {
      this.editorService.disableHsmJump();
    }
  }

}
