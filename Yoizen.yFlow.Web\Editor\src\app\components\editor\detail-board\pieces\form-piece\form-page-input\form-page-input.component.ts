import {Component, Input, OnInit} from '@angular/core';
import {
  FormInputOptions, FormInputOptionKeyboardTypes, FormInputOptionTextContentTypes, FormInputOptionTypes,
  FormInputPage,
  FormItemSelect,
  FormPage,
  FormPiece,
  FormSelectPage
} from "../../../../../../models/pieces/FormPiece";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../../models/TypeDefinition";
import {EditorService} from "../../../../../../services/editor.service";

@Component({
  selector: 'app-form-page-input',
  templateUrl: './form-page-input.component.html',
  styleUrls: ['./form-page-input.component.scss']
})
export class FormPageInputComponent implements OnInit {
  @Input() basePage: FormPage;
  @Input() pageIndex: number;
  @Input() form: FormPiece;
  @Input() readOnly : boolean = false;
  page: FormInputPage;
  option: FormInputOptions;
  variableTypes = TypeDefinition;
  inputTypes = FormInputOptionTypes;
  keyboardTypes = FormInputOptionKeyboardTypes;
  textContentTypes = FormInputOptionTextContentTypes;

  get variableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.page.variableId);
  }

  constructor(public editorService: EditorService) { }

  ngOnInit() {
    this.page = <FormInputPage> this.basePage;
    this.option = this.page.options;
  }

  getVariableTypes() : TypeDefinition[] {
    return new Array<TypeDefinition>(TypeDefinition.Text);
  }

  setVariable(variableData: VariableDefinition) {
    this.page.variableId = variableData ? variableData.Id : -1;
  }
}
