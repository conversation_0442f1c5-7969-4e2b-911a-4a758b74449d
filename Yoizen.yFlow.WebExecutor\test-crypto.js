// Test simple para verificar que crypto-js funciona correctamente
import * as CryptoJS from 'crypto-js';

console.log('Testing CryptoJS...');
console.log('CryptoJS object:', typeof CryptoJS);
console.log('CryptoJS.enc:', typeof CryptoJS.enc);
console.log('CryptoJS.enc.Utf8:', typeof CryptoJS.enc.Utf8);

try {
    const message = "Hello World";
    const key = "my-secret-key";
    
    // Test encryption
    const encrypted = CryptoJS.AES.encrypt(message, key);
    console.log('Encrypted:', encrypted.toString());
    
    // Test decryption
    const decrypted = CryptoJS.AES.decrypt(encrypted, key);
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    console.log('Decrypted:', decryptedText);
    
    if (decryptedText === message) {
        console.log('✅ CryptoJS is working correctly!');
    } else {
        console.log('❌ CryptoJS decryption failed');
    }
} catch (error) {
    console.error('❌ Error testing CryptoJS:', error.message);
}
