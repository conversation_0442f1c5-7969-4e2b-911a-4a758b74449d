<div class="single-condition" [ngClass]="{ 'invalid': !singleCondition.isValid() }">
  <div class="operation">
    <select class="select" name="" id="" [(ngModel)]="singleCondition.operation"
            [disabled]="readOnly">
      <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}
      </option>
    </select>
  </div>
  <div class="operator" *ngIf="shouldDisplayOperand()">
    <app-input-with-variables [placeholder]="'CONDITION_OPERAND' | translate"
                              [(value)]="singleCondition.value"
                              [disabled]="readOnly"
                              [isTextArea]="singleCondition.operation == 'contains_multiple'"
                              [wideInput]="true"
                              [validator]="getInputValidator()"></app-input-with-variables>
  </div>
</div>
