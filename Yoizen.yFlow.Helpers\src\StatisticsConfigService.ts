import { logger } from './Logger';

/**
 * Interface for statistics configuration
 */
export interface StatisticsConfigData {
    consolidatedEnabled: boolean;
    detailedEnabled: boolean;
}

/**
 * Service to manage statistics configuration state
 */
export class StatisticsConfigService {
    private static currentConfig: StatisticsConfigData = {
        consolidatedEnabled: true,
        detailedEnabled: true
    };
    
    /**
     * Update statistics configuration
     * @param config New configuration to apply
     */
    public static updateConfig(config: StatisticsConfigData): void {
        try {
            this.currentConfig = {
                consolidatedEnabled: config.consolidatedEnabled !== undefined ? config.consolidatedEnabled : true,
                detailedEnabled: config.detailedEnabled !== undefined ? config.detailedEnabled : true
            };
            logger.info(`Statistics configuration updated: consolidatedEnabled=${this.currentConfig.consolidatedEnabled}, detailedEnabled=${this.currentConfig.detailedEnabled}`);
        } catch (error) {
            logger.error({ error: error }, 'Error updating statistics configuration');
        }
    }
    
    /**
     * Get current statistics configuration
     * @returns Current StatisticsConfigData
     */
    public static getStatisticsConfig(): StatisticsConfigData {
        return this.currentConfig;
    }
    
    /**
     * Check if consolidated statistics are enabled
     * @returns boolean indicating if consolidated statistics should be saved
     */
    public static isConsolidatedEnabled(): boolean {
        return this.currentConfig.consolidatedEnabled;
    }
    
    /**
     * Check if detailed statistics are enabled
     * @returns boolean indicating if detailed statistics should be saved
     */
    public static isDetailedEnabled(): boolean {
        return this.currentConfig.detailedEnabled;
    }
}
