<div class="evaluate-commands card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-brain icon"></span> {{ 'CARD_EVALUATE_COMMANDS_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_EVALUATE_COMMANDS_INFO' | translate"></span>
  </div>
  <div class="commands" *ngIf="!model.EvaluateCommand">
    <span class="title">{{'EVALUATE_COMMANDS_LABEL' | translate}}:</span>
    <ui-switch [(ngModel)]="model.EvaluateCommand" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="commands" *ngIf="model.EvaluateCommand">
    <span class="title">{{'EVALUATE_COMMANDS_USE_MESSAGE_TEXT' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UseMessageText" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="expression" *ngIf="model.EvaluateCommand && !model.UseMessageText">
    <span class="title">{{'EVALUATE_COMMANDS_EXPRESSION' | translate}}:</span>
    <app-input-with-variables placeholder="{{ '{' + '{text}' + '}' }}"
                              [(value)]="model.Expression"
                              class="input-variable-area"
                              [disabled]="readOnly"
                              [wideInput]="true"></app-input-with-variables>
  </div>
</div>
