import { Component, OnInit, Renderer2, <PERSON>ementRef, ViewChild } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { QuickReplyPiece , QuickReplyOption} from '../../../../../models/pieces/QuickReplyPiece';
import { VariableDefinition} from '../../../../../models/VariableDefinition';
import { isStringValid } from '../../../../../urlutils.module';
import {BlockDefinition} from "../../../../../models/BlockDefinition";
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import {ChannelTypes} from "../../../../../models/ChannelType";
import {MessagePieceType} from "../../../../../models/pieces/MessagePieceType";

@Component({
  selector: 'app-quick-replies-piece',
  templateUrl: './quick-replies-piece.component.html',
  styleUrls: ['./quick-replies-piece.component.scss']
})
export class QuickRepliesPieceComponent extends BasePieceVM implements OnInit {

  @ViewChild('varPicker', { static: false }) VarPicker : ElementRef;

  model : QuickReplyPiece;
  parentBlock : BlockDefinition;
  expandButton : QuickReplyOption;
  currentChannel : string;
  showStoreVariable : boolean = true;
  VariableData : VariableDefinition;
  SearchVariableString : string;

  constructor( editorService : EditorService, public modalService : ModalService, private renderer : Renderer2, private singleOverlay:SingleOverlayService ) {
    super(editorService, modalService);
   }

  ngOnInit() {
    this.model = this.context as QuickReplyPiece;
    this.VariableData = this.model.VariableId != -1? this.editorService.findVariableWithId(this.model.VariableId) : null;

    this.parentBlock = this.editorService.getSelectedBlock();

    let flow = this.editorService.getCurrentFlow();
    this.currentChannel = flow.channel;
    if (flow.channel === ChannelTypes.Telegram) {
      this.showStoreVariable = false;
    }
  }

  onShowButtonDetail(btn : QuickReplyOption) {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = btn;
  }

  isValidString(str) {
      return isStringValid(str);
  }

  hasVariable(): Boolean{
    return this.VariableData != null;
  }

  deleteVariable() {
    this.VariableData = null;
    this.SearchVariableString = '';
    this.model.VariableId = null;
  }

  onVarSelect(varData) {
    this.VariableData = this.editorService.findVariableWithId(varData.Id);
    this.SearchVariableString = varData.Name;
    this.model.VariableId = varData.Id;
  }

  onInputFocusInVar() {

    this.renderer.removeClass(this.VarPicker.nativeElement, 'hide');
  }

  onInputFocusOutVar() {

    setTimeout(() => {
      this.renderer.addClass(this.VarPicker.nativeElement, 'hide');
    }, 500);
  }

  addOption() {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = this.model.addOption();
    this.model.Options.push(this.expandButton);
  }

  removeOption(element) {
    this.model.Options.splice(element, 1);
  }

  moveRightOption(index: number) {
    let option = this.model.Options[index + 1];
    this.model.Options[index + 1] = this.model.Options[index];
    this.model.Options[index] = option;
  }

  moveLeftOption(index: number) {
    let option = this.model.Options[index - 1];
    this.model.Options[index - 1] = this.model.Options[index];
    this.model.Options[index] = option;
  }

  getVariableType(varDef: VariableDefinition) {
    if (typeof(varDef) === 'undefined' || varDef === null) {
      return '';
    }

    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (varDef.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  getButtonStats(button: QuickReplyOption) : any {
    if (this.stats === null ||
      typeof(this.stats.buttons) === 'undefined' ||
      this.stats.buttons === null) {
      return null;
    }

    if (button.BlockId === "-1") {
      return null;
    }

    let buttonStatsInfo = this.stats.buttons.find(b => b.id === button.Uid && b.destBlock === button.BlockId);
    if (typeof(buttonStatsInfo) === 'undefined') {
      return null;
    }
    return buttonStatsInfo;
  }

  isInInvalidPosition() : boolean {
    if (this.parentBlock !== null) {
      let currentIndex = this.parentBlock.Pieces.findIndex(p => p.Id === this.model.Id);
      if (currentIndex !== -1) {
        if (currentIndex === 0) {
          return true;
        }

        let previousPiece = this.parentBlock.Pieces[currentIndex - 1];
        if (previousPiece.type !== 'message-piece' &&
          previousPiece.type !== 'gallery-piece' &&
          previousPiece.type !== 'dynamic-gallery-piece' &&
          previousPiece.type !== 'attachment-piece' &&
          previousPiece.type !== 'video-embed-piece') {
          return true;
        }

        if (this.currentChannel === ChannelTypes.Telegram) {
          if (previousPiece.type === 'message-piece') {
            let messagePiece = previousPiece as MessagePieceType;
            if (messagePiece.Buttons !== null && messagePiece.Buttons.length > 0) {
              return true;
            }
          }
        }

      }
    }
    return false;
  }
}
