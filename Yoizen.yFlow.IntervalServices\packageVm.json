{"name": "yoizen.yflow.intervalservices", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node --env-file=.env --loader ts-node/esm src/index.ts", "startDocker": "node ./dist/Yoizen.yFlow.IntervalServices/src/index.js", "dev": "node --env-file=.env --watch --loader ts-node/esm src/index.ts", "build": "tsc", "buildDocker": "tsc --build tsconfig.docker.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "console-stamp": "^3.1.2", "crypto-js": "4.1.1", "jszip": "^3.10.1", "moment": "^2.30.1", "mssql": "^6.4.1", "mysql2": "^3.9.7", "node-cache": "^5.1.2", "node-xlsx": "^0.24.0", "sequelize": "^6.37.2", "zip-dir": "^2.0.0", "@azure/storage-blob": "12.17.0", "@azure/logger": "1.0.0", "@azure/core-util": "1.11.0", "redis": "^4.6.13", "uuid": "^9.0.1", "app-root-path": "^3.0.0", "jsonwebtoken": "^9.0.2", "excel4node": "^1.8.2", "i18n": "^0.15.1", "csv-stringify": "^6.5.0", "basic-ftp": "^4.6.3", "stream-buffers": "^3.0.2", "ssh2-sftp-client": "^7.0.4", "pino": "8.21.0", "pino-http": "9.0.0", "pino-pretty": "10.0.0"}, "devDependencies": {"@types/crypto-js": "4.1.1", "@types/mssql": "^9.1.5", "@types/mysql": "^2.15.26", "@types/node": "^20.12.4", "ts-node": "^10.9.2", "typescript": "^4.9.5", "@types/cors": "^2.8.17", "@types/uuid": "^10.0.0"}}