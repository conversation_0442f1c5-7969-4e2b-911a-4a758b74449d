import { BasePiece } from './BasePiece';
import { jsonArrayMember, jsonMember, jsonObject } from "typedjson";
import { EditorService } from 'src/app/services/editor.service';
import { EncryptType } from './AccountLinkingPiece'; // ToDo: Mover a una clase comun

@jsonObject
export class CharacterReplacement {
  @jsonMember(String)
  value: string;
  @jsonMember(String)
  replaceWith: string;
}

@jsonObject
export class DecryptPiece extends BasePiece {
  @jsonMember(Number)
  VariableToDecryptId: number = null;
  @jsonMember(Number)
  VariableDecryptedId: number = null;
  @jsonMember(Number)
  DecryptModeType: Number = 0;
  @jsonMember(Number)
  DecryptPaddingType: Number = 0;
  @jsonMember(String)
  DecryptKey: string = '';
  @jsonMember(String)
  ErrorMessage: string = null;
  @jsonMember(String, { deserializer: id => id.toString() })
  ErrorBlockId: string = "-1";
  @jsonMember(Number)
  DecryptType: EncryptType = EncryptType.AES;

  // New properties for custom method support
  @jsonMember(Boolean)
  public CustomMethod: boolean = false;
  @jsonMember(String)
  public Delimiter: string = null;
  @jsonMember(Number)
  public IvBytesLength: number = 16; // Default to 128 bits (16 bytes)
  @jsonMember(Boolean)
  public ReplaceCharacters: boolean = false;
  @jsonArrayMember(CharacterReplacement)
  CharactersToReplace: CharacterReplacement[] = [];

  constructor() {
    super();
  }

  isValid(editorService: EditorService): boolean {

    if (!this.isDecryptKeyValid()) {
      return false;
    }

    if (!this.isVariableToDecryptValid(editorService)) {
      return false;
    }

    if (!this.isVariableDecryptedValid(editorService)) {
      return false;
    }

    if (this.ErrorBlockId !== "-1" && !this.isErrorBlockValid(editorService)) {
      return false;
    }

    return true;
  }

  //#region Validaciones de pieza
  isDecryptKeyValid(): boolean {
    return this.DecryptKey !== null && this.DecryptKey.trim() !== '';
  }

  isVariableToDecryptValid(editorService: EditorService): boolean {
    if (this.VariableToDecryptId == null) {
      return false;
    }
    const variable = editorService.findVariableWithId(this.VariableToDecryptId);
    return variable != null;
  }

  isVariableDecryptedValid(editorService: EditorService): boolean {
    if (this.VariableDecryptedId == null) {
      return false;
    }
    const variable = editorService.findVariableWithId(this.VariableDecryptedId);
    return variable != null;
  }

  isErrorBlockValid(editorService: EditorService): boolean {
    if (this.ErrorBlockId == null || this.ErrorBlockId === "-1") {
      return false;
    }
    const block = editorService.findBlockWithId(this.ErrorBlockId);
    return block != null;
  }

  isCustomMethodConfigurationValid(): boolean {
    if (!this.CustomMethod) {
      // If custom method is disabled, legacy configuration is always valid 
      return true;
    }

    if (this.requiresIV() && (this.IvBytesLength == null || this.IvBytesLength <= 0)) {
      console.log('isCustomMethodConfigurationValid: IvBytesLength is required for algorithms that use IV when custom method is enabled.');
      return false;
    }

    if (this.IvBytesLength != null && this.IvBytesLength % 4 !== 0) {
      console.log('isCustomMethodConfigurationValid: IvBytesLength must be a multiple of 4.');
      return false;
    }

    if (this.ReplaceCharacters) {

      if (!this.CharactersToReplace || this.CharactersToReplace.length === 0) {
        console.log('isCustomMethodConfigurationValid: CharactersToReplace array is required when ReplaceCharacters is enabled.');
        return false;
      }

      // Validate characters to replace structure
      if (this.CharactersToReplace && this.CharactersToReplace.length > 0) {
        for (let i = 0; i < this.CharactersToReplace.length; i++) {
          const char = this.CharactersToReplace[i];
          if (!char.value || !char.replaceWith) {
            console.log(`isCustomMethodConfigurationValid: Invalid character replacement at index ${i}. Both 'value' and 'replaceWith' are required.`);
            return false;
          }
        }
      }
    }

    return true;
  }

  private requiresIV(): boolean {
    return this.DecryptType === EncryptType.AES || this.DecryptType === EncryptType.TripleDES;
  }
  //#endregion
}
