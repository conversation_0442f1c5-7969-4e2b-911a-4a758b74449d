{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "Y<PERSON><PERSON><PERSON><PERSON>s"}, "apps": [{"root": "src", "outDir": "dist", "assets": ["./favicon.ico", "./assets/"], "index": "index.html", "main": "main.ts", "polyfills": "polyfills.ts", "test": "test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "styles": ["sass/styles.scss", "../node_modules/bootstrap/dist/css/bootstrap.min.css"], "stylePreprocessorOptions": {"includePaths": ["./", "./sass"]}, "scripts": [], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.prod.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json", "exclude": "**/node_modules/**"}, {"project": "src/tsconfig.spec.json", "exclude": "**/node_modules/**"}, {"project": "e2e/tsconfig.e2e.json", "exclude": "**/node_modules/**"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"styleExt": "css", "component": {}, "build": {"progress": true}}}