import {Component, EventEmitter, Input, OnInit} from '@angular/core';
import { FormatDefinition, FormatDefinitionType } from 'src/app/models/FormatDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import * as moment from 'moment';
import * as numeral from 'numeral';
import { TranslateService } from '@ngx-translate/core';
import {DeleteGroupQuestionComponent} from "../../popups/delete-group-question/delete-group-question.component";
import {DeleteInput} from "../../../../models/UI/DeleteInput";
import {ModalService} from "../../../../services/Tools/ModalService";
import {DefaultSettings} from "../../../../models/DefaultSettings";
import { getDefaultLanguaje } from 'src/app/Utils/window';

@Component({
  selector: 'app-string-format-selection',
  templateUrl: './string-format-selection.component.html',
  styleUrls: ['./string-format-selection.component.scss']
})
export class StringFormatSelectionComponent implements OnInit {

  @Input() readOnly: boolean = false;

  get formatList() : FormatDefinition[] {
    return this.editorService.getFormatDefinitions();
  }

  get userFormatList() : FormatDefinition[] {
    return this.editorService.getUserFormatDefinition();
  }

  get defaultSettings() : DefaultSettings {
    return this.editorService.getDefaultSettings();
  }

  formatTypes = FormatDefinitionType;
  currentDate : moment.Moment;

  constructor(private editorService: EditorService, private translateService: TranslateService, private modalService: ModalService) { }

  ngOnInit() {
    this.currentDate = moment();
  }

  getTypeName(type:TypeDefinition): string {
    let vt = VariableDefinition.variableType.find( vt => vt.value === type);
    if(vt) {
      return vt.localized;
    }
    return "";
  }

  getFormatedDate(definition: FormatDefinition, valueToFormat) {
    if (definition.formatType === FormatDefinitionType.Date) {
      return this.currentDate.format(definition.format);
    }
    if (definition.formatType === FormatDefinitionType.Number) {
      if (typeof(numeral['locales']['custom']) !== 'undefined') {
        delete numeral['locales']['custom'];
      }

      // @ts-ignore
      numeral.register('locale', 'custom', {
        delimiters: {
          thousands: this.defaultSettings.DefaultThousandsSeparator,
          decimal: this.defaultSettings.DefaultDecimalSeparator
        },
        currency: {
          symbol: this.defaultSettings.DefaultCurrencySymbol
        }
      });

      numeral.locale('custom');
      let mynumeral = numeral(valueToFormat);
      return mynumeral.format(definition.format);
    }
  }

  getFormatExampleFor(definition: FormatDefinition) {
    if( definition.formatType === FormatDefinitionType.Date) {
      return this.currentDate.locale(getDefaultLanguaje()).format('LLL');
    }
    else if( definition.formatType === FormatDefinitionType.Number) {
      return 123456780.54321;
    }
  }

  getUserFormatExampleFor(definition: FormatDefinition) {
    if (definition.inputType !== null) {
      switch (definition.inputType) {
        case TypeDefinition.Number:
        case TypeDefinition.Decimal:
          definition.formatType = FormatDefinitionType.Number;
          break;
        case TypeDefinition.Timestamp:
        case TypeDefinition.StringDate:
          definition.formatType = FormatDefinitionType.Date;
          break;
      }

      return this.getFormatExampleFor(definition);
    }
  }

  getSupportedTypes(definition: FormatDefinition) {
    let localized = definition.inputTypes.map( el => {
      return '<span class="variable-type">' + this.translateService.instant(this.getTypeName(el)) + '</span>';
    });

    return localized.join(", ");
  }

  addUserFormat() {
    this.userFormatList.push(new FormatDefinition());
  }

  getVariablesTypes() {
    const value = VariableDefinition.variableType.filter(v =>
      v.value !== TypeDefinition.Bool &&
      v.value !== TypeDefinition.Text &&
      v.value !== TypeDefinition.Array &&
      v.value !== TypeDefinition.Object &&
      v.value !== TypeDefinition.Any &&
      v.value !== TypeDefinition.ByteArray);
    return value;
  }

  deleteUserFormat(format: FormatDefinition) {
    var emmitAction = new EventEmitter();
    emmitAction.subscribe( ()=> {
      this.editorService.deleteUserFormat(format.key);
    });

    var deleteInfo : DeleteInput = new DeleteInput();
    deleteInfo.ElementName = format.name;

    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_DELETE_USERFORMAT_QUESTION',
        HideAffected: true,
        HideConfirmation: true,
        deleteText: 'ACCEPT',
        DeleteTextExplanation: 'DELETE_USERFORMAT_EXPLANATION',
      }, { DeleteAction : emmitAction}
    );
  }
}
