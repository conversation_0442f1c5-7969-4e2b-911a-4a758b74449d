<div class="new-flow-container">
  <div class="title">{{ 'NEW_FLOW_TITLE' | translate }}</div>
  <div class="contents">
    <app-dashboard-flow *ngFor="let flow of moduleFlows" class="content"
        [model]="flow">
        <!--(onClone)="onClone($event)"
            (onUpload)="onUpload($event, true)"
            (onDownload)="onDownload($event, true)"
            (onPublish)="onPublish($event)"
            (onDelete)="onDelete($event, true)">-->
    </app-dashboard-flow>
    <div class="separator"></div>
    <div class="button-area">
      <button type="button" class="action-button" (click)="onCancel()">{{'CANCEL' | translate}}</button>
    </div>
  </div>
</div>
