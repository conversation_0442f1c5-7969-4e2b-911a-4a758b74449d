import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { MultimediaAnalysisPiece } from 'src/app/models/pieces/MultimediaAnalysisPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { ExtractionFormat } from 'src/app/models/cognitivity/ExtractionFormat';
import { TypeDefinition } from 'src/app/models/TypeDefinition';

@Component({
  selector: 'app-multimedia-analysis-piece',
  templateUrl: './multimedia-analysis-piece.component.html',
  styleUrls: ['./multimedia-analysis-piece.component.scss']
})
export class MultimediaAnalysisPieceComponent extends BasePieceVM implements OnInit {
  model: MultimediaAnalysisPiece;
  outputVariable: VariableDefinition;
  extractionFormats: ExtractionFormat[] = [];
  enableClientPrompts: boolean = false;
  outputVariableFilter = [TypeDefinition.Object];


  constructor(public editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as MultimediaAnalysisPiece;
    if (!this.model) {
      this.model = new MultimediaAnalysisPiece();
    }
    this.outputVariable = this.editorService.findVariableWithId(this.model.outputVariableId);
    this.loadExtractionFormats();
  }

  loadExtractionFormats() {
    this.extractionFormats = this.editorService.getExtractionFormats();
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.errorBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.errorBlockId = "-1";
  }

  onSelectIntegrityErrorBlock(blockData: BlockDefinition) {
    this.model.integrityErrorBlockId = blockData.Id;
  }

  onDeleteIntegrityErrorBlock() {
    this.model.integrityErrorBlockId = "-1";
  }

  setOutputVariable(variable: VariableDefinition) {
    this.outputVariable = variable;
    this.model.outputVariableId = variable ? variable.Id : null;
  }

  isValid(): boolean {
    return this.model.isValid(this.editorService);
  }

  isImageUrlValid(): boolean {
    return this.model.isImageUrlValid();
  }

  isExtractionFormatValid(): boolean {
    return this.model.isExtractionFormatValid();
  }

  isOutputVariableValid(): boolean {
    return this.model.isOutputVariableValid(this.editorService);
  }

  isErrorBlockValid(): boolean {
    return this.model.isErrorBlockValid(this.editorService);
  }

  isIntegrityErrorBlockValid(): boolean {
    return this.model.isIntegrityErrorBlockValid(this.editorService);
  }
}
