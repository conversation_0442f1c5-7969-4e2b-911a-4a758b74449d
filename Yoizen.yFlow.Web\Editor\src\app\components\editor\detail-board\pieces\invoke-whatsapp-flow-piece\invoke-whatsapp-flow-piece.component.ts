import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { InvokeWhatsappFlowParameter, InvokeWhatsappFlowPiece } from 'src/app/models/pieces/InvokeWhatsappFlowPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { WhatsappFlowScreen, WhatsappFlowsByService } from 'src/app/models/WhatsappFlows';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { hasEmojis, isStringValid } from 'src/app/urlutils.module';

@Component({
  selector: 'app-invoke-whatsapp-flow-piece',
  templateUrl: './invoke-whatsapp-flow-piece.component.html',
  styleUrls: ['./invoke-whatsapp-flow-piece.component.scss']
})
export class InvokeWhatsappFlowPieceComponent extends BasePieceVM implements OnInit {
  model: InvokeWhatsappFlowPiece;
  flows: WhatsappFlowsByService[] = [];
  allFlows: WhatsappFlowsByService[] = [];
  selectedFlow: WhatsappFlowsByService = null;
  selectedScreen: WhatsappFlowScreen = null;
  socialServices: string [] = [];
  selectedService: string = null;
  
  SuccessBlockData: BlockDefinition = null;
  searchSuccessBlockString : String;
  ExpirationBlockData: BlockDefinition = null;
  searchExpirationBlockString : String;
  CaseExceptionBlockData: BlockDefinition = null;
  searchCaseExceptionBlockString : String;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as InvokeWhatsappFlowPiece;
    
    this.allFlows = this.editorService.getYSocialSettings().WhatsappFlowsByService;

    this.selectedService = this.model.SelectedService ||'';
    
    this.setSocialServices();

    this.reloadComponents();

    this.SuccessBlockData = this.editorService.findBlockWithId(this.model.SuccessBlockId);
    this.ExpirationBlockData = this.editorService.findBlockWithId(this.model.ExpirationBlockId);
    this.CaseExceptionBlockData = this.editorService.findBlockWithId(this.model.CaseExceptionBlockId);
  }

  reloadComponents(){
    if (typeof(this.model.SelectedFlow) == 'string') {
      this.selectedFlow = this.flows.find(f => f.FlowId === this.model.SelectedFlow);
    }
    
    if (this.model.SelectedScreen) {
      if (typeof(this.selectedFlow) == 'object' && this.selectedFlow != null) {
        this.selectedScreen = this.selectedFlow.FlowScreens.find(s => s.ID === this.model.SelectedScreen);
        if (!this.selectedScreen){
          this.model.SelectedScreen = null;
        }
      }
    }
    
  }

  onSuccessBlockSelect(blockData) {
    this.model.SuccessBlockId = blockData.Id;
    this.searchSuccessBlockString = blockData.Name;
    this.SuccessBlockData = this.editorService.findBlockWithId(this.model.SuccessBlockId);

  }

  onDeleteSuccessBlock() {
    this.SuccessBlockData = null;
    this.searchSuccessBlockString = "-1";
    this.model.SuccessBlockId = "-1";
  }

  onCaseBlockSelect(blockData) {
    this.model.CaseExceptionBlockId = blockData.Id;
    this.searchCaseExceptionBlockString = blockData.Name;
    this.SuccessBlockData = this.editorService.findBlockWithId(this.model.CaseExceptionBlockId);
  }

  onDeleteCaseBlock() {
    this.CaseExceptionBlockData = null;
    this.searchCaseExceptionBlockString = "-1";
    this.model.CaseExceptionBlockId = "-1";
  }

  onExpirationBlockSelect(blockData) {
    this.model.ExpirationBlockId = blockData.Id;
    this.ExpirationBlockData = blockData.Name;
    this.SuccessBlockData = this.editorService.findBlockWithId(this.model.ExpirationBlockId);
  }

  onDeleteExpirationBlock() {
    this.ExpirationBlockData = null;
    this.searchExpirationBlockString = "-1";
    this.model.ExpirationBlockId = "-1";
  }

  onSelectFlow(event) {
    this.model.clear();
    this.model.SelectedFlow = this.selectedFlow.FlowId;
    this.selectedScreen = this.selectedFlow.FlowScreens[0];
    this.onSelectScreen(null);
  }

  onSelectScreen(event) {
    if(this.selectedScreen != null) {
      this.model.SelectedScreen = this.selectedScreen.ID;
      this.model.Data = this.selectedScreen.Data.map(d => {
        let data = new InvokeWhatsappFlowParameter()
        data.Name = d.Name;
        data.Type = d.Type;
        data.VariableId = -1;
        return data;
      });
    } else {
      this.model.SelectedScreen = null;
    }
  }

  setSocialServices() {
    if (this.allFlows && this.allFlows.length > 0) {
      const aux = this.allFlows.map(f => f.Name);
      const socialServices = new Set(aux);
      this.socialServices = Array.from(socialServices);
      this.onSelectService();
    }
  }

  onSelectService(){
    this.model.SelectedService = this.selectedService;
    this.flows = this.allFlows.filter(f => f.Name === this.selectedService);
    this.selectedFlow = null;
    this.selectedScreen = null;
    this.reloadComponents();
  }

  setVariableOnOutput(variable, parameter: InvokeWhatsappFlowParameter) {
    if(!parameter || !variable) {
      this.model.Data.find(d => d.Name === parameter.Name).VariableId = -1
    } else {
      this.model.Data.find(d => d.Name === parameter.Name).VariableId = variable.Id
    }
  }

  getAssignVariableIdValid() {
    return true;
  }

  getVariableData(parameter: InvokeWhatsappFlowParameter) {
    if(!parameter) {
      return null;
    }

    let data = this.model.Data.find(d => d.Name === parameter.Name);
    
    if(!data || data.VariableId === -1) {
      return null;
    }
    return this.editorService.getVariableWithId(data.VariableId);
  }

  getVariableType(parameter: InvokeWhatsappFlowParameter) {
    if(!parameter) {
      return [];
    }

    let type = parameter.Type.toLowerCase();

    if(type === 'string') {
      return [ TypeDefinition.Text ];
    } else if(type === 'array') {
      return [ TypeDefinition.Array ];
    } else if(type === 'object') {
      return [ TypeDefinition.Object ];
    } else if(type === 'number') {
      return [ TypeDefinition.Number ];
    } else {
      return [ ];
    }
  }
  
  isStringValid(str) {
    return isStringValid(str);
  }

  validateButtonText(str) {
    if(!isStringValid(str)) {
      return false;
    }
    
    if(hasEmojis(str)) {
      return false;
    }
    
    if(str.length > 20) {
      return false;
    }

    return true;
  }

  isValidBodyText(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if(hasEmojis(str)) {
      return false;
    }

    if(str.length > 1024) {
      return false;
    }

    return true;
  }

  isValidFooterText(str) {
    if(hasEmojis(str)) {
      return false;
    }

    return true;
  }

  isValidHeaderText(str) {
    if(hasEmojis(str)) {
      return false;
    }

    return true;
  }
}
