<div class="invoke-flow card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sitemap"></span> {{ 'PIECE_INVOKE_WHATSAPP_FLOW_TITLE' | translate }}
  </div>

  <div class="max-length" role="alert" *ngIf="socialServices.length == 0">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_FLOWS_NOT_EXIST' | translate }}</span>
    </div>
  </div>

  <div class="flow-column" *ngIf="socialServices.length > 0">
    <div class="title">
      {{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_SERVICE' | translate }}
    </div>
    <select class="select"
            [disabled]="readOnly"
            [(ngModel)]="selectedService"
            [ngClass]="{'invalid-input': selectedFlow === '' }"
            (ngModelChange)="onSelectService($event)">
      <option *ngFor="let service of socialServices" [ngValue]="service" >{{ service }}</option>
    </select>
  </div>

  <div class="flow-column" *ngIf="flows.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_FLOW' | translate }}:</span>
    <select class="select" name="" id=""
            [disabled]="readOnly"
            [(ngModel)]="selectedFlow"
            [ngClass]="{'invalid-input': selectedFlow === '' }"
            (ngModelChange)="onSelectFlow($event)">
      <option *ngFor="let flow of flows" [ngValue]="flow" >{{ flow.FlowName }} ({{ flow.FlowId }})</option>
    </select>
  </div>

  <div class="flow-column" *ngIf="selectedFlow && selectedFlow.FlowScreens.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_SCREEN' | translate }}:</span>
    <select class="select" name="" id=""
            [disabled]="true"
            [(ngModel)]="selectedScreen"
            [ngClass]="{'invalid-input': !selectedScreen}"
            (ngModelChange)="onSelectScreen($event)">
      <option *ngFor="let screen of selectedFlow.FlowScreens" [ngValue]="screen" >{{ screen.Title }} ({{ screen.ID }})</option>
    </select>
  </div>

  <div class="flow-column" *ngIf="selectedFlow && selectedFlow.FlowScreens.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_HEADER_TEXT' | translate }}:</span>
    <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
    [(value)]="model.HeaderText"
    [disabled]="readOnly"
    class="input"
    [wideInput]="true"
    [validator]="isValidHeaderText">
    </app-input-with-variables>
    <!-- <input class="input" type="text" placeholder="{{ 'PIECE_INVOKE_WHATSAPP_FLOW_HEADER_TEXT' | translate }}" [(ngModel)]="model.HeaderText"> -->
  </div>
  
  <div class="flow-column" *ngIf="selectedFlow && selectedFlow.FlowScreens.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_BODY_TEXT' | translate }}:</span>
    <div class="max-length" role="alert" >
      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_MAX_LENGTH' | translate }}</span>
      </div>
    </div>
    <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
      [(value)]="model.BodyText"
      [disabled]="readOnly"
      [isTextArea]="true"
      class="input"
      [wideInput]="true"
      [validator]="isValidBodyText">
    </app-input-with-variables>
    <!-- <input class="input" type="text" placeholder="{{ 'PIECE_INVOKE_WHATSAPP_FLOW_BODY_TEXT' | translate }}" [(ngModel)]="model.BodyText"> -->
  </div>

  <div class="flow-column" *ngIf="selectedFlow && selectedFlow.FlowScreens.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_FOOTER_TEXT' | translate }}:</span>
    <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
      [(value)]="model.FooterText"
      [disabled]="readOnly"
      class="input"
      [wideInput]="true"
      [validator]="isValidFooterText">
    </app-input-with-variables>
    <!-- <input class="input" type="text" placeholder="{{ 'PIECE_INVOKE_WHATSAPP_FLOW_FOOTER_TEXT' | translate }}" [(ngModel)]="model.FooterText"> -->
  </div>

  <div class="flow-column" *ngIf="selectedFlow && selectedFlow.FlowScreens.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_BUTTON_TEXT' | translate }}:</span>
    <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
      [(value)]="model.ButtonText"
      [disabled]="readOnly"
      class="input"
      [wideInput]="true"
      [validator]="validateButtonText">
    </app-input-with-variables>
    <!-- <input class="input" type="text" placeholder="{{ 'PIECE_INVOKE_WHATSAPP_FLOW_BUTTON_TEXT' | translate }}" [(ngModel)]="model.ButtonText"> -->
  </div>

  <div class="flow-data" *ngIf="selectedScreen && selectedScreen.Data.length > 0">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_SCREEN_DATA' | translate }}:</span>
    <div class="flow-row" *ngFor="let data of selectedScreen.Data let i = index">
      <span class="title">{{ data.Name }}:</span>
      <app-variable-selector-input [VariableData]="getVariableData(data)"
                                   (setVariable)="setVariableOnOutput($event, data)"
                                   [validator]="getAssignVariableIdValid()"
                                   [typeFilters]="getVariableType(data)"
                                   [readOnly]="readOnly"
                                   class="input"></app-variable-selector-input>
    </div>
  </div>

  <div class="flow-row" *ngIf="selectedFlow">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_SUCCESS_BLOCK_ID' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.SuccessBlockId"
                    (onSelectNewBlock)="onSuccessBlockSelect($event)"
                    (onDeleteBlock)="onDeleteSuccessBlock()"
                    [readOnly]="readOnly"
                    [isInvalid]="!model.isSuccessBlockValid(editorService)"></app-block-picker>
  </div>

  <div class="flow-row" *ngIf="selectedFlow">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_EXPIRATION_BLOCK_ID' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.ExpirationBlockId"
                    (onSelectNewBlock)="onExpirationBlockSelect($event)"
                    (onDeleteBlock)="onDeleteExpirationBlock()"
                    [readOnly]="readOnly"
                    [isInvalid]="!model.isExpirationBlockValid(editorService)"></app-block-picker>
  </div>

  <div class="flow-row" *ngIf="selectedFlow">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_SELECT_CASE_EXCEPTION_BLOCK_ID' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.CaseExceptionBlockId"
                    (onSelectNewBlock)="onCaseBlockSelect($event)"
                    (onDeleteBlock)="onDeleteCaseBlock()"
                    [readOnly]="readOnly"
                    [isInvalid]="!model.isCaseExceptionBlockValid(editorService)"></app-block-picker>
  </div>

  <div class="flow-row" *ngIf="selectedFlow">
    <span class="title">{{ 'PIECE_INVOKE_WHATSAPP_FLOW_EXPIRATION_TIME' | translate }} {{model.TokenExpiration}} hs:</span>
    <input class="slider" type="range" min="1" max="24" [(ngModel)]="model.TokenExpiration" [disabled]="readOnly" />
  </div>
</div>