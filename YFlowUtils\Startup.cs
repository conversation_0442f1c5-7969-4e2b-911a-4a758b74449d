using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Serialization;
using YFlowUtils.Helpers;

namespace YFlowUtils
{
    public class Startup
    {
		public static string ContentRoot;
		public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
			ContentRoot = env.ContentRootPath;
		}

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
			services.AddControllers(options => options.ModelValidatorProviders.Clear()).AddJsonOptions(x =>
			{
				x.JsonSerializerOptions.IgnoreNullValues = true;
				x.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
			}).AddNewtonsoftJson(options =>
			{
				options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
				options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
			});

			// In production, the Angular files will be served from this directory
			services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = "ClientApp/dist";
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
			loggerFactory.AddLog4Net();
			if (env.IsDevelopment())
			{
				app.UseDeveloperExceptionPage();
				//IdentityModelEventSource.ShowPII = true;
			}
			else
			{
				app.UseExceptionHandler("/Error");
				// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
				app.UseHsts();
			}

			app.UseHttpsRedirection();
			app.UseStaticFiles();
			if (!env.IsDevelopment())
			{
				app.UseSpaStaticFiles();
			}
			
			app.UseCors(options => options.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());
			app.UseRouting();
			app.UseAuthentication();
			app.UseAuthorization();

			app.Use(async (context, next) =>
			{
				log4net.ILog _log4net = log4net.LogManager.GetLogger(typeof(Startup));
				var request = context.Request;
				var url = request.GetDisplayUrl();
				if (request.Method.Equals("GET") || request.Method.Equals("DELETE"))
				{
					_log4net.Info("=============================================================");
					_log4net.Info(string.Format("{0} {1}{2}", request.Method, url, request.ConvertHeadersToString()));
				}
				else if (request.Method.Equals("POST"))
				{
					_log4net.Info("=============================================================");
					_log4net.Info(string.Format("{0} {1}{2}", request.Method, url, request.ConvertHeadersToString()));
				}

				await next.Invoke();
			});

			app.UseEndpoints(endpoints =>
			{
				endpoints.MapControllerRoute(
					name: "default",
					pattern: "{controller}/{action=Index}/{id?}");
			});

			app.UseSpa(spa =>
			{
				// To learn more about options for serving an Angular SPA from ASP.NET Core,
				// see https://go.microsoft.com/fwlink/?linkid=864501

				spa.Options.SourcePath = "ClientApp";

				if (env.IsDevelopment())
				{
					spa.UseProxyToSpaDevelopmentServer("http://localhost:4400");
					//spa.UseAngularCliServer(npmScript: "start");
				}
			});
		}
    }
}
