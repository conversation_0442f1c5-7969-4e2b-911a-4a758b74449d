import { DbConfigBase } from "./ConfigDbBase";
import { isEnvironmentVariableValid, parseToInt, parseToBoolean } from "./Helpers";
import { logger } from "./Logger";

export class DbYFlow extends DbConfigBase {
}

export const dbYFlow = new DbYFlow()
dbYFlow.host = process.env.dbhost;
dbYFlow.port = parseToInt(process.env.dbport);
dbYFlow.name = process.env.dbname;
dbYFlow.username = process.env.dbusername;
dbYFlow.password = process.env.dbpassword;
dbYFlow.dialect = process.env.dbdialect;
dbYFlow.dbtimeout = parseToInt(process.env.dbtimeout, 30000);
dbYFlow.dbcanceltimeout = parseToInt(process.env.dbcanceltimeout, 5000);
dbYFlow.ssl = parseToBoolean(process.env.dbssl, false);
dbYFlow.enable = true;
dbYFlow.connectionString = process.env.yFlowConnectionString;
dbYFlow.poolMaxSize = parseToInt(process.env.dbMaxPoolSize, 5);

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(dbYFlow.dialect)) {
        logger.error(`Faltan datos de conexión al Sql de yFlow`);
        process.exit(9);
    }

    dbYFlow.dialect = dbYFlow.dialect.toLowerCase();
    dbYFlow.parseConnectionString();


    if (!(isEnvironmentVariableValid(dbYFlow.host) &&
        isEnvironmentVariableValid(dbYFlow.port) &&
        isEnvironmentVariableValid(dbYFlow.name) &&
        isEnvironmentVariableValid(dbYFlow.username) &&
        isEnvironmentVariableValid(dbYFlow.password) &&
        isEnvironmentVariableValid(dbYFlow.dialect))) {
        logger.error(`Faltan datos de conexión al Sql de yFlow`);
        process.exit(9);
    }
}

validateAndFormatConfig();