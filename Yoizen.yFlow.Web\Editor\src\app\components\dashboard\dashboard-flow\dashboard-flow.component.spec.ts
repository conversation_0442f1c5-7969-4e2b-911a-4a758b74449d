import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardFlowComponent } from './dashboard-flow.component';

describe('DashboardFlowComponent', () => {
  let component: DashboardFlowComponent;
  let fixture: ComponentFixture<DashboardFlowComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DashboardFlowComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardFlowComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
