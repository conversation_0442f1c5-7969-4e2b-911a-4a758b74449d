<div class="shorten-url card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>

  <div class="card-title">
    <span class="fas fa-file-image"></span> {{ 'PIECE_ENCODE_BASE64_IMAGE' | translate }}
  </div>

  <div class="data">
    <span class="title">{{'SAVE_INTO' | translate}}:</span>
    <app-variable-selector-input [VariableData]="curretVariable"
                                 (setVariable)="setVariableOnOutput($event)"
                                 [typeFilters]="variableFilter"
                                 [readOnly]="readOnly"
                                 class="input-variable-area"></app-variable-selector-input>
  </div>

</div>
