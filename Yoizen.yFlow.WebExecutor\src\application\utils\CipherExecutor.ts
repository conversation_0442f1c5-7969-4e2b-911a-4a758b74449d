import * as CryptoJS from 'crypto-js';

// Verificación adicional para asegurar que CryptoJS se cargue correctamente
const ensureCryptoJS = () => {
    let crypto = CryptoJS;

    // Si la importación ES6 no funciona, intentar require
    if (!crypto || !crypto.enc || !crypto.enc.Utf8) {
        try {
            // @ts-ignore
            crypto = require('crypto-js');
        } catch (error) {
            throw new Error('Failed to load CryptoJS library: ' + error.message);
        }
    }

    if (!crypto) {
        throw new Error('CryptoJS library is not loaded.');
    }

    if (!crypto.enc || !crypto.enc.Utf8) {
        throw new Error('CryptoJS library is missing required encoding functions.');
    }

    return crypto;
};

export const EncryptType = {
    AES: 0,
    TripleDES: 1,
    Rabbit: 2,
} as const;

export const EncryptModeType = {
    CBC: 0,
    CFB: 1,
    CTR: 2,
    OFB: 3,
    ECB: 4,
} as const;

export const DelimiterType = {
    pipe: '|'
} as const;

export const KeySizes = {
    128: 16, // 128 bits = 16 bytes
    192: 24, // 192 bits = 24 bytes
    256: 32, // 256 bits = 32 bytes
} as const;

export interface charactersToReplace {
    value: string,
    replaceWith: string,
};

export const EncryptPaddingType = {
    Pkcs7: 0,
    Iso97971: 1,
    AnsiX923: 2,
    Iso10126: 3,
    ZeroPadding: 4,
    NoPadding: 5,
} as const;

export class CipherExecutor {
    private type: number;
    private mode: number;
    private padding: number;
    private delimiter: string;
    private key: any;
    private customMethod: boolean;
    private ivBytesLength: number;
    private replaceCharacters: boolean;
    private charactersToReplace: charactersToReplace[] = []; // Characters to replace in Base64

    constructor(type: number, mode: number, padding: number, privateKey: string, customMethod: boolean = false, delimiter: string, ivBytesLength: number, replaceCharacters: boolean, charactersToReplace: charactersToReplace[] = []) {
        this.type = type;
        this.mode = mode;
        this.padding = padding;

        if (!privateKey) {
            throw new Error('Private key cannot be empty.');
        }

        this.customMethod = customMethod;
        this.delimiter = delimiter;
        this.ivBytesLength = ivBytesLength;
        this.replaceCharacters = replaceCharacters;
        this.charactersToReplace = charactersToReplace;

        if (!this.customMethod) {
            this.replaceCharacters = false;
            this.delimiter = null;
            this.ivBytesLength = null;
        }

        this.validateKeyLength(privateKey);
        this.key = this.processKey(privateKey);
    }

    /**
     * Validates the key length for the specified encryption algorithm
     * @param privateKey The private key to validate
     */
    private validateKeyLength(privateKey: string): void {
        if (this.type === EncryptType.AES) {
            const validAESLengths = [16, 24, 32]; // 128, 192, 256 bits
            if (validAESLengths.indexOf(privateKey.length) === -1) {
                throw new Error(`Invalid AES key length: ${privateKey.length} bytes. Valid lengths are: 16 (AES-128), 24 (AES-192), or 32 (AES-256) bytes.`);
            }
        } else if (this.type === EncryptType.TripleDES) {
            const validDESLengths = [16, 24]; // 128, 192 bits
            if (validDESLengths.indexOf(privateKey.length) === -1) {
                throw new Error(`Invalid TripleDES key length: ${privateKey.length} bytes. Valid lengths are: 16 or 24 bytes.`);
            }
        }
        // Rabbit doesn't have strict key length requirements, so we don't validate it
    }

    /**
     * Processes the key according to algorithm requirements
     * @param privateKey The private key string
     * @returns Processed key as WordArray
     */
    private processKey(privateKey: string): any {
        // For all algorithms, parse the key as UTF-8
        const crypto = ensureCryptoJS();
        return crypto.enc.Utf8.parse(privateKey);
    }

    /**
     * Converts URL-safe Base64 back to standard Base64 format
     * @param urlSafeBase64 The URL-safe Base64 string
     * @returns Standard Base64 string
     */
    private replaceCharactersInString(urlSafeBase64: string): string {
        if (!urlSafeBase64) {
            return '';
        }
        if (this.replaceCharacters) {
            // Replace URL-safe characters with standard Base64 characters
            for (const char of this.charactersToReplace) {
                const escapedValue = char.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape special regex characters
                urlSafeBase64 = urlSafeBase64.replace(new RegExp(escapedValue, 'g'), char.replaceWith);
            }
        }
        return urlSafeBase64;
    }

    /**
     * Validates if a string is a valid Base64 format
     * @param str The string to validate
     * @returns True if valid Base64, false otherwise
     */
    private isValidBase64(str: string): boolean {
        if (!str) {
            return false;
        }
        
        try {
            // Try to decode and re-encode to check validity
            const crypto = ensureCryptoJS();
            const decoded = crypto.enc.Base64.parse(str);
            const reEncoded = decoded.toString(crypto.enc.Base64);
            return reEncoded === str;
        } catch (error) {
            return false;
        }
    }

    private requiresIV(): boolean {
        return this.type === EncryptType.AES || this.type === EncryptType.TripleDES;
    }

    /**
     * Generates a random IV for encryption operations
     * @returns Random IV as WordArray
     */
    private generateIV(random: boolean): any {
        if (random) {
            if (!this.ivBytesLength || this.ivBytesLength <= 0) {
                throw new Error('IV bytes length must be specified and greater than 0.');
            }
            if (this.ivBytesLength % 4 !== 0) {
                throw new Error('IV bytes length must be a multiple of 4.');
            }
            const crypto = ensureCryptoJS();
            return crypto.lib.WordArray.random(this.ivBytesLength);
        }

        const crypto = ensureCryptoJS();
        return crypto.enc.Hex.parse('00000000000000000000000000000000');
    }

    /**
     * Encrypts a message using the configured algorithm with a random IV
     * @param message The plain text message to encrypt
     * @returns Encrypted message in URL-safe format: [IV_URL_SAFE_BASE64]|[CIPHERTEXT_URL_SAFE_BASE64] or just [CIPHERTEXT_URL_SAFE_BASE64] for algorithms that don't use IV
     */
    encrypt(message: string): string {
        if (!message && message !== '') {
            throw new Error('Message to encrypt cannot be null or undefined.');
        }

        // Verificar que CryptoJS esté disponible
        const crypto = ensureCryptoJS();

        let encrypted;
        let iv: any = undefined;
    
        if (this.requiresIV()) {
            iv = this.generateIV(this.customMethod);
        }

        const config: any = {
            mode: this.getMode(),
            padding: this.getPadding(),
        };
        
        if (iv) {
            config.iv = iv;
        }

        try {
            switch (this.type) {
                case EncryptType.AES:
                    encrypted = crypto.AES.encrypt(message, this.key, config);
                    break;
                case EncryptType.TripleDES:
                    encrypted = crypto.TripleDES.encrypt(message, this.key, config);
                    break;
                case EncryptType.Rabbit:
                    encrypted = crypto.Rabbit.encrypt(message, this.key);
                    break;
                default:
                    throw new Error('Unsupported encryption type');
            }
        } catch (error) {
            throw new Error('Encryption failed: ' + (error as Error).message);
        }

        if (!encrypted) {
            throw new Error('Encryption resulted in an undefined value.');
        }

        // Get the ciphertext as standard Base64
        const cipherTextBase64 = encrypted.toString();

        if (!this.customMethod)
            return cipherTextBase64;
        
        // Validate that we got valid Base64
        if (!this.isValidBase64(cipherTextBase64)) {
            throw new Error('Encryption produced invalid Base64 output.');
        }
        
        const cipherTextUrlSafe = this.replaceCharactersInString(cipherTextBase64);

        // If IV was used, prepend it to the ciphertext with delimiter (now in URL-safe Base64 format)
        if (iv) {
            const ivBase64 = iv.toString(crypto.enc.Base64);
            
            // Validate IV Base64
            if (!this.isValidBase64(ivBase64)) {
                throw new Error('IV conversion to Base64 failed.');
            }
            
            const ivUrlSafe = this.replaceCharactersInString(ivBase64);

            if (this.delimiter) {
                return ivUrlSafe + this.delimiter + cipherTextUrlSafe;
            }

            return ivUrlSafe  + cipherTextUrlSafe;
        }

        // For algorithms that don't use IV (like Rabbit), return just the ciphertext
        return cipherTextUrlSafe;
    }

    /**
     * Decrypts a message using the configured algorithm with a random IV
     * @param cipherText The encrypted message in URL-safe format
     * @returns Decrypted plain text message
     */
    decrypt(cipherText: string): string {
        if (!cipherText) {
                throw new Error('Cipher text to decrypt cannot be empty.');
        }

        // Verificar que CryptoJS esté disponible
        const crypto = ensureCryptoJS();

        cipherText = cipherText.trim();
        cipherText = this.replaceCharactersInString(cipherText);
        
        let iv: any = undefined;
        if (this.requiresIV()) {
            if (this.customMethod) {
                let ivUrlSafe: string;
                if (this.delimiter) {
                    const parts = cipherText.split(this.delimiter);
                    if (parts.length !== 2) {
                        throw new Error('Invalid ciphertext format. Expected format: [IV_URL_SAFE_BASE64]|[CIPHERTEXT_URL_SAFE_BASE64]');
                    }
                    ivUrlSafe = parts[0].trim();
                    cipherText = parts[1].trim();
                }
                else {
                    // Assuming IV length is 16 bytes = 24 characters in base64 (after URL-safe conversion)
                    // Adjust IV_LENGTH_BASE64 according to your specific IV size
                    const IV_LENGTH_BASE64 = 24; // Common for AES with 16-byte IV
                    
                    if (cipherText.length <= IV_LENGTH_BASE64) {
                        throw new Error('Invalid encrypted data length');
                    }
                    ivUrlSafe = cipherText.substring(0, IV_LENGTH_BASE64);
                    cipherText = cipherText.substring(IV_LENGTH_BASE64);
                }

                // Validate that we have both IV and ciphertext parts
                if (!ivUrlSafe || !cipherText) {
                    throw new Error('IV or ciphertext part is empty after splitting by delimiter.');
                }
                // Convert URL-safe Base64 back to standard Base64 for IV
                const ivBase64 = this.replaceCharactersInString(ivUrlSafe);
                
                // Validate Base64 format of IV
                if (!this.isValidBase64(ivBase64)) {
                    throw new Error('Invalid IV Base64 format after URL-safe conversion.');
                }
                try {
                    // Try to parse the IV to validate Base64 format and length
                    const parsedIV = crypto.enc.Base64.parse(ivBase64);
                    
                    // Validate IV length (should be exactly IV_LENGTH_BYTES after decoding)
                    if (parsedIV.sigBytes !== this.ivBytesLength) {
                        throw new Error(`Invalid IV length. Expected ${this.ivBytesLength} bytes after Base64 decoding, got ${parsedIV.sigBytes} bytes.`);
                    }
                    
                    iv = parsedIV;
                } 
                catch (error) {
                    throw new Error('Failed to parse IV: ' + (error as Error).message);
                }
            } 
            else {
                iv = this.generateIV(false); // Use a fixed IV if not using custom method
            }
        }
        const cipherParams = crypto.lib.CipherParams.create({
            ciphertext: crypto.enc.Base64.parse(cipherText), // Procesa en Base64
        });

        const config = {
            mode: this.getMode(),
            padding: this.getPadding(),
            iv: iv,
        };

        let decrypted;
        switch (this.type) {
            case EncryptType.AES:
                decrypted = crypto.AES.decrypt(cipherParams, this.key, config);
                break;
            case EncryptType.TripleDES:
                decrypted = crypto.TripleDES.decrypt(cipherParams, this.key, config);
                break;
            case EncryptType.Rabbit:
                decrypted = crypto.Rabbit.decrypt(cipherParams, this.key);
                break;
            default:
                throw new Error('Unsupported encryption type');
        }

        return decrypted.toString(crypto.enc.Utf8);
    }

    private getMode(): any {
        const crypto = ensureCryptoJS();
        switch (this.mode) {
            case EncryptModeType.CBC:
                return crypto.mode.CBC;
            case EncryptModeType.CFB:
                return crypto.mode.CFB;
            case EncryptModeType.CTR:
                return crypto.mode.CTR;
            case EncryptModeType.ECB:
                return crypto.mode.ECB;
            case EncryptModeType.OFB:
                return crypto.mode.OFB;
            default:
                throw new Error('Unsupported encryption mode: ' + this.mode);
        }
    }

    private getPadding(): any {
        const crypto = ensureCryptoJS();
        switch (this.padding) {
            case EncryptPaddingType.AnsiX923:
                return crypto.pad.AnsiX923;
            case EncryptPaddingType.Iso10126:
                return crypto.pad.Iso10126;
            case EncryptPaddingType.Iso97971:
                return crypto.pad.Iso97971;
            case EncryptPaddingType.NoPadding:
                return crypto.pad.NoPadding;
            case EncryptPaddingType.Pkcs7:
                return crypto.pad.Pkcs7;
            case EncryptPaddingType.ZeroPadding:
                return crypto.pad.ZeroPadding;
            default:
                throw new Error('Unsupported padding type: ' + this.padding);
        }
    }
}

export default {
    Types: EncryptType,
    Modes: EncryptModeType,
    Paddings: EncryptPaddingType,
    Class: CipherExecutor,
};
