@import "_variables";
@import "_mixins";

.attach {
  width: 600px;

  .definition {
    padding: 10px;
    border-bottom: 2px solid $sidebarBorderColor;

    .name,
    .code,
    .email,
    .phone {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      ::ng-deep .app-variable-selector-input {
        flex-grow: 1;
        justify-self: flex-start;
        align-self: flex-start;
        padding-right: 10px;
        .block {
          width: 100%;
        }
        .block-selected {
          .trash {
            position: absolute;
          }
        }
      }
      ::ng-deep .app-input-with-variables {
        flex-grow: 1;
        width: 100%;
        justify-self: flex-start;
        align-self: flex-start;
        padding-right: 10px;
        textarea {
          width: 100%;
        }
        input {
          width: 100%;
        }
      }

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      .input-variable-area {
        flex-grow: 1;
      }
    }

    .url, .name {
      margin-top: 0;
    }
  }

  .next {
    display: flex;
    flex-direction: row;
    margin-top: 12px;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

  }
}
