@import "_variables";
@import "_mixins";

.condition-group {
  position: relative;
  font-weight: normal;
  border: 1px solid $sidebarBorderColor;
  border-radius: 7px;
  margin-bottom: 3px;
  padding: 10px;
  padding-top: 0;

  @for $i from 0 through 5 {
    &.depth-#{$i} {
      //background-color: lighten(#007bff, percentage($i / 10));
      background-color: transparentize(darken($sidebarBackgroundColor, 20%), 1 - ($i / 5));
    }
  }

  &.invalid {
    border-color: $error-color;
  }

  .header {
    display: flex;
    flex-direction: row;
    width: 100%;
    align-items: center;
    padding: 10px 0;

    .button-area {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      flex-grow: 1;
      width: 100%;

      .add-element-button {
        @include actionButton();
        margin-right: 5px;
      }
    }
  }

  .trash {
    @include trashSmall;
    position: absolute;
    top: -9px;
    right: -9px;
    line-height: 14px;

    &:hover {
      color: #555;
    }
  }

  &:hover {
    .trash {
      @include trashOver;
    }
  }

  .conditionEntry {
    position: relative;
    margin-left: 10px;
    margin-right: 10px;
  }
}
