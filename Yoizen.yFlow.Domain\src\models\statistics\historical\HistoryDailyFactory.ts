import { HistoryDailyByAbandonedCase } from "./HistoryDailyByAbandonedCase";
import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { HistoryDailyByBlocks } from "./HistoryDailyByBlocks";
import { HistoryDailyByBlocksSequence } from "./HistoryDailyByBlocksSequence";
import { HistoryDailyByCommands } from "./HistoryDailyByCommands";
import { HistoryDailyByDefaultAnswer } from "./HistoryDailyByDefaultAnswer";
import { HistoryDailyByDerivationKey } from "./HistoryDailyByDerivationKey";
import { HistoryDailyByFlow } from "./HistoryDailyByFlow";
import { HistoryDailyByGroups } from "./HistoryDailyByGroups";
import { HistoryDailyByGroupsSequence } from "./HistoryDailyByGroupsSequence";
import { HistoryDailyByIntegrations } from "./HistoryDailyByIntegrations";
import { HistoryDailyByStatisticEvent } from "./HistoryDailyByStatisticEvent";
import { SystemStatusType } from "../SystemStatus";
import { HistoryDaily } from "./HistoryDaily";

export class HistoryDailyFactory {

    static CreateHistoryDaily(type: HistoryDailyInfoTypes): HistoryDailyBase {
        switch (type) {
            case HistoryDailyInfoTypes.Normal:
                return new HistoryDaily();
            case HistoryDailyInfoTypes.AbandonedCases:
                return new HistoryDailyByAbandonedCase();
            case HistoryDailyInfoTypes.Blocks:
                return new HistoryDailyByBlocks();
            case HistoryDailyInfoTypes.BlocksSequence:
                return new HistoryDailyByBlocksSequence();
            case HistoryDailyInfoTypes.Commnads:
                return new HistoryDailyByCommands();
            case HistoryDailyInfoTypes.DefaultAnswers:
                return new HistoryDailyByDefaultAnswer();
            case HistoryDailyInfoTypes.DerivationKey:
                return new HistoryDailyByDerivationKey();
            case HistoryDailyInfoTypes.ByFlow:
                return new HistoryDailyByFlow();
            case HistoryDailyInfoTypes.Groups:
                return new HistoryDailyByGroups();
            case HistoryDailyInfoTypes.GroupSequence:
                return new HistoryDailyByGroupsSequence();
            case HistoryDailyInfoTypes.Integrations:
                return new HistoryDailyByIntegrations();
            case HistoryDailyInfoTypes.StatisticEvent:
                return new HistoryDailyByStatisticEvent();
            default:
                throw new Error('Invalid totalizer type');
        }
    }

    static CreateHistoryDailyBySystemStatus(type: SystemStatusType) {
        switch (type) {
            case SystemStatusType.LastIntervalDaily:
                return new HistoryDaily();
            case SystemStatusType.LastIntervalByAbandonedCase:
                return new HistoryDailyByAbandonedCase();
            case SystemStatusType.LastIntervalDailyByBlocks:
                return new HistoryDailyByBlocks();
            case SystemStatusType.LastIntervalDailyByBlocksSequence:
                return new HistoryDailyByBlocksSequence();
            case SystemStatusType.LastIntervalDailyByCommands:
                return new HistoryDailyByCommands();
            case SystemStatusType.LastIntervalDailyByDefaultAnswers:
                return new HistoryDailyByDefaultAnswer();
            case SystemStatusType.LastIntervalDailyByDerivationKey:
                return new HistoryDailyByDerivationKey();
            case SystemStatusType.LastIntervalDailyByFlow:
                return new HistoryDailyByFlow();
            case SystemStatusType.LastIntervalDailyByGroups:
                return new HistoryDailyByGroups();
            case SystemStatusType.LastIntervalDailyByGroupsSequence:
                return new HistoryDailyByGroupsSequence();
            case SystemStatusType.LastIntervalDailyByIntegrations:
                return new HistoryDailyByIntegrations();
            case SystemStatusType.LastIntervalDailyByStatisticEvent:
                return new HistoryDailyByStatisticEvent();
            default:
                throw new Error('Invalid totalizer type');
        }
    }
}