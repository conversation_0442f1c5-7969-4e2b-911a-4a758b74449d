import { readFile, access } from "fs/promises";
import { constants } from "fs";
import { FlowRedisCacheRepository } from "../../../../Yoizen.yFlow.Infrastructure/src/adapters/RedisCacheRepository";
import { IPersonalizedTablePort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IPersonalizedTablePort";
import { parse as xlsParse } from 'node-xlsx';
import FlowTableType from "../../../../Yoizen.yFlow.Web/models/imports/flowTableType";
import moment from "moment";
import { config } from "../../config";
import { personalizedTables } from "../../../../Yoizen.yFlow.Web/helpers/folders";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class InsertPersonalizedTableService {
  constructor(
    private personalizedTablePort: IPersonalizedTablePort,
    private cache: FlowRedisCacheRepository
  ) {
  }

  private sanitizeString(value: string): string {
    return value
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\x00-\x7F]/g, '')
      .replaceAll("'", "''");
  }

  private async Consolidate(): Promise<any> {

    logger.info(`Voy a consolidar las tablas personalizadas`);
    const flowtables: any = await this.personalizedTablePort.getAllProcessing();
    for (const flowtable of flowtables) {
      logger.info(`Voy a consolidar la tabla ${flowtable.tableName}`);

      const tablename = flowtable.tableName + '.' + flowtable.extension;
      const copyTablename = `${flowtable.tableName}_temp`;

      // Agregar logs para verificar las variables
      logger.info(`personalizedTables: ${personalizedTables}`);
      logger.info(`tablename: ${tablename}`);
      const filePath = `${personalizedTables}/${tablename}`;
      logger.info(`Ruta completa del archivo: ${filePath}`);

      try {
        // Verificar acceso al archivo
        logger.info(`Verificando si el archivo existe en: ${filePath}`);
        await access(filePath, constants.F_OK);
        logger.info(`El archivo existe: ${filePath}`);
      } catch (err) {
        logger.error(`El archivo no existe: ${filePath}`);
        logger.error(`Error de acceso al archivo:`, err);
        // Continuar al siguiente flowtable si el archivo no existe
        continue;
      }

      try {
        await this.personalizedTablePort.query(`DROP TABLE IF EXISTS ${copyTablename};`);
        await this.personalizedTablePort.query(`SELECT * INTO ${copyTablename} FROM ${flowtable.tableName} WHERE 1 > 2`);
        let headers = JSON.parse(flowtable.headers);
        let allData, data;

        if (flowtable.extension === 'csv') {
          try {
            data = await readFile(filePath, { encoding: 'utf8' });
            const dataSplitted = data.split('\n');
            allData = dataSplitted.slice(1);
          } catch (readError) {
            logger.error(`Error al leer el archivo ${filePath}:`, readError);
            // Manejar el error o continuar al siguiente flowtable
            continue;
          }
        } else {
          try {
            const obj = xlsParse(filePath);
            allData = obj[0].data.slice(1);
            allData = allData.map(row => row.join(';'));
          } catch (parseError) {
            logger.error(`Error al parsear el archivo ${filePath}:`, parseError);
            // Manejar el error o continuar al siguiente flowtable
            continue;
          }
        }

        let insertIntoQuery = null;
        let valueQuery = "VALUES";
        let totalRowToInsert = 0;
        let rowFailed = false;
        for (let i = 0; i < allData.length; i++) {
          const row = allData[i];
          if (insertIntoQuery == null) {
            insertIntoQuery = this.getInsertIntoQuery(headers, copyTablename);
          }
          if (row !== '') {
            totalRowToInsert += 1;
            valueQuery += this.getValuesQuery(headers, copyTablename, row);
            if (totalRowToInsert === config.amountRecordImport) {
              try {
                logger.info(`query ${i}/${allData.length}: ${insertIntoQuery} ${valueQuery}`);
                await this.personalizedTablePort.query(`${insertIntoQuery} ${valueQuery}`);
              } catch (error) {
                logger.info(`Error al ejecutar la consulta de inserción en la iteración ${i}:`, error);
                rowFailed = true;
              }
              totalRowToInsert = 0;
              valueQuery = "VALUES";
            } else {
              valueQuery += ',';
            }
          }
        }
        if (valueQuery !== 'VALUES') {
          try {
            valueQuery = valueQuery.substring(0, valueQuery.length - 1);
            logger.info(`query de lo restante: ${insertIntoQuery} ${valueQuery}`);
            await this.personalizedTablePort.query(`${insertIntoQuery} ${valueQuery}`);
          } catch (error) {
            logger.info(`Error al ejecutar la consulta de inserción final:`, error);
            rowFailed = true;
          }
        }

        let columns = headers.filter((h) => h.index).map((x) => x.name);
        let setIndexes = `CREATE INDEX ix_${flowtable.tableName} ON ${flowtable.tableName} (${columns})`;

        let transaction;
        try {
          transaction = await this.personalizedTablePort.getTransaction();
          await this.personalizedTablePort.query(`DROP TABLE ${flowtable.tableName}`, { transaction });
          await this.personalizedTablePort.query(`sp_rename '${copyTablename}','${flowtable.tableName}';`, { transaction });
          if (columns !== null && typeof (columns) !== 'undefined' && columns.length > 0) {
            await this.personalizedTablePort.query(setIndexes, { transaction });
          }

          await transaction.commit();
        } catch (error) {
          if (transaction) {
            await transaction.rollback();
          }
          logger.info(`Error en la transacción de base de datos:`, error);
          await this.personalizedTablePort.query(`DROP TABLE ${copyTablename}`);
          rowFailed = true;
        }

        flowtable.status = rowFailed ? FlowTableType.Error : FlowTableType.Finished;
        flowtable.updatedAt = moment();
        await flowtable.save();

        logger.info(`${tablename} importado`);
      } catch (error) {
        logger.error(`Ocurrió un error al intentar consolidar los reportes para la tabla ${flowtable.tableName}`);
        logger.error(`Error: ${error}`);
      }
    }

    return null;
  }

  private getInsertIntoQuery(headers, tablename) {
    const headersText = headers.map(h => h.name).join(',');
    return `INSERT INTO ${tablename} (${headersText}) `;
  }

  private getValuesQuery(headers, tablename, values) {
    const dataValues = values.split(';').map(value => {
      return this.sanitizeString((`${value}`).replace(/(\r\n|\n|\r)/gm, ""));
    });
    for (let i = 0; i < headers.length; i++) {
      switch (headers[i].type.toLowerCase()) {
        case 'int':
        case 'integer':
          dataValues[i] = parseInt(dataValues[i]) || 0;
          break;
        case 'number':
          dataValues[i] = parseInt(dataValues[i]) || 0;
          break;
        case 'bit':
          switch (dataValues[i].toLocaleString().toLowerCase()) {
            case 'true':
            case '1':
              dataValues[i] = 1;
              break;
            case 'false':
            case '0':
              dataValues[i] = 0;
              break;
            default:
              dataValues[i] = 0;
          }
          break;
        case 'float':
          dataValues[i] = parseFloat(dataValues[i]) || 0;
          break;
        case 'decimal':
          dataValues[i] = parseFloat(dataValues[i]) || 0;
          break;
        case 'datetime':
          dataValues[i] = `'${moment(dataValues[i]).format('YYYY-MM-DD HH:mm:ss')}'` || null;
          break;
        default:
          dataValues[i] = `'${dataValues[i].replaceAll("'", "''")}'`;
      }
    }
    const valuesText = dataValues.join(',');
    return ` (${valuesText})`;
  }

  async Process() {
    await this.Consolidate();

    setTimeout(this.Process.bind(this), 60000 * 5);
  }
}
