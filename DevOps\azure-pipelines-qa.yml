
resources:
  repositories:
    - repository: pipelines-templates
      type: git
      name: infra-cd-aplicaciones/pipeline-templates
      ref: refs/heads/main
    
variables:
  - group: global-variables

stages:
  - stage: acrbuild
    pool:
      name: devops-agent
    displayName: 'Build and push al acr'
    jobs:
      - job: buildandpush
        steps:
          - checkout: self
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: '.'
              dockerfile: 'WebExecutor.Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacrdev'
              appName: 'yflow-executor'
              appVersion: dev-latest
              buildArgs: "--build-arg versionName=dev-latest"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: '.'
              dockerfile: 'IntervalServices.Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacrdev'
              appName: 'yflow-intervals'
              appVersion: dev-latest
              buildArgs: "--build-arg versionName=dev-latest"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: 'Yoizen.yFlow.Worker/'
              dockerfile: 'Yoizen.yFlow.Worker/Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacrdev'
              appName: 'yflow-worker'
              appVersion: dev-latest
              buildArgs: "--build-arg versionName=dev-latest"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: 'Yoizen.yFlow.Web/'
              dockerfile: 'Yoizen.yFlow.Web/Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacrdev'
              appName: 'yflow-web'
              appVersion: dev-latest
              buildArgs: "--build-arg versionName=dev-latest"
          # - template: helm-acr-build-and-push.yml@pipelines-templates
          #   parameters:
          #     innerPath: './DevOps/Helm'
          #     serviceConnectionName: 'azure'
          #     acrName: 'yoizenacrdev'
          #     appName: 'yflow'
          #     appVersion: dev-latest
          - template: restart-deployment.yml@pipelines-templates
            parameters:
              deployments: "yflow-qa-executor yflow-qa-intervals yflow-qa-worker yflow-qa-web"
              namespace: 'yflow-qa'
              serviceConnectionName: 'azure'
              aksName: 'aks-yoizen'
              aksRG: 'k8s-rg'
      
      - job: Notify_GoogleChat
        displayName: 'Notificar Resultado del Pipeline a Google Chat'
        dependsOn:
          - buildandpush
        condition: always()
        steps:
        - script: |
            curl -X POST -H 'Content-Type: application/json' \
            -d '{"text":"✅ *Pipeline yFlow-Docker-CI completado exitosamente*. `Versión: dev-latest` 🔗 Ver build: https://yoizen.visualstudio.com/yFlow/_build/results?buildId=$(Build.BuildId)"}' \
            "$(googleChatWebhook)" 
          displayName: 'Notificar Éxito a Google Chat'
          condition: succeeded()

        - script: |
            curl -X POST -H 'Content-Type: application/json' \
            -d '{"text":"❌ *Pipeline yFlow-Docker-CI falló*. Revisar errores. `Versión: dev-latest` 🔗 Ver build: https://yoizen.visualstudio.com/yFlow/_build/results?buildId=$(Build.BuildId)"}' \
            "$(googleChatWebhook)" 
          displayName: 'Notificar Falla a Google Chat'
          condition: failed()
trigger:
  branches:
    include:
      - refs/heads/dev