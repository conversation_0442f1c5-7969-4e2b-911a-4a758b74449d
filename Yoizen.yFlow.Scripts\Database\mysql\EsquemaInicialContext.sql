CREATE TABLE case_contexts (
    case_id BIGINT NOT NULL,
    json <PERSON>SON NOT NULL,
    created_at DATETIME(6) DEFAULT NULL,
    updated_at DATETIME(6) DEFAULT NULL,
    PRIMARY KEY (case_id)
);

CREATE TABLE case_contexts_string (
    case_id VARCHAR(50) NOT NULL,
    json JSON NOT NULL,
    created_at DATETIME(6) DEFAULT NULL,
    updated_at DATETIME(6) DEFAULT NULL,
    PRIMARY KEY (case_id)
);

CREATE EVENT `Depuration case_contexts`
    ON SCHEDULE
        EVERY 1 DAY STARTS '2024-09-12 00:05:00'
    ON COMPLETION NOT PRESERVE
    ENABLE
    COMMENT 'Depura la tabla case_contexts con los registros de mas de 2 dias'
    DO
    DELETE FROM case_contexts WHERE updated_at < (DATE_SUB(DATE(NOW()), INTERVAL 2 DAY));

CREATE EVENT `Depuration case_contexts_string`
    ON SCHEDULE
        EVERY 1 DAY STARTS '2024-09-12 00:05:00'
    ON COMPLETION NOT PRESERVE
    ENABLE
    COMMENT 'Depura la tabla case_contexts_string con los registros de mas de 2 dias'
    DO
    DELETE FROM case_contexts_string WHERE updated_at < (DATE_SUB(DATE(NOW()), INTERVAL 2 DAY));
