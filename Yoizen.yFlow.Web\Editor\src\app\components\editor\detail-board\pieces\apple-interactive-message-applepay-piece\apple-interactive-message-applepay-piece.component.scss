@import "_variables";
@import "_mixins";

.apple-pay {
	width: 500px;

  .endpoints, .applepay, .shippingmethods, .total, .lineitems, .merchant, .navigation {
    margin-top: 5px;
    border: 1px solid $cardSeparatorBorderColor;
    border-radius: 7px;
    padding: 5px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-size: 1.4em;
      font-weight: bold;
    }

    & > .info {
      font-style: italic;
      color: #aaaaaa;
    }
  }

  .options {
    margin-top: 5px;
  }

  .option {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables, .input {
      flex-grow: 1;
      flex-shrink: 1;
    }

    &.with-info {
      .info {
        margin-left: 3px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }

    &.with-action {
      .action {
        cursor: pointer;
        margin-left: 10px;
        flex-grow: 0;
        flex-shrink: 0;
        width: 26px;
        height: 26px;
        background: #ffffff none;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
        border: solid 1px #aaa;
        border-radius: 13px;
        font-size: 15px;
        text-align: center;
        z-index: 1400;
        line-height: 24px;

        & > span {
          line-height: 24px;
        }

        &:hover {
          border-color: #666;
        }
      }
    }

    .items {
      flex-grow: 1;
      flex-shrink: 1;
      border: 1px solid $cardSeparatorBorderColor;
      border-radius: 7px;
      padding: 5px;

      &.maxheight {
        max-height: 200px;
        overflow-y: auto;

        @include scrollbar;
      }

      &.invalid {
        border-color: red;
      }

      .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;

        ui-switch {
          height: 24px;
        }

        & > .item-text {
          line-height: 24px;
          margin-left: 3px;
        }
      }
    }
  }

  .shippingmethods, .lineitems {
    .items {
      margin: 5px 0;

      & > .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        font-size: 110%;
        margin-bottom: 3px;
      }

      .item {
        border: solid 1px #ebebeb;
        padding: 10px;
        display: flex;
        flex-direction: row;
        position: relative;

        .trash {
          @include trash;
          position: absolute;
          right: -13px;
          top: -13px;
          cursor: pointer;

          &:hover {
            color: #555;
          }
        }

        &:hover {
          .trash {
            @include trashOver;
          }
        }

        .icon {
          width: 30px;
          flex-grow: 0;
          flex-shrink: 0;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
        }

        .data {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;
        }
      }

      .items-add {
        @include addPieceButton;
        border: solid 1px #ebebeb;
        border-top-style: none;
      }

      &.empty {
        .items-add {
          border-top-style: solid;
        }
      }
    }
  }
}
