<div class="storemessage card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-bug"></span> {{ 'CARD_LOG_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'LOG_INFO' | translate }}
  </div>
  <div class="contents">
    <app-input-with-variables
      [placeholder]="'MESSAGE' | translate"
      [(value)]="model.Text"
      [isTextArea]="true"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
</div>
