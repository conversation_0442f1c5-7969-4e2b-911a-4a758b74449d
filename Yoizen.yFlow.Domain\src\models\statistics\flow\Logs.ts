import { Moment } from "moment";
import { DataTypes, Model } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export enum LogType {
    IntegrationError = 0,
    LogPieze = 1,
    ShortenPieze = 2,
    GeocoderGooglePieze = 3
}
export class Logs extends Model {
    declare type: LogType;
    declare date: Moment;
    declare message: string;
    declare stack: string;
    declare messageId: string;

    static Init(date: Moment, type: LogType, messageId: string): Logs {
        let log = new Logs();
        log.date = date;
        log.type = type;
        log.messageId = messageId;

        return log;
    }
}

Logs.init({
    type: DataTypes.INTEGER,
    messageId: {
        type: new DataTypes.STRING,
        field: 'message_id'
    },
    date: DataTypes.DATE,
    message: DataTypes.STRING,
    stack: DataTypes.STRING,
}, {
    sequelize: sequelize,
    modelName: 'logs',
    tableName: 'logs',
    timestamps: false
});