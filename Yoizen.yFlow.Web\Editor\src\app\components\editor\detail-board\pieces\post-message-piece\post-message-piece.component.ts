import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {StoreMessagePiece} from "../../../../../models/pieces/StoreMessagePiece";
import {LogPiece} from "../../../../../models/pieces/LogPiece";
import {PostMessagePiece} from "../../../../../models/pieces/PostMessagePiece";

@Component({
  selector: 'app-post-message-piece',
  templateUrl: './post-message-piece.component.html',
  styleUrls: ['./post-message-piece.component.scss']
})
export class PostMessagePieceComponent extends BasePieceVM implements OnInit {
  model : PostMessagePiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as PostMessagePiece;
  }
}
