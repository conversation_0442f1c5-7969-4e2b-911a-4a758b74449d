import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { JumpToBlockPiece } from 'src/app/models/pieces/JumpToBlockPiece';
import { PaymentGatewayPiece } from 'src/app/models/pieces/PaymentGatewayPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-payment-gateway-piece',
  templateUrl: './payment-gateway-piece.component.html',
  styleUrls: ['./payment-gateway-piece.component.scss']
})
export class PaymentGatewayPieceComponent extends BasePieceVM implements OnInit {
  SuccessBlockData: BlockDefinition;
  ErrorBlockData: BlockDefinition;
  model: PaymentGatewayPiece;

  @ViewChild('successBlockPicker', { static: false }) SuccessBlockPicker : ElementRef;
  @ViewChild('errorBlockPicker', { static: false }) ErrorBlockPicker : ElementRef;

  constructor(editorService: EditorService, public modalService: ModalService, private renderer: Renderer2) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as PaymentGatewayPiece;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.SuccessBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.SuccessBlockId = null;
  }

  onSelectErrorBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

}
