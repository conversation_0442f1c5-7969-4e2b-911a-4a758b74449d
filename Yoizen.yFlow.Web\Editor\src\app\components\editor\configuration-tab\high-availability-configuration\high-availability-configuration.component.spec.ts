import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { UiSwitchModule } from 'ngx-ui-switch';
import { HighAvailabilityConfigurationComponent } from './high-availability-configuration.component';
import { EditorService } from 'src/app/services/editor.service';

class EditorServiceStub {
  private value = false;
  getHighAvailability() { return this.value; }
  setHighAvailability(v: boolean) { this.value = v; }
}

describe('HighAvailabilityConfigurationComponent', () => {
  let component: HighAvailabilityConfigurationComponent;
  let fixture: ComponentFixture<HighAvailabilityConfigurationComponent>;
  let editorService: EditorServiceStub;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FormsModule, UiSwitchModule, TranslateModule.forRoot()],
      declarations: [HighAvailabilityConfigurationComponent],
      providers: [
        { provide: EditorService, useClass: EditorServiceStub }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HighAvailabilityConfigurationComponent);
    component = fixture.componentInstance;
  // Using get for compatibility with Angular testing utilities version in project
  editorService = (TestBed as any).get(EditorService) as EditorServiceStub;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle high availability', () => {
  expect(component.isHighAvailabilityEnabled).toBeFalsy();
    component.isHighAvailabilityEnabled = true;
  expect(component.isHighAvailabilityEnabled).toBeTruthy();
  });
});
