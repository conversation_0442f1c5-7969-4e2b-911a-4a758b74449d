{
  "ts-node": {
    "esm": false,
    "transpileOnly": true,
    "experimentalSpecifierResolution": "node"
  },
  "compilerOptions": {
    "esModuleInterop": true,
    "moduleResolution": "node",
    "module": "ESNext",
    "target": "ES2022",
    "allowJs": true,
    "checkJs": false,
    "outDir": "./dist",
  },
  "include": [
    "../Yoizen.yFlow.Domain/src/**/*",
    "../Yoizen.yFlow.Helpers/src/**/*",
    "../Yoizen.yFlow.Infrastructure/src/**/*",
    "../Yoizen.yFlow.WebExecutor/src/**/*",
    "../Yoizen.yFlow.IntervalServices/src/**/*"
  ],
  "exclude": []
}