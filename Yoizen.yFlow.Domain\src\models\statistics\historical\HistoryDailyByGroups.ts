import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByGroups extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare total: number;
    group: { id: string; name: string; };
    declare groupId: string;

    init(datetime: Moment, data) {
        this.initBase(datetime);

        this.channel = data.channel;
        this.flowId = data.flowId;
        this.groupId = data.groupId;
        this.version = data.version;
        this.total = data.total;
        this.group = {
            id: data.groupId,
            name: undefined
        }
    }

    type() {
        return HistoryDailyInfoTypes.Groups;
    }
}

HistoryDailyBase.init({
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    groupId: {
        type: DataTypes.STRING,
        field: 'group_id'
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_group',
    tableName: 'history_daily_by_group',
    timestamps: false
});