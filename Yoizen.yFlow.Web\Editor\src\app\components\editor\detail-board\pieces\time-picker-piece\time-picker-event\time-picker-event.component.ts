import {Component, Input, OnInit} from '@angular/core';
import { BaseDynamicComponent } from '../../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../../BasePieceVM';
import { EditorService } from '../../../../../../services/editor.service';
import { ModalService } from '../../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../../services/server.service";
import {TimePickerEvent, TimePickerPiece} from "../../../../../../models/pieces/TimePickerPiece";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../../models/TypeDefinition";
import {BlockDefinition} from "../../../../../../models/BlockDefinition";

@Component({
  selector: 'app-time-picker-event',
  templateUrl: './time-picker-event.component.html',
  styleUrls: ['./time-picker-event.component.scss']
})
export class TimePickerEventComponent implements OnInit {
  @Input() event: TimePickerEvent;
  @Input() readOnly: boolean = false;
  sourceVariableFilter = [TypeDefinition.Array];

  constructor( public editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
  }

  ngOnInit() {

  }

  setSourceVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.event.sourceVariableId = variable.Id;
    }
    else {
      this.event.sourceVariableId = null;
    }
  }

  get sourceVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.event.sourceVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }
}
