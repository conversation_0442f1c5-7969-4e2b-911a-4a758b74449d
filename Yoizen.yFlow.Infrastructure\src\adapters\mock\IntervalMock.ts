import { IIntervalPort } from "../../ports/IIntervalPort";
import { DailyBase } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/daily/DailyBase";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class IntervalMock implements IIntervalPort {
    constructor() {
        logger.info(`No se utiliza la base Intervals`);
    }

    async saveInDatabase(interval: DailyBase): Promise<any> {
        return {
            failed: false,
            message: 'Database is disabled (Contingency bot)'
        }
    }

    generateTableName(interval: DailyBase) {
        throw new Error('Method not implemented.');
    }

    async executeStoredProcedure(procedureName: string, params: string[]): Promise<any> {
        throw new Error('Method not implemented.');

    }

    async dropTable(interval: DailyBase): Promise<any> {
        throw new Error('Method not implemented.');

    }

    async connect(): Promise<void> {
    }

    async calculate(interval: DailyBase) {
        throw new Error('Method not implemented.');

    }
}