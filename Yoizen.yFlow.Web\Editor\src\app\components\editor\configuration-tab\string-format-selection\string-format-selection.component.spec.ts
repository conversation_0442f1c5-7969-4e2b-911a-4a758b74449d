import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StringFormatSelectionComponent } from './string-format-selection.component';

describe('StringFormatSelectionComponent', () => {
  let component: StringFormatSelectionComponent;
  let fixture: ComponentFixture<StringFormatSelectionComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StringFormatSelectionComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StringFormatSelectionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
