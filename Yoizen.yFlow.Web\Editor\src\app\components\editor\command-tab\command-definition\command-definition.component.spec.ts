import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommandDefinitionComponent } from './command-definition.component';

describe('CommandDefinitionComponent', () => {
  let component: CommandDefinitionComponent;
  let fixture: ComponentFixture<CommandDefinitionComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CommandDefinitionComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommandDefinitionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
