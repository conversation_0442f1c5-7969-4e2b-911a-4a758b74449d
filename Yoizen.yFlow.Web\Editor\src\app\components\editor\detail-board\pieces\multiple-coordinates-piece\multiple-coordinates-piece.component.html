<div class="multiple-coordinates card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
       container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-map-marked"></span> {{ 'CARD_MULTIPLECOORDINATES_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_MULTIPLECOORDINATES_INFO' | translate }}
  </div>
  <div class="source">
    <span class="title">{{'MULTIPLECOORDINATES_SOURCE_VARIABLE' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="VariableData"
      [includeImplicit]="false"
      (setVariable)="setVariable($event)"
      [readOnly]="readOnly"
      [typeFilters]="variableFilter">
    </app-variable-selector-input>
  </div>
  <div *ngIf="model.VariableId !== -1">
    <div class="implicit-variables">
        <div class="variables-info">{{'DYNAMICGALLERY_VARIABLELIST' | translate}}</div>
        <div class="variables-table">
          <div class="variables-header">
            <div>{{'NAME' | translate}}</div>
            <div>{{'DESCRIPTION' | translate}}</div>
          </div>
          <div class="variable-row" *ngFor="let variable of customVariables">
            <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
            <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
          </div>
        </div>
      </div>
    <div class="latitude-prop">
        <span class="title">{{'MULTIPLECOORDINATES_LATITUDE_PROP' | translate}}:</span>
        <app-input-with-variables
          [placeholder]="'MULTIPLECOORDINATES_LATITUDE_PROP' | translate"
          [(value)]="model.LatitudeProp"
          [wideInput]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly"
          class="input-variable">
        </app-input-with-variables>
    </div>
    <div class="longitude-prop">
      <span class="title">{{'MULTIPLECOORDINATES_LONGITUDE_PROP' | translate}}:</span>
      <app-input-with-variables
          [placeholder]="'MULTIPLECOORDINATES_LONGITUDE_PROP' | translate"
          [(value)]="model.LongitudeProp"
          [wideInput]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly"
          class="input-variable">
        </app-input-with-variables>
    </div>
    <div class="key-prop">
      <span class="title">{{'MULTIPLECOORDINATES_KEY_PROP' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MULTIPLECOORDINATES_KEY_PROP' | translate"
        [(value)]="model.KeyProp"
        [wideInput]="true"
        [customVariableList]="customVariables"
        [variableFinder]="searchForVariable.bind(this)"
        [JoinCustomVariable]="true"
        [disabled]="readOnly"
        class="input-variable">
      </app-input-with-variables>
    </div>
    <div class="label-prop">
      <span class="title">{{'MULTIPLECOORDINATES_LABEL_PROP' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MULTIPLECOORDINATES_LABEL_PROP' | translate"
        [(value)]="model.LabelProp"
        [wideInput]="true"
        [customVariableList]="customVariables"
        [variableFinder]="searchForVariable.bind(this)"
        [JoinCustomVariable]="true"
        [disabled]="readOnly"
        class="input-variable">
      </app-input-with-variables>
    </div>
    <div class="title-prop">
      <span class="title">{{'MULTIPLECOORDINATES_TITLE_PROP' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MULTIPLECOORDINATES_TITLE_PROP' | translate"
        [(value)]="model.TitleProp"
        [wideInput]="true"
        [customVariableList]="customVariables"
        [variableFinder]="searchForVariable.bind(this)"
        [JoinCustomVariable]="true"
        [disabled]="readOnly"
        class="input-variable">
      </app-input-with-variables>
    </div>
    <div class="icon-prop">
      <span class="title">{{'MULTIPLECOORDINATES_ICON_PROP' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MULTIPLECOORDINATES_ICON_PROP' | translate"
        [(value)]="model.LabelIcon"
        [wideInput]="true"
        [customVariableList]="customVariables"
        [variableFinder]="searchForVariable.bind(this)"
        [JoinCustomVariable]="true"
        [disabled]="readOnly"
        class="input-variable">
      </app-input-with-variables>
    </div>
    <div class="zoom-prop">
      <span class="title">{{ 'MULTIPLECOORDINATES_ZOOM_PROP' | translate }}:</span>
      <input class="input tries" type="number" placeholder="0" min="0"
             [disabled]="readOnly"
             [(ngModel)]="model.ZoomProp" [ngClass]="{'invalid-input': !model.isZoomValid()}">
    </div>
    <div class="label-group-marker-prop">
      <span class="title">{{'MULTIPLECOORDINATES_LABELGROUPMARKER_PROP' | translate}}:</span>
      <ui-switch [(ngModel)]="model.LabelGroupMarker"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="include-my-coordinates-prop">
      <span class="title">{{'MULTIPLECOORDINATES_INCLUDEMYCOORDINATES_PROP' | translate}}:</span>
      <ui-switch [(ngModel)]="model.IncludeMessageCoordinatesProp"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div *ngIf="model.IncludeMessageCoordinatesProp">
        <div class="my-latitude-prop">
          <span class="title">{{'MULTIPLECOORDINATES_INCLUDEMYLATITUDE_PROP' | translate}}:</span>
          <app-variable-selector-input
            [VariableData]="VariableLatitudeData"
            [includeImplicit]="false"
            (setVariable)="setLatitudeVariable($event)"
            [readOnly]="readOnly"
            [typeFilters]="latitudeVariableFilter">
          </app-variable-selector-input>
        </div>
        <div class="my-longitude-prop">
          <span class="title">{{'MULTIPLECOORDINATES_INCLUDEMYLONGITUDE_PROP' | translate}}:</span>
          <app-variable-selector-input
            [VariableData]="VariableLongitudeData"
            [includeImplicit]="false"
            (setVariable)="setLongitudeVariable($event)"
            [readOnly]="readOnly"
            [typeFilters]="longitudeVariableFilter">
          </app-variable-selector-input>
        </div>
        <div class="my-icon-prop">
          <span class="title">{{'MULTIPLECOORDINATES_MY_ICON_PROP' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'MULTIPLECOORDINATES_MY_ICON_PROP' | translate"
            [(value)]="model.LabelMyIcon"
            [wideInput]="true"
            [customVariableList]="customVariables"
            [variableFinder]="searchForVariable.bind(this)"
            [JoinCustomVariable]="true"
            [disabled]="readOnly"
            class="input-variable">
          </app-input-with-variables>
        </div>
    </div>
    <div class="next">
      <span class="title">{{'MULTIPLECOORDINATES_NEXTBLOCK' | translate}}:</span>
      <app-block-picker class="input"
                        [blockId]="model.NextBlockId"
                        (onSelectNewBlock)="onSelectBlock($event)"
                        (onDeleteBlock)="onDeleteBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!model.isNextBlockValid(editorService)"></app-block-picker>
    </div>
    <div *ngIf="model.NextBlockId !== null">
        <div class="dest">
            <span class="title">{{'MULTIPLECOORDINATES_DEST_VARIABLE' | translate}}:</span>
            <app-variable-selector-input
              [VariableData]="StoreVariableData"
              [includeImplicit]="false"
              (setVariable)="setStoreVariable($event)"
              [readOnly]="readOnly"
              [typeFilters]="storeVariableFilter">
            </app-variable-selector-input>
          </div>
        </div>
    </div>
</div>
