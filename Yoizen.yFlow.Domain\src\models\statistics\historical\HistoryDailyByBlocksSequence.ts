import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}
export class HistoryDailyByBlocksSequence extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare sourceBlockId: string;
    declare destBlockId: string;
    declare version: number;
    declare total: number;
    declare typeSequence: number;
    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.sourceBlockId = data.sourceBlockId;
        this.destBlockId = data.destBlockId;
        this.version = data.version;
        this.total = data.total;
        this.typeSequence = data.type;
    }

    type() {
        return HistoryDailyInfoTypes.BlocksSequence;
    }
}

HistoryDailyByBlocksSequence.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    sourceBlockId: {
        type: DataTypes.STRING,
        field: 'source_block_id',
    },
    destBlockId: {
        type: DataTypes.STRING,
        field: 'dest_block_id',
    },
    typeSequence: {
        type: DataTypes.INTEGER,
        field: 'type',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_blocks_sequence',
    tableName: 'history_daily_by_blocks_sequence',
    timestamps: false
});