import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { CloseCasePiece } from "../../../../../models/pieces/CloseCasePiece";
import {ChannelTypes} from "../../../../../models/ChannelType";
import {
  ExtendedProfileData,
  UpdateProfileActionTypes,
  UpdateProfilePiece
} from "../../../../../models/pieces/UpdateProfilePiece";

@Component({
  selector: 'app-update-profile-piece',
  templateUrl: './update-profile-piece.component.html',
  styleUrls: ['./update-profile-piece.component.scss']
})
export class UpdateProfilePieceComponent extends BasePieceVM implements OnInit {
  model: UpdateProfilePiece;
  updateProfileActionTypes = UpdateProfileActionTypes;
  channelTypes = ChannelTypes;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as UpdateProfilePiece;
  }

  deleteExtendedProfileData(i: number){
    this.model.extendedProfileData.splice(i, 1);
  }

  addExtendedProfileData(){
    this.model.extendedProfileData.push(new ExtendedProfileData());
  }
}
