# Script para actualizar yFlow.Intervals y reinstalar dependencias de Node.js
# Autor: Script automático para Windows Server
# Compatible con PowerShell 5.1

# Definir variables de tiempo para cada paso
$startOverall = Get-Date
$timings = @{}

Write-Host "=== INICIANDO PROCESO DE ACTUALIZACION DE yFlow.Intervals ===" -ForegroundColor Green
Write-Host ""

# Obtener la ruta actual
$currentPath = Get-Location
Write-Host "Directorio actual: $currentPath" -ForegroundColor Yellow
Write-Host ""

# Paso 1: Solicitar la ruta donde se va a instalar la nueva versión
$stepStart = Get-Date
Write-Host "=== PASO 1: UBICACION DE DESTINO ===" -ForegroundColor Magenta
Write-Host "Por favor, ingresa la ruta completa donde se va a instalar/actualizar yFlow.Intervals:" -ForegroundColor Cyan
$destinationPath = Read-Host "Ruta de destino"

# Validar que la ruta ingresada exista
Write-Host ""
Write-Host "Validando ruta ingresada..." -ForegroundColor Cyan
if (-not (Test-Path $destinationPath)) {
    Write-Host "Error: La ruta '$destinationPath' no existe." -ForegroundColor Red
    Write-Host "Presiona Enter para salir..."
    Read-Host
    exit 1
}

Write-Host "Ruta de destino validada correctamente." -ForegroundColor Green
Write-Host "Destino: $destinationPath" -ForegroundColor Gray
Write-Host "Origen (directorio actual): $currentPath" -ForegroundColor Gray
$timings["Paso1_Ubicacion"] = [math]::Round(((Get-Date) - $stepStart).TotalMinutes, 2)
Write-Host ""

# Paso 2: Crear backup del directorio de destino
$stepStart = Get-Date
Write-Host "=== PASO 2: CREANDO BACKUP ===" -ForegroundColor Magenta
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupName = "backup_yFlow_Intervals_$timestamp.zip"
$backupPath = Join-Path (Split-Path $destinationPath -Parent) $backupName

Write-Host "Creando backup del directorio de destino..." -ForegroundColor Cyan
Write-Host "Directorio a respaldar: $destinationPath" -ForegroundColor Yellow
Write-Host "Ubicacion del backup: $backupPath" -ForegroundColor Yellow
Write-Host ""

# Preguntar si incluir node_modules en el backup
Write-Host "¿Desea incluir la carpeta 'node_modules' en el backup?" -ForegroundColor Cyan
Write-Host "Nota: Incluir node_modules aumentara significativamente el tamaño y tiempo del backup." -ForegroundColor Yellow
Write-Host "Las dependencias se pueden reinstalar con 'npm install'." -ForegroundColor Yellow
Write-Host ""
Write-Host "[S] Si - Incluir node_modules (backup completo)" -ForegroundColor White
Write-Host "[N] No - Excluir node_modules (recomendado)" -ForegroundColor White
Write-Host ""
do {
    $includeNodeModules = Read-Host "Seleccione una opcion (S/N)"
    $includeNodeModules = $includeNodeModules.ToUpper()
} while ($includeNodeModules -ne "S" -and $includeNodeModules -ne "N")

$excludeNodeModules = $includeNodeModules -eq "N"
if ($excludeNodeModules) {
    Write-Host "Node_modules sera excluido del backup." -ForegroundColor Green
} else {
    Write-Host "Node_modules sera incluido en el backup." -ForegroundColor Yellow
}
Write-Host ""

try {
    # Crear el archivo ZIP del directorio de destino con compresión rápida
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    Add-Type -AssemblyName System.IO.Compression
    
    # Usar ZipArchive con compresión fastest para máxima velocidad
    $zipStream = [System.IO.File]::Create($backupPath)
    $archive = New-Object System.IO.Compression.ZipArchive($zipStream, [System.IO.Compression.ZipArchiveMode]::Create)
      # Obtener todos los archivos recursivamente
    $files = Get-ChildItem -Path $destinationPath -Recurse -File
    
    # Filtrar node_modules si se eligió excluirlo
    if ($excludeNodeModules) {
        $files = $files | Where-Object { $_.FullName -notmatch "\\node_modules\\" }
        Write-Host "Excluyendo archivos de node_modules del backup..." -ForegroundColor Gray
    }
    
    $totalFiles = $files.Count
    $currentFile = 0
    
    Write-Host "Procesando $totalFiles archivos..." -ForegroundColor Yellow
    
    foreach ($file in $files) {
        $currentFile++
        if ($currentFile % 50 -eq 0) {
            Write-Host "Progreso: $currentFile/$totalFiles archivos procesados" -ForegroundColor Gray
        }
        
        # Crear ruta relativa para el ZIP
        $relativePath = $file.FullName.Substring($destinationPath.Length + 1)
        
        # Crear entrada en el ZIP con compresión mínima (más rápido)
        $entry = $archive.CreateEntry($relativePath, [System.IO.Compression.CompressionLevel]::Fastest)
        
        # Copiar el archivo al ZIP
        $entryStream = $entry.Open()
        $fileStream = [System.IO.File]::OpenRead($file.FullName)
        $fileStream.CopyTo($entryStream)
        $fileStream.Close()
        $entryStream.Close()
    }
    
    $archive.Dispose()
    $zipStream.Close()
      Write-Host "Backup creado exitosamente: $backupPath" -ForegroundColor Green
}
catch {
    Write-Host "Error al crear backup: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Presiona Enter para salir..."
    Read-Host
    exit 1
}

$timings["Paso2_Backup"] = [math]::Round(((Get-Date) - $stepStart).TotalMinutes, 2)
Write-Host ""

# Paso 3: Copiar archivos al directorio de destino
$stepStart = Get-Date
Write-Host "=== PASO 3: COPIANDO NUEVA VERSION ===" -ForegroundColor Magenta
Write-Host "Copiando archivos desde: $currentPath" -ForegroundColor Cyan
Write-Host "Destino: $destinationPath" -ForegroundColor Cyan

try {
    # Obtener el nombre del script actual para excluirlo de la copia
    $scriptName = Split-Path $MyInvocation.MyCommand.Path -Leaf
    
    # Obtener todos los archivos y carpetas del origen, excluyendo node_modules, .git, backups y el script actual
    $itemsToCopy = Get-ChildItem -Path $currentPath -Force | Where-Object { 
        $_.Name -notmatch "^(node_modules|\.git|backup_yFlow_Intervals_.*)$" -and $_.Name -ne $scriptName
    }
    
    $totalItems = $itemsToCopy.Count
    $currentItem = 0
    
    Write-Host "Elementos a copiar: $totalItems" -ForegroundColor Yellow
    Write-Host ""
    
    foreach ($item in $itemsToCopy) {
        $currentItem++
        $targetPath = Join-Path $destinationPath $item.Name
        
        Write-Host "[$currentItem/$totalItems] Copiando: $($item.Name)" -ForegroundColor Gray
        
        if ($item.PSIsContainer) {
            # Es un directorio - usar Copy-Item con recursión
            if (Test-Path $targetPath) {
                Remove-Item $targetPath -Recurse -Force
            }
            Copy-Item $item.FullName $targetPath -Recurse -Force
        } else {
            # Es un archivo
            Copy-Item $item.FullName $targetPath -Force
        }
    }
      Write-Host ""
    Write-Host "Todos los archivos copiados exitosamente." -ForegroundColor Green
}
catch {
    Write-Host "Error al copiar archivos: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Presiona Enter para salir..."
    Read-Host
    exit 1
}

$timings["Paso3_CopiarArchivos"] = [math]::Round(((Get-Date) - $stepStart).TotalMinutes, 2)
Write-Host ""

# Paso 4: Limpieza de node_modules y package-lock.json
$stepStart = Get-Date
Write-Host "=== PASO 4: LIMPIEZA Y REINSTALACION ===" -ForegroundColor Magenta

# Verificar si existe la carpeta node_modules en el directorio de destino
Write-Host "Verificando existencia de carpeta node_modules en destino..." -ForegroundColor Cyan
$nodeModulesPath = Join-Path $destinationPath "node_modules"
if (Test-Path $nodeModulesPath) {
    Write-Host "Carpeta node_modules encontrada. Procediendo a eliminarla..." -ForegroundColor Yellow
    try {
        Remove-Item -Path $nodeModulesPath -Recurse -Force
        Write-Host "Carpeta node_modules eliminada exitosamente." -ForegroundColor Green
    }
    catch {
        Write-Host "Error al eliminar node_modules: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Presiona Enter para salir..."
        Read-Host
        exit 1
    }
} else {
    Write-Host "No se encontro carpeta node_modules (no es necesario eliminarla)." -ForegroundColor Gray
}

Write-Host ""

# Verificar si existe el archivo package-lock.json en el directorio de destino
Write-Host "Verificando existencia de package-lock.json en destino..." -ForegroundColor Cyan
$packageLockPath = Join-Path $destinationPath "package-lock.json"
if (Test-Path $packageLockPath) {
    Write-Host "Archivo package-lock.json encontrado. Procediendo a eliminarlo..." -ForegroundColor Yellow
    try {
        Remove-Item -Path $packageLockPath -Force
        Write-Host "Archivo package-lock.json eliminado exitosamente." -ForegroundColor Green
    }
    catch {
        Write-Host "Error al eliminar package-lock.json: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Presiona Enter para salir..."
        Read-Host
        exit 1
    }
} else {
    Write-Host "No se encontro package-lock.json (no es necesario eliminarlo)." -ForegroundColor Gray
}

Write-Host ""

# Verificar si existe package.json en el directorio de destino antes de ejecutar npm install
Write-Host "Verificando existencia de package.json en destino..." -ForegroundColor Cyan
$packageJsonPath = Join-Path $destinationPath "package.json"
if (-not (Test-Path $packageJsonPath)) {
    Write-Host "Error: No se encontro package.json en el directorio de destino." -ForegroundColor Red
    Write-Host "No se puede ejecutar npm install sin package.json." -ForegroundColor Red
    Write-Host "Presiona Enter para salir..."
    Read-Host
    exit 1
}
Write-Host "Archivo package.json encontrado en destino." -ForegroundColor Green

Write-Host ""

# Cambiar al directorio de destino para ejecutar npm install
Write-Host "=== PASO 5: INSTALACION DE DEPENDENCIAS ===" -ForegroundColor Magenta
Write-Host "Cambiando al directorio de destino..." -ForegroundColor Cyan
$originalLocation = Get-Location
try {
    Set-Location $destinationPath
    Write-Host "Directorio de trabajo cambiado a: $destinationPath" -ForegroundColor Green
    Write-Host ""
    
    # Ejecutar npm install con legacy-peer-deps
    Write-Host "Iniciando instalacion de dependencias..." -ForegroundColor Cyan
    Write-Host "Ejecutando: npm install --legacy-peer-deps" -ForegroundColor Yellow
    Write-Host ""

    try {
        # Ejecutar npm install y mostrar la salida en tiempo real
        $process = Start-Process -FilePath "npm" -ArgumentList "install", "--legacy-peer-deps" -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Host ""
            Write-Host "Instalacion completada exitosamente!" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "Error durante la instalacion. Codigo de salida: $($process.ExitCode)" -ForegroundColor Red
            Set-Location $originalLocation
            Write-Host "Presiona Enter para salir..."
            Read-Host
            exit 1
        }
    }
    catch {
        Write-Host ""
        Write-Host "Error al ejecutar npm install: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Asegurate de que Node.js y npm esten instalados y disponibles en el PATH." -ForegroundColor Yellow
        Set-Location $originalLocation
        Write-Host "Presiona Enter para salir..."
        Read-Host
        exit 1
    }
}
finally {
    # Volver al directorio original
    Set-Location $originalLocation
}

$timings["Paso4_LimpiezaYReinstalacion"] = [math]::Round(((Get-Date) - $stepStart).TotalMinutes, 2)
$totalTime = [math]::Round(((Get-Date) - $startOverall).TotalMinutes, 2)

Write-Host ""
Write-Host "=== PROCESO COMPLETADO EXITOSAMENTE ===" -ForegroundColor Green
Write-Host ""
Write-Host "Resumen de acciones realizadas:" -ForegroundColor White
Write-Host "• Validacion de la ruta de destino para la instalacion" -ForegroundColor Gray
Write-Host "• Creacion de backup: $backupPath" -ForegroundColor Gray
Write-Host "• Copia de archivos desde: $currentPath" -ForegroundColor Gray
Write-Host "• Copia de archivos hacia: $destinationPath" -ForegroundColor Gray
Write-Host "• Verificacion y eliminacion de node_modules en destino (si existia)" -ForegroundColor Gray
Write-Host "• Verificacion y eliminacion de package-lock.json en destino (si existia)" -ForegroundColor Gray
Write-Host "• Instalacion de dependencias con --legacy-peer-deps en destino" -ForegroundColor Gray
Write-Host ""
Write-Host "=== TIEMPOS DE EJECUCION ===" -ForegroundColor Cyan
Write-Host "Paso 1 - Ubicacion de destino: $($timings['Paso1_Ubicacion']) minutos" -ForegroundColor White
Write-Host "Paso 2 - Creacion de backup: $($timings['Paso2_Backup']) minutos" -ForegroundColor White
Write-Host "Paso 3 - Copia de archivos: $($timings['Paso3_CopiarArchivos']) minutos" -ForegroundColor White
Write-Host "Paso 4 - Limpieza y reinstalacion: $($timings['Paso4_LimpiezaYReinstalacion']) minutos" -ForegroundColor White
Write-Host "TIEMPO TOTAL: $totalTime minutos" -ForegroundColor Yellow
Write-Host ""
Write-Host "Presiona Enter para cerrar la consola..." -ForegroundColor Yellow
Read-Host