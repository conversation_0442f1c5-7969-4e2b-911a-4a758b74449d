import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { AccountUnlinkingPiece } from 'src/app/models/pieces/AccountUnlinkingPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-account-unlinking-piece',
  templateUrl: './account-unlinking-piece.component.html',
  styleUrls: ['./account-unlinking-piece.component.scss']
})
export class AccountUnlinkingPieceComponent extends BasePieceVM implements OnInit {
  model: AccountUnlinkingPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
  }
}
