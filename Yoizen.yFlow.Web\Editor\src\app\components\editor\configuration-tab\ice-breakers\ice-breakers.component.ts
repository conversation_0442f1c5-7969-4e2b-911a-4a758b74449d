import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import { EditorService } from '../../../../services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import {ChannelTypes} from "../../../../models/ChannelType";
import {FlowDefinition} from "../../../../models/FlowDefinition";
import {ButtonPiece, ButtonType} from "../../../../models/pieces/ButtonPiece";
import {IceBreakers} from "../../../../models/IceBreakers";
import {ButtonElementComponent} from "../../detail-board/pieces/message-piece/button-element/button-element.component";
import {SingleOverlayService} from "../../../../services/SingleOverlay.service";
import {DragulaService} from "ng2-dragula";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-ice-breakers',
  templateUrl: './ice-breakers.component.html',
  styleUrls: ['./ice-breakers.component.scss']
})
export class IceBreakersComponent implements OnInit, OnDestroy {
  model : IceBreakers;
  flow : FlowDefinition;
  expandButton : ButtonPiece;
  currentBtnDetail : ButtonElementComponent;
  subs = new Subscription();

  @Input() readOnly: boolean = false;

  constructor(private editorService : EditorService, private translateService: TranslateService, private singleOverlay: SingleOverlayService, private dragulaService : DragulaService) {
  }

  ngOnInit() {
    this.model = this.editorService.getIceBreakers();
    this.flow = this.editorService.getCurrentFlow();

    if (this.dragulaService.find('MESSAGE_BUTTON_ELEMENT') == null) {
      this.dragulaService.createGroup('MESSAGE_BUTTON_ELEMENT', {
        moves: function (el: any, container: any, handle: any, sibling: any): any {
          let parentElement: HTMLElement = handle.parentElement;
          while (parentElement !== null) {
            if (parentElement.classList.contains('button-content')) {
              break;
            }

            if (parentElement.classList.contains('button-details')) {
              return false;
            }

            parentElement = parentElement.parentElement;
          }

          return true;
        }
      });
    }

    this.subs.add(this.dragulaService.dropModel("MESSAGE_BUTTON_ELEMENT")
      .subscribe(({ el, target, source, sourceModel, targetModel, item }) => {
        this.model.buttons = targetModel;
      })
    );
  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.subs.unsubscribe();
  }

  getButtonLimitForIndex(index: number) : number {
    switch (this.flow.channel) {
      case ChannelTypes.FacebookMessenger:
        if (index == 0) {
          return 1;
        }
        return 5;
      case ChannelTypes.Twitter:
        return 20;
      case ChannelTypes.Chat:
        return 5;
      case ChannelTypes.Instagram:
        return 4;
    }
  }

  getAllowedButtonTypes() : ButtonType[] {
    return [ButtonType.Redirect];
  }

  canAddButton() : boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Chat) {
      return this.model.buttons.length < 5;
    }

    if (this.flow.channel === ChannelTypes.Instagram) {
      return this.model.buttons.length < 4;
    }

    return false;
  }

  addNewButton() {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = new ButtonPiece();
    this.model.buttons.push(this.expandButton);
  }

  deleteButtonElement(element) {
    this.model.buttons.splice(element, 1);
  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = btn;
  }
}
