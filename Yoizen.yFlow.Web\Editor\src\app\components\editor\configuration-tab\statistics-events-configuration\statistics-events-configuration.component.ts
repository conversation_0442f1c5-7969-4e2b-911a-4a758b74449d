import { Component, EventEmitter, Input, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { ToasterService } from 'angular2-toaster';
import { EditorService } from 'src/app/services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from "../../../../services/Tools/ModalService";
import { StatisticEventDefinition } from 'src/app/models/StatisticEventDefinition';
import { MatPaginator, MatTableDataSource, MatSort} from '@angular/material';
import { StructuredStatisticEventDataComponent } from '../../popups/structured-statistic-event-data/structured-statistic-event-data.component';

@Component({
  selector: 'app-statistics-events-configuration',
  templateUrl: './statistics-events-configuration.component.html',
  styleUrls: ['./statistics-events-configuration.component.scss']
})
export class StatisticsEventsConfigurationComponent implements OnInit, AfterViewInit {
  @Input() readOnly: boolean = false;
  events: StatisticEventDefinition[] = null;

  displayedColumns: string[] = ['Name', 'Enabled', 'StructuredDataEnabled', 'StructuredData'];
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  dataSource = new MatTableDataSource(this.editorService.getAllStatisticEventList());

  constructor(
    public editorService: EditorService, 
    private readonly modalService: ModalService,
    private readonly toasterService: ToasterService,
    private readonly translateService: TranslateService
  ) { }

  ngOnInit() {
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  public isValid(): boolean {
    if (!this.editorService.newStatisticsEventsAreValid)
      return false;

    return this.editorService.statisticEventConfigurationIsValid();
  }

  public validateEventName(element: StatisticEventDefinition): void {
    if (!element.Name || element.Name.trim() === '') {
      return;
    }

    const sanitizedName = this.isValidEventName(element.Name.trim());
    if (!sanitizedName) {
      this.toasterService.pop({
        type: 'error',
        body: this.translateService.instant('INVALID_STATISTIC_EVENT_NAME'),
        timeout: 5000
      });
      this.editorService.newStatisticsEventsAreValid = false;
      return;
    }

    this.editorService.newStatisticsEventsAreValid = true;
  }

  private isValidEventName(name: string): boolean {
    // Verificar si contiene caracteres problemáticos para el sistema de archivos
    const invalidCharsRegex = /[/\\:*?"<>|[\]]/;
    return !invalidCharsRegex.test(name);
  }

  openStructuredDataModal(statisticEvent: StatisticEventDefinition){
    const statisticEventEmitter = new EventEmitter<StatisticEventDefinition>();

    statisticEventEmitter.subscribe((event: StatisticEventDefinition) => {
      // Handle the statistic event update if needed
    });
    this.modalService.init(StructuredStatisticEventDataComponent, { statisticEvent: statisticEvent, readOnly: this.readOnly }, {saveStatisticEvent: statisticEventEmitter});
  }
}
