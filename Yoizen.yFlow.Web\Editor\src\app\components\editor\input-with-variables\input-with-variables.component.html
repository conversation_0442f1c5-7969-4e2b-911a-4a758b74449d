<div class="app-input-with-variables" [ngClass]="{'wide-input': wideInput, 'disabled': disabled, 'multiline': isTextArea}">
  <div class="help" (click)="openFunctionModal()" *ngIf="!disabled && !insideHelp"
       data-toggle="tooltip" ngbTooltip="{{ 'HELP_FUNCTIONS' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-help">
    <span class="fa fa-question-circle"></span>
  </div>
  <input *ngIf="!isTextArea" #inputArea
         class="input" type="text" [placeholder]="placeholder | translate" [(ngModel)]="inputValue"
         (ngModelChange)="testInputForVariable($event)"
         [disabled]="disabled"
         [ngStyle]="extendedStyles"
         [ngClass]="{'invalid-input': !validate(inputValue), 'hide': !withFocus }"
         (focusout)="onInputFocusOut()"
         (focusin)="onInputFocusIn()"
         [spellcheck]="spellCheck"
         [attr.list]="list"/>
  <div *ngIf="!isTextArea" class="input-mask" #maskArea
       (click)="focusInputArea()"
       (focus)="focusInputArea()"
       tabindex="0"
       [ngClass]="{'invalid-input': !validate(inputValue), 'hide': withFocus }"
       [ngStyle]="extendedStyles">
    <span [innerHTML]="parseInputText()" *ngIf="inputValue !== null && inputValue?.length > 0"></span>
    <span class="placeholder" *ngIf="inputValue === null || inputValue?.length === 0">{{ placeholder | translate }}</span>
  </div>
  <textarea *ngIf="isTextArea" #inputArea class="input" type="text" [placeholder]="placeholder | translate"
            [(ngModel)]="inputValue"
            (ngModelChange)="testInputForVariable($event)"
            [ngClass]="{'invalid-input': !validate(inputValue), 'hide': !withFocus }"
            [ngStyle]="extendedStyles"
            [disabled]="disabled"
            (focusout)="onInputFocusOut()"
            (focusin)="onInputFocusIn()"
            [spellcheck]="spellCheck"></textarea>
  <div *ngIf="isTextArea && inputValue !== null && inputValue?.length > 0" class="textarea-mask" #maskArea
       (click)="focusInputArea()"
       (focus)="focusInputArea()"
       [ngClass]="{'invalid-input': !validate(inputValue), 'hide': withFocus }"
       [ngStyle]="extendedStyles"
       tabindex="0"
       [innerHTML]="parseInputText()"></div>
  <div *ngIf="isTextArea && (!inputValue || inputValue.length === 0)" class="textarea-mask" #maskArea
       (click)="focusInputArea()"
       (focus)="focusInputArea()"
       [ngClass]="{ 'invalid-input': !validate(''), 'hide': withFocus }"
       tabindex="0"
       [ngStyle]="extendedStyles">
    <span class="placeholder">{{ placeholder | translate }}</span>
  </div>
  <div #varPicker class="picker-container" [ngStyle]="{'position': insideHelp? 'fixed' : 'absolute'}" *ngIf="showVarPicker">
    <app-variable-selector
      [includeImplicits]="true"
      [VariableName]="currentVarName"
      [createCustomVariable]="variableCreator?.bind(this)"
      [JoinCustomVariable]="JoinCustomVariable"
      [fitContent]="!insideHelp"
      [CustomVariables]="customVariableList" (onSelectVariable)="onSelectVariable($event)"></app-variable-selector>
  </div>
</div>
