<div class="gallery card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-images"></span> {{ 'CARD_DYNAMICGALLERY_TITLE' | translate }}
  </div>
  <div class="sourcearray">
    <span class="title">{{'DYNAMICGALLERY_SOURCE_ARRAY' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="InputVariableData"
      (setVariable)="setInputVariable($event)"
      [validator]="validateVariable"
      [readOnly]="readOnly"
      [typeFilters]="variableFilter">
    </app-variable-selector-input>
  </div>
  <div class="item-container" *ngIf="InputVariableData">
    <div class="implicit-variables">
      <div class="variables-info">{{'DYNAMICGALLERY_VARIABLELIST' | translate}}</div>
      <div class="variables-table">
        <div class="variables-header">
          <div>{{'NAME' | translate}}</div>
          <div>{{'DESCRIPTION' | translate}}</div>
        </div>
        <div class="variable-row" *ngFor="let variable of customVariables">
          <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
          <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
        </div>
      </div>
    </div>
    <div class="add-condition" (click)="model.HasVisibilityCondition = true" *ngIf="!model.HasVisibilityCondition && !readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_VISIBILITY_CONDITION' | translate }}
    </div>
    <div class="condition" *ngIf="model.HasVisibilityCondition">
      <label class="title">{{ 'CARD_VISIBILITY_CONDITION' | translate }}</label>
      <div class="data">
        <app-input-with-variables [placeholder]="'VALUE' | translate"
                                  class="item"
                                  [(value)]="model.FirstOperand"
                                  [disabled]="readOnly"
                                  [wideInput]="true"
                                  [customVariableList]="customVariables"
                                  [variableFinder]="searchForVariable.bind(this)"
                                  [JoinCustomVariable]="true"
                                  [ngClass]="{'validation-error': !model.isFirstOperandValid(editorService)}"></app-input-with-variables>
        <select class="select"
                [(ngModel)]="model.Operator"
                [disabled]="readOnly"
                [ngClass]="{'validation-error': !model.isOperatorValid()}">
          <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
        </select>
        <app-input-with-variables *ngIf="showOperand()"
                                  class="item"
                                  [placeholder]="'VALUE' | translate"
                                  [(value)]="model.SecondOperand"
                                  [disabled]="readOnly"
                                  [wideInput]="true"
                                  [customVariableList]="customVariables"
                                  [variableFinder]="searchForVariable.bind(this)"
                                  [JoinCustomVariable]="true"
                                  [ngClass]="{'validation-error': !model.isSecondOperandValid(editorService)}"></app-input-with-variables>
      </div>
      <div class="trash" (click)="model.HasVisibilityCondition=false" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'CARD_REMOVE_CONDITION' | translate }}" placement="top" container="body"
           tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>
    <div class="image" [ngClass]="{ 'showImg': isStringValid(model.image.imageUrl)}">
      <img [src]="model.image.imageUrl"
           *ngIf="isStringValid(model.image.imageUrl)" />
      <span class="fa fa-camera"></span>
    </div>
    <ul>
      <li>
        <app-input-with-variables
          [placeholder]="'IMAGE_URL' | translate"
          [(value)]="model.image.imageUrl"
          [validator]="isUrlValid"
          [wideInput]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly"
          [spellCheck]="false">
        </app-input-with-variables>
      </li>
      <li>
        <app-input-with-variables
          [placeholder]="'TITLE' | translate"
          [(value)]="model.image.title"
          [wideInput]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly">
        </app-input-with-variables>
      </li>
      <li>
        <app-input-with-variables
          [placeholder]="'SUBTITLE' | translate"
          [(value)]="model.image.subtitle"
          [wideInput]="true"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly">
        </app-input-with-variables>
      </li>
      <li>
        <app-input-with-variables
          [placeholder]="'ACTION_URL' | translate"
          [(value)]="model.image.targetUrl"
          [validator]="isUrlValid"
          [wideInput]="true"
          [spellCheck]="false"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [disabled]="readOnly">
        </app-input-with-variables>
      </li>
    </ul>
    <div>
      <app-button-element *ngFor="let button of model.image.buttons let i = index"
                          [Model]="button" [Index]="i"
                          (onDelete)="deleteButtonElement(model.image)"
                          (onShowDetail)="onShowButtonDetail($event)"
                          [readOnly]="readOnly"
                          [customVariableList]="customVariables"
                          [expandedBtn]="expandButton">
      </app-button-element>
    </div>
    <div class="addButton" (click)="addButton()" *ngIf="canAddButton()">
      <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
    </div>
    <div class="addQuick" *ngIf="canCreateQuickReply()" (click)="addQuickReplyPiece()">
      <span class="fa fa-plus"></span> {{ 'QUICKREPLY_ADD' | translate }}
    </div>
  </div>
</div>
