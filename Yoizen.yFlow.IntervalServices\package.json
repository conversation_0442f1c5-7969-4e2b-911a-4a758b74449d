{"name": "yoizen.yflow.intervalservices", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node --env-file=.env --loader ts-node/esm src/index.ts", "startDocker": "node ./dist/Yoizen.yFlow.IntervalServices/src/index.js", "build": "tsc", "buildDocker": "tsc --build tsconfig.docker.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts", "dev": "node --import ./preload.mjs --loader ts-node/esm src/index.ts", "debug": "node --inspect=9230 --import ./preload.mjs --loader ts-node/esm src/index.ts", "debug-brk": "node --inspect-brk=9230 --watch --import ./preload.mjs --loader ts-node/esm src/index.ts"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "console-stamp": "^3.1.2", "crypto-js": "4.1.1", "jszip": "^3.10.1", "moment": "^2.30.1", "mssql": "^6.4.1", "mysql2": "^3.9.7", "node-cache": "^5.1.2", "node-xlsx": "^0.24.0", "sequelize": "^6.37.2", "zip-dir": "^2.0.0"}, "devDependencies": {"@types/mssql": "^9.1.5", "@types/mysql": "^2.15.26", "@types/node": "^20.12.4", "ts-node": "^10.9.2", "typescript": "^4.9.5", "dotenv": "^16.4.7"}}