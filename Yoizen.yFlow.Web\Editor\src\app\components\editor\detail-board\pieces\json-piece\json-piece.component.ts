import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';

@Component({
  selector: 'app-json-piece',
  templateUrl: './json-piece.component.html',
  styleUrls: ['./json-piece.component.scss']
})
export class JsonPieceComponent extends BasePieceVM implements OnInit {

  model : JsonPiece;
  isJsonValid : Boolean;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as JsonPiece;
    this.validate();
  }

  validate() : Bo<PERSON>an {
    this.isJsonValid = this.model.isValid(this.editorService);
    return this.isJsonValid;
  }

  getValidator() {
    return this.validate.bind(this);
  }
}
