import { EvaluateCognitivityPieceComponent } from './pieces/evaluate-cognitivity-piece/evaluate-cognitivity-piece.component';
import { CalendarPieceComponent } from './pieces/calendar-piece/calendar-piece.component';
import { Component, OnInit, Input, EventEmitter } from '@angular/core';
import { ComponentFactoryService } from '../../../services/component-factory-service.service';
import { ActionsPieceComponent } from './pieces/actions-piece/actions-piece.component';
import { MessagePieceComponent } from './pieces/message-piece/message-piece.component';
import { GalleryPieceComponent } from './pieces/gallery-piece/gallery-piece.component';
import { AttachPieceComponent } from './pieces/attach-piece/attach-piece.component';
import { QuickRepliesPieceComponent } from './pieces/quick-replies-piece/quick-replies-piece.component';
import { JsonPieceComponent } from './pieces/json-piece/json-piece.component';
import { IntegrationPieceComponent } from './pieces/integration-piece/integration-piece.component';
import { DeriveOperatorPieceComponent } from './pieces/derive-operator-piece/derive-operator-piece.component';
import { MailPieceComponent } from './pieces/mail-piece/mail-piece.component';
import { DataEntryPieceComponent } from './pieces/data-entry-piece/data-entry-piece.component';
import { AddButtonPieceComponent } from './pieces/add-button-piece/add-button-piece.component';
import { ConditionPieceComponent } from './pieces/condition-piece/condition-piece.component';
import { EditorService } from '../../../services/editor.service';
import { EditorState } from '../../../models/EditorState';
import arrayMove from 'array-move';
import { SetVariableComponent } from './pieces/set-variable/set-variable.component';
import { JumpToBlockPieceComponent } from "./pieces/jump-piece/jump-to-block-piece.component";
import { ConcatenateComponent } from './pieces/concatenate/concatenate.component';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { DisplayConnectionsComponent } from '../popups/display-connections/display-connections.component';
import { ErrorPopupComponent } from "../../error-popup/error-popup.component";
import { TranslateService } from "@ngx-translate/core";
import { BasePiece } from "../../../models/pieces/BasePiece";
import { DynamicGalleryPieceComponent } from "./pieces/dynamic-gallery-piece/dynamic-gallery-piece.component";
import { ReturnToLastBlockPieceComponent } from "./pieces/return-piece/return-to-last-block-piece.component";
import { CloseCasePieceComponent } from "./pieces/close-case-piece/close-case-piece.component";
import { ValidateBackDniPieceComponent } from "./pieces/validate-back-dni-piece/validate-back-dni-piece.component";
import { ValidateFrontalDniPieceComponent } from "./pieces/validate-frontal-dni-piece/validate-frontal-dni-piece.component";
import { VariableConditionPieceComponent } from "./pieces/variable-condition-piece/variable-condition-piece.component";
import { PieceSelectorDialogComponent } from "../popups/piece-selector-dialog/piece-selector-dialog.component";
import { PieceType } from "../../../models/PieceType";
import { BlockDefinition } from "../../../models/BlockDefinition";
import { ResetVariablesPieceComponent } from "./pieces/reset-variables-piece/reset-variables-piece.component";
import { SwitchJumpToBlockPieceComponent } from "./pieces/switch-jump-piece/switch-jump-to-block-piece.component";
import { VideoEmbedPieceComponent } from "./pieces/video-embed-piece/video-embed-piece.component";
import { StoreMessagePieceComponent } from "./pieces/store-message-piece/store-message-piece.component";
import { TagPieceComponent } from "./pieces/tag-piece/tag-piece.component";
import { CoordinatesPieceComponent } from "./pieces/coordinates-piece/coordinates-piece.component";
import { NearestCoordinatesPieceComponent } from "./pieces/nearest-coordinates-piece/nearest-coordinates-piece.component";
import { GetElementFromArrayPieceComponent } from './pieces/get-element-from-array-piece/get-element-from-array-piece.component';
import { GetElementsFromArrayPieceComponent } from './pieces/get-elements-from-array-piece/get-elements-from-array-piece.component';
import { LogPieceComponent } from "./pieces/log-piece/log-piece.component";
import { MultipleCoordinatesPieceComponent } from "./pieces/multiple-coordinates-piece/multiple-coordinates-piece.component";
import { MultiMediaEntryPieceComponent } from './pieces/multimedia-entry-piece/multimedia-entry-piece.component';
import { GeocoderGooglePieceComponent } from './pieces/geocoder-google-piece/geocoder-google-piece.component';
import { PostMessagePieceComponent } from "./pieces/post-message-piece/post-message-piece.component";
import { EvaluateCommandsPieceComponent } from './pieces/evaluate-commands-piece/evaluate-commands-piece.component';
import { CallBlockAsProcedureComponent } from "./pieces/call-block-as-procedure-piece/call-block-as-procedure.component";
import { StatisticEventPieceComponent } from './pieces/statistic-event-piece/statistic-event-piece.component';
import { UpdateProfilePieceComponent } from "./pieces/update-profile-piece/update-profile-piece.component";
import { UpdateCasePieceComponent } from "./pieces/update-case-piece/update-case-piece.component";
import { ShortenUrlComponent } from './pieces/shorten-url-piece/shorten-url-piece.component';
import { MultipleMessagesPieceComponent } from "./pieces/multiple-messages-piece/multiple-messages-piece.component";
import { WAMenuPieceComponent } from './pieces/wa-menu-piece/wa-menu-piece.component';
import { MultipleAttachmentsPieceComponent } from './pieces/multiple-attachments-piece/multiple-attachments-piece.component';
import { DbQueryPieceComponent } from './pieces/db-query-piece/db-query-piece.component';
import { InteractiveMessageListPieceComponent } from "./pieces/interactive-message-list-piece/interactive-message-list-piece.component";
import { InteractiveMessageButtonsPieceComponent } from "./pieces/interactive-message-buttons-piece/interactive-message-buttons-piece.component";
import { EncodeBase64ImageComponent } from './pieces/encode-base64-image/encode-base64-image.component';
import { SignaturePadPieceComponent } from './pieces/signature-pad-piece/signature-pad-piece.component';
import { PaymentGatewayPieceComponent } from './pieces/payment-gateway-piece/payment-gateway-piece.component';
import { MarkMessageAsPendingPieceComponent } from "./pieces/mark-message-as-pending-piece/mark-message-as-pending-piece.component";
import { InteractiveMessageProductlistPieceComponent } from "./pieces/interactive-message-productlist-piece/interactive-message-productlist-piece.component";
import { ChannelTypes } from "../../../models/ChannelType";
import { finalize } from "rxjs/operators";
import { StatusResponse } from "../../../models/StatusResponse";
import { ServerService } from "../../../services/server.service";
import { InteractiveMessageProductPieceComponent } from "./pieces/interactive-message-product-piece/interactive-message-product-piece.component";
import { StickerPieceComponent } from './pieces/sticker-piece/sticker-piece.component';
import { FormPieceComponent } from "./pieces/form-piece/form-piece.component";
import { RichLinkPieceComponent } from "./pieces/rich-link-piece/rich-link-piece.component";
import { TimePickerPieceComponent } from "./pieces/time-picker-piece/time-picker-piece.component";
import {
  AppleInteractiveMessageAuthenticationPieceComponent
} from "./pieces/apple-interactive-message-authentication-piece/apple-interactive-message-authentication-piece.component";
import {
  AppleInteractiveMessageImessageAppPieceComponent
} from "./pieces/apple-interactive-message-imessage-app-piece/apple-interactive-message-imessage-app-piece.component";
import {
  AppleInteractiveMessageApplePayPieceComponent
} from "./pieces/apple-interactive-message-applepay-piece/apple-interactive-message-applepay-piece.component";
import { SendHsmPieceComponent } from './pieces/send-hsm-piece/send-hsm-piece.component';
import { AccountLinkingPieceComponent } from './pieces/account-linking-piece/account-linking-piece.component';
import { AccountUnlinkingPieceComponent } from './pieces/account-unlinking-piece/account-unlinking-piece.component';
import { BiometricPieceComponent } from './pieces/biometric-piece/biometric-piece.component';
import { SmartFormPieceComponent } from './pieces/ysmart-form-piece/smart-form-piece.component';
import { SetVariableFromEntityComponent } from './pieces/set-variable-from-entity/set-variable-from-entity.component';
import { CommentPieceComponent } from './pieces/comment-piece/comment-piece.component';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { AuthenticateAnonymousProfilePieceComponent } from './pieces/authenticate-anonymous-profile-piece/authenticate-anonymous-profile-piece.component';
import { KnowledgeBasePieceComponent } from './pieces/knowledge-base-piece/knowledge-base-piece.component';
import { GetMessageEntitiesPieceComponent } from './pieces/get-message-entities-piece/get-message-entities-piece.component';
import { InteractiveMessageUrlbuttonPieceComponent } from './pieces/interactive-message-urlbutton-piece/interactive-message-urlbutton-piece.component';
import { InvokeWhatsappFlowPieceComponent } from 'src/app/components/editor/detail-board/pieces/invoke-whatsapp-flow-piece/invoke-whatsapp-flow-piece.component';
import { EncryptPieceComponent } from './pieces/encrypt-piece/encrypt-piece.component';
import { DecryptPieceComponent } from './pieces/decrypt-piece/decrypt-piece.component';
import { MultimediaAnalysisPieceComponent } from './pieces/multimedia-analysis-piece/multimedia-analysis-piece.component';
import { ProfileListPieceComponent } from './pieces/profile-list-piece/profile-list-piece.component';

@Component({
  selector: 'app-detail-board',
  templateUrl: './detail-board.component.html',
  styleUrls: ['./detail-board.component.scss']
})
export class DetailBoardComponent implements OnInit {
  @Input() readOnly: boolean = false;
  showMenuPiece: boolean = false;
  editorState: EditorState;
  isShowingStats: boolean = false;
  stats: any = null;
  loading: boolean = false;
  mCurrentModule: ModuleDefinition;
  isMasterBot: boolean = false;
  constructor(private readonly componentHolderService: ComponentFactoryService,
    private readonly editorService: EditorService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly serverService: ServerService) {
    this.editorState = editorService.getEditorState();
    componentHolderService.registerComponent("actions-piece", ActionsPieceComponent);
    componentHolderService.registerComponent("message-piece", MessagePieceComponent);
    componentHolderService.registerComponent("interactive-message-list-piece", InteractiveMessageListPieceComponent);
    componentHolderService.registerComponent("interactive-message-buttons-piece", InteractiveMessageButtonsPieceComponent);
    componentHolderService.registerComponent("interactive-message-urlbutton-piece", InteractiveMessageUrlbuttonPieceComponent);
    componentHolderService.registerComponent("interactive-message-productlist-piece", InteractiveMessageProductlistPieceComponent);
    componentHolderService.registerComponent("interactive-message-product-piece", InteractiveMessageProductPieceComponent);
    componentHolderService.registerComponent("gallery-piece", GalleryPieceComponent);
    componentHolderService.registerComponent("dynamic-gallery-piece", DynamicGalleryPieceComponent);
    componentHolderService.registerComponent("attachment-piece", AttachPieceComponent);
    componentHolderService.registerComponent("video-embed-piece", VideoEmbedPieceComponent);
    componentHolderService.registerComponent("form-piece", FormPieceComponent);
    componentHolderService.registerComponent("quick-reply-piece", QuickRepliesPieceComponent);
    componentHolderService.registerComponent("json-piece", JsonPieceComponent);
    componentHolderService.registerComponent("rich-link-piece", RichLinkPieceComponent);
    componentHolderService.registerComponent("apple-interactive-message-authentication-piece", AppleInteractiveMessageAuthenticationPieceComponent);
    componentHolderService.registerComponent("apple-interactive-message-imessage-app-piece", AppleInteractiveMessageImessageAppPieceComponent);
    componentHolderService.registerComponent("apple-interactive-message-applepay-piece", AppleInteractiveMessageApplePayPieceComponent);
    componentHolderService.registerComponent("time-picker-piece", TimePickerPieceComponent);
    componentHolderService.registerComponent("integration-piece", IntegrationPieceComponent);
    componentHolderService.registerComponent("derive-piece", DeriveOperatorPieceComponent);
    componentHolderService.registerComponent("mark-message-as-pending-piece", MarkMessageAsPendingPieceComponent);
    componentHolderService.registerComponent("mail-piece", MailPieceComponent);
    componentHolderService.registerComponent("data-entry-piece", DataEntryPieceComponent);
    componentHolderService.registerComponent("multimedia-entry-piece", MultiMediaEntryPieceComponent);
    componentHolderService.registerComponent("geocoder-google-piece", GeocoderGooglePieceComponent);
    componentHolderService.registerComponent("add-button-piece", AddButtonPieceComponent);
    componentHolderService.registerComponent('condition-piece', ConditionPieceComponent);
    componentHolderService.registerComponent('validate-frontal-dni-piece', ValidateFrontalDniPieceComponent);
    componentHolderService.registerComponent('validate-back-dni-piece', ValidateBackDniPieceComponent);
    componentHolderService.registerComponent('encode-base64-image', EncodeBase64ImageComponent);
    componentHolderService.registerComponent('variable-condition-piece', VariableConditionPieceComponent);
    componentHolderService.registerComponent('set-variable', SetVariableComponent);
    componentHolderService.registerComponent('set-variable-from-entity', SetVariableFromEntityComponent);
    componentHolderService.registerComponent('shorten-url', ShortenUrlComponent);
    componentHolderService.registerComponent('jump-to-block-piece', JumpToBlockPieceComponent);
    componentHolderService.registerComponent('call-block-as-procedure-piece', CallBlockAsProcedureComponent);
    componentHolderService.registerComponent('switch-jump-to-block-piece', SwitchJumpToBlockPieceComponent);
    componentHolderService.registerComponent('concatenate-piece', ConcatenateComponent);
    componentHolderService.registerComponent('multiple-messages-piece', MultipleMessagesPieceComponent);
    componentHolderService.registerComponent('return-to-last-block-piece', ReturnToLastBlockPieceComponent);
    componentHolderService.registerComponent('close-case-piece', CloseCasePieceComponent);
    componentHolderService.registerComponent('update-profile-piece', UpdateProfilePieceComponent);
    componentHolderService.registerComponent('update-case-piece', UpdateCasePieceComponent);
    componentHolderService.registerComponent('reset-variables-piece', ResetVariablesPieceComponent);
    componentHolderService.registerComponent('store-message-piece', StoreMessagePieceComponent);
    componentHolderService.registerComponent('log-piece', LogPieceComponent);
    componentHolderService.registerComponent('post-message-piece', PostMessagePieceComponent);
    componentHolderService.registerComponent('tag-piece', TagPieceComponent);
    componentHolderService.registerComponent('payment-gateway-piece', PaymentGatewayPieceComponent);
    componentHolderService.registerComponent('coordinates-piece', CoordinatesPieceComponent);
    componentHolderService.registerComponent('multiple-coordinates-piece', MultipleCoordinatesPieceComponent);
    componentHolderService.registerComponent('nearest-coordinates-piece', NearestCoordinatesPieceComponent);
    componentHolderService.registerComponent('get-element-from-array-piece', GetElementFromArrayPieceComponent);
    componentHolderService.registerComponent('get-elements-from-array-piece', GetElementsFromArrayPieceComponent);
    componentHolderService.registerComponent('evaluate-commands-piece', EvaluateCommandsPieceComponent);
    componentHolderService.registerComponent('statistic-event-piece', StatisticEventPieceComponent);
    componentHolderService.registerComponent('calendar-piece', CalendarPieceComponent);
    componentHolderService.registerComponent('evaluate-cognitivity-piece', EvaluateCognitivityPieceComponent);
    componentHolderService.registerComponent('wa-menu-piece', WAMenuPieceComponent);
    componentHolderService.registerComponent('multiple-attachments-piece', MultipleAttachmentsPieceComponent);
    componentHolderService.registerComponent('db-query-piece', DbQueryPieceComponent);
    componentHolderService.registerComponent('signature-pad-piece', SignaturePadPieceComponent);
    componentHolderService.registerComponent('account-linking-piece', AccountLinkingPieceComponent);
    componentHolderService.registerComponent('account-unlinking-piece', AccountUnlinkingPieceComponent);
    componentHolderService.registerComponent('biometric-piece', BiometricPieceComponent);
    componentHolderService.registerComponent('sticker-piece', StickerPieceComponent);
    componentHolderService.registerComponent('send-hsm-piece', SendHsmPieceComponent);
    componentHolderService.registerComponent("smart-form-piece", SmartFormPieceComponent);
    componentHolderService.registerComponent("comment-piece", CommentPieceComponent);
    componentHolderService.registerComponent("authenticate-anonymous-profile-piece", AuthenticateAnonymousProfilePieceComponent);
    componentHolderService.registerComponent("knowledge-base-piece", KnowledgeBasePieceComponent);
    componentHolderService.registerComponent("get-message-entities-piece", GetMessageEntitiesPieceComponent);
    componentHolderService.registerComponent("invoke-whatsapp-flow-piece", InvokeWhatsappFlowPieceComponent);
    componentHolderService.registerComponent("encrypt-piece", EncryptPieceComponent);
    componentHolderService.registerComponent("decrypt-piece", DecryptPieceComponent);
    componentHolderService.registerComponent("multimedia-analysis-piece", MultimediaAnalysisPieceComponent);
    componentHolderService.registerComponent("profile-list-piece", ProfileListPieceComponent);
  }

  ngOnInit() {
    this.mCurrentModule = this.editorService.getCurrentModule();
    //console.log(this.mCurrentModule.isMaster(), this.editorState.SelectedBlock.SystemProtected)
    this.editorService.onBlockSelected.subscribe(() => {
      if (this.componentSelected()) {
        let flow = this.editorService.getCurrentFlow();
        this.isMasterBot = flow.isMasterBot;

        if (typeof (flow.ActiveProductionVersion) !== 'undefined' &&
          flow.ActiveProductionVersion !== null &&
          typeof (flow.ActiveProductionVersion.stats) !== 'undefined' &&
          flow.ActiveProductionVersion.stats !== null) {
          let stats = JSON.parse(flow.ActiveProductionVersion.stats);
          if (stats.blocksInfo !== null) {
            let blockStatInfo = stats.blocksInfo.find(b => b.id === this.editorState.SelectedBlock.Id);
            if (typeof (blockStatInfo) !== 'undefined' &&
              blockStatInfo !== null) {
              this.stats = blockStatInfo;
            }
          }
        }

        if (this.mCurrentModule !== undefined) {
          if (this.editorState.SelectedBlock !== null) {
            if(this.mCurrentModule.isMaster()) {
              this.readOnly = this.editorState.SelectedBlock.ModuleId !== this.mCurrentModule.id && !this.editorState.SelectedBlock.SystemProtected;
            }
            else {
              this.readOnly = this.editorState.SelectedBlock.ModuleId !== this.mCurrentModule.id;
            }
          } else if (!this.mCurrentModule.isMaster()) {
            this.readOnly = true;
          }
        }

        this.menuPieceActions();
      }
    });
    this.menuPieceActions();

    this.reloadWhatsappCatalogs();

    if (this.mCurrentModule !== undefined) {
      if (this.editorState.SelectedBlock !== null) {
        if(this.mCurrentModule.isMaster()) {
          this.readOnly = this.editorState.SelectedBlock.ModuleId !== this.mCurrentModule.id && !this.editorState.SelectedBlock.SystemProtected;
        }
        else {
          this.readOnly = this.editorState.SelectedBlock.ModuleId !== this.mCurrentModule.id;
        }
      } else if (!this.mCurrentModule.isMaster()) {
        this.readOnly = true;
      }
    }

  }

  menuPieceActions() {
    if (!this.readOnly) {
      this.showMenuPiece = true;
      if (this.mCurrentModule !== undefined) {
        if (this.editorService.getCurrentBlockGroup() !== null) {
          this.showMenuPiece = this.editorService.getCurrentBlockGroup().ModuleId === this.mCurrentModule.id;
        } else if (!this.mCurrentModule.isMaster()) {
          this.showMenuPiece = false;
        }
      }
    } else {
      this.showMenuPiece = false;
    }
  }

  reloadWhatsappCatalogs() {
    let currentFlow = this.editorService.getCurrentFlow();
    this.isMasterBot = currentFlow.isMasterBot;

    if (currentFlow.channel === ChannelTypes.WhatsApp) {
      this.loading = true;
      let settings = this.editorService.getYSocialSettings();
      if (settings.WhatsappUseInteractive &&
        settings.WhatsappUseInteractiveCatalog &&
        !this.editorService.getRetrievedWhatsappCatalogs()) {
        this.serverService.retrieveWhatsappCatalogs(settings.Url)
          .pipe(finalize(() => {
            this.loading = false;
          }))
          .subscribe((status: StatusResponse) => {
            this.editorService.setWhatsappCatalogs(status.data);
            this.loading = false;

            this.editorService.onWhatsappCatalogsLoaded.emit();
          },
            error => {
              this.loading = false;
              let errorDesc: any = {};
              if (error.status == 403) {
                errorDesc.Title = this.translateService.instant('CANNOT_RELOAD_WHATSAPP_CATALOG');
                errorDesc.Desc = error.error.code == 1002 ?
                    this.translateService.instant('CANNOT_RELOAD_WHATSAPP_CATALOG_DESC') :
                    this.translateService.instant('CANNOT_RELOAD_WHATSAPP_CATALOG_DESC');
              }
              this.modalService.init(ErrorPopupComponent, errorDesc, {});
            });
      }
    }
  }

  componentSelected() {
    return this.editorState.SelectedBlock != null;
  }

  movePieceDown(currentPos: number) {
    const elementList = this.editorState.SelectedBlock.Pieces;
    if (currentPos > -1 && currentPos - 1 < elementList.length) {
      arrayMove.mut(elementList, currentPos, currentPos + 1);
    }
  }

  movePieceUp(currentPos: number) {
    const elementList = this.editorState.SelectedBlock.Pieces;
    if (currentPos > 0) {
      arrayMove.mut(elementList, currentPos, currentPos - 1);
    }
  }

  onBlockNameChanged(eventData) {
    let newName = eventData.target.value;
    if (newName != null && newName.trim().length == 0) {
      newName = this.translateService.instant('BLOCK_NAME_NEW');
    }
    this.editorState.SelectedBlock.Name = this.editorService.getUniqueBlockName(newName);
  }

  displayConnections() {
    let outConections = this.editorService.getBlocksReferencedByBlock(this.editorState.SelectedBlock);
    let referencedBy = this.editorService.findBlocksReferencingBlock(this.editorState.SelectedBlock.Id);
    let referencedByCommands = this.editorService.findCommandsReferencingBlock(this.editorState.SelectedBlock.Id);
    let referencedByCognitivity = this.editorService.ExistsCognitivitiesReferencingBlock(this.editorState.SelectedBlock.Id)
    let isPersistentMenuReferencingBlock = this.editorService.isPersistentMenuReferencingBlock(this.editorState.SelectedBlock.Id);
    let isYSocialSettingsReferencingBlock = this.editorService.isYSocialSettingsReferencingBlock(this.editorState.SelectedBlock.Id);
    let isIceBreakersReferencingBlock = this.editorService.isIceBreakersReferencingBlock(this.editorState.SelectedBlock.Id);

    this.modalService.init(DisplayConnectionsComponent, {
      referencedBy: referencedBy,
      referencedBlocks: outConections,
      referencedByCommands: referencedByCommands,
      referencedByCognitivity: referencedByCognitivity,
      isPersistentMenuReferencingBlock: isPersistentMenuReferencingBlock,
      isIceBreakersReferencingBlock: isIceBreakersReferencingBlock,
      isYSocialSettingsReferencingBlock: isYSocialSettingsReferencingBlock,
      currentBlock: this.editorState.SelectedBlock
    }, {});
  }

  isEditingProductiveVersion(): boolean {
    return this.editorService.isEditingProductiveVersion();
  }

  getPieceStats(piece: BasePiece): any {
    if (this.stats === null ||
      typeof (this.stats.piecesInfo) === 'undefined' ||
      this.stats.piecesInfo === null) {
      return null;
    }

    let stats = this.stats.piecesInfo.find(p => p.id === piece.Uid);
    if (typeof (stats) === 'undefined') {
      return null;
    }
    return stats;
  }

  returnsMessage(): boolean {
    if (this.editorState.SelectedBlock === null) {
      return false;
    }

    let inspectedBlocks: string[] = [];
    return this.editorState.SelectedBlock.returnsMessage(this.editorService, inspectedBlocks);
  }

  addPieceAtIndex(index: number) {
    if (this.editorState.SelectedBlock === null) {
      return;
    }

    let emitAction = new EventEmitter<PieceType>();
    emitAction.subscribe((pieceType: PieceType) => {
      if (this.canAddPiece(pieceType, this.editorState.SelectedBlock, index)) {
        if (pieceType.PieceDefinitionType === 'interactive-message-productlist-piece' ||
          pieceType.PieceDefinitionType === 'interactive-message-product-piece') {
          this.reloadWhatsappCatalogs();
        }

        this.editorService.insertNewPiece(pieceType, index);
      }
    });

    this.modalService.init(PieceSelectorDialogComponent, {}, { PieceSelected: emitAction });
  }

  pieceSelected(pieceType: PieceType) {
    if (this.editorState.SelectedBlock === null) {
      return false;
    }

    if (this.canAddPiece(pieceType, this.editorState.SelectedBlock)) {
      if (pieceType.PieceDefinitionType === 'interactive-message-productlist-piece' ||
        pieceType.PieceDefinitionType === 'interactive-message-product-piece') {
        this.reloadWhatsappCatalogs();
      }

      this.editorService.addNewPiece(pieceType);
    }
  }

  public canAddPiece(pieceDefinition: PieceType, currentBlock: BlockDefinition, index: number = null): boolean {
    if (pieceDefinition.PieceDefinitionType === 'quick-reply-piece') {
      if (currentBlock.Pieces.length === 0) {
        this.modalService.init(ErrorPopupComponent, {
          Title: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_TITLE',
          Desc: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_INFO'
        }, {});
        return false;
      }

      if (index === 0) {
        this.modalService.init(ErrorPopupComponent, {
          Title: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_TITLE',
          Desc: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_INFO'
        }, {});
        return false;
      }

      let previousPiece: BasePiece;
      if (index === null) {
        previousPiece = currentBlock.Pieces[currentBlock.Pieces.length - 1];
      }
      else {
        previousPiece = currentBlock.Pieces[index - 1];
      }

      if (previousPiece.type !== 'message-piece' &&
        previousPiece.type !== 'gallery-piece' &&
        previousPiece.type !== 'dynamic-gallery-piece' &&
        previousPiece.type !== 'attachment-piece') {
        this.modalService.init(ErrorPopupComponent, {
          Title: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_TITLE',
          Desc: 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_INFO'
        }, {});
        return false;
      }

      if (index !== null) {
        let nextPiece = currentBlock.Pieces[index];
        if (nextPiece.type === 'quick-reply-piece') {
          this.modalService.init(ErrorPopupComponent, {
            Title: 'QUICKREPLY_ALREADYEXISTSATINDEX_TITLE',
            Desc: 'QUICKREPLY_ALREADYEXISTSATINDEX_INFO'
          }, {});
          return false;
        }
      }
    }
    else if (pieceDefinition.PieceDefinitionType === 'return-to-last-block-piece') {
      if (currentBlock.SystemProtected) {
        return false;
      }
    }

    return true;
  }

  changePublicStatus() {
    this.editorState.SelectedBlock.IsPublic = !this.editorState.SelectedBlock.IsPublic;
  }
}
