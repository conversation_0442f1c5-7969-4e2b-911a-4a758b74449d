<app-nav-bar
  [leftButtonLink]="homeUrl"
  [mainText]="'WELCOME' | translate"
  (onLogoutButtonClick)="logout()"
  [showLogoutButton]="true"
  [showSettingsButton]="false"
  [showTestButton]="false"
  [showConfigurationButton]="false"
  [showDashboardButton]="false"
  [showFilterButton]="true"
  [showLogsErrorButton]="false"
  [showGlobalStatisticButton]="false"
  [showAccessYSmart]="false"
  (onFilterButtonClick)="openFilter()">
</app-nav-bar>
<main role="main" class="container">
  <div class="container" *ngIf="flowsLoaded">
    <div class="tags">
      <div *ngIf="keywordfilterTag" class="tag label label-info keyword">
        <span>{{keywordfilterTag}}</span>
        <a (click)="removeFilterKeyword()"><span class="fa fa-times"></span></a>
      </div>
      <div *ngIf="channelFilterTag" class="tag label label-info {{ getChannelClass(channelFilterTag) }}">
        <span>{{getChannelType(channelFilterTag)}}</span>
        <a (click)="removeFilterChannel()"><span class="fa fa-times"></span></a>
      </div>
    </div>
    <h2>{{ 'MASTER_FLOWS' | translate }}</h2>
    <div class="tiles" *ngIf="masterFlows != null && masterFlows.length > 0">
      <app-dashboard-flow *ngFor="let flow of masterFlows" class="content"
                          [ngClass]="{ 'development' : !isProd }"
                          [model]="flow"
                          [showCloneButton]="false"
                          (onClone)="onClone($event)"
                          (onUpload)="onUpload($event, false)"
                          (onDownload)="onDownload($event, false)"
                          (onDownloadProd)="onDownload($event, true)"
                          (onPublish)="onPublish($event)"
                          (onDelete)="onDelete($event, false)">
      </app-dashboard-flow>
    </div>
    <div class="empty" *ngIf="!masterFlows || masterFlows.length == 0" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'NO_FLOWS' | translate }}
      </div>
    </div>
    <h2>{{ 'MODULE_FLOWS' | translate }}</h2>
    <div class="tiles">
      <app-dashboard-flow *ngFor="let flow of moduleFlows" class="content"
                          [ngClass]="{ 'development' : !isProd }"
                          [model]="flow"
                          [showCloneButton]="false"
                          (onClone)="onClone($event)"
                          (onUpload)="onUpload($event, false)"
                          (onDownload)="onDownload($event, false)"
                          (onDownloadProd)="onDownload($event, true)"
                          (onPublish)="onPublish($event)"
                          (onDelete)="onDelete($event, false)">
      </app-dashboard-flow>
    </div>
    <div class="empty" *ngIf="!moduleFlows || moduleFlows.length == 0" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'NO_FLOWS' | translate }}
      </div>
    </div>
    <h2>{{ 'READONLY_MODULE_FLOWS' | translate }}</h2>
    <div class="tiles">
      <app-dashboard-flow *ngFor="let flow of readOnlyModuleFlows" class="content"
                          [ngClass]="{ 'development' : !isProd }"
                          [model]="flow">
      </app-dashboard-flow>
    </div>
    <div class="empty" *ngIf="!readOnlyModuleFlows || readOnlyModuleFlows.length == 0" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'NO_FLOWS' | translate }}
      </div>
    </div>
  </div>
  <input type="file" hidden #fileField accept=".json">
</main>
<div class="overlay" *ngIf="loading">
    <action-spinner class="spinner"></action-spinner>
</div>
