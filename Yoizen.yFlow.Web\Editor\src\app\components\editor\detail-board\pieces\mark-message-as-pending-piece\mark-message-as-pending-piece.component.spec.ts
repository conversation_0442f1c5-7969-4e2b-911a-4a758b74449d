import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MarkMessageAsPendingPieceComponent } from './mark-message-as-pending-piece.component';

describe('MarkMessageAsPendingPieceComponent', () => {
  let component: MarkMessageAsPendingPieceComponent;
  let fixture: ComponentFixture<MarkMessageAsPendingPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MarkMessageAsPendingPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MarkMessageAsPendingPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
