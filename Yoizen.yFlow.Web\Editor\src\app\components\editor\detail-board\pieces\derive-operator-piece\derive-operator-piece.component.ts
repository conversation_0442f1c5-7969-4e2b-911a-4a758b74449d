import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { DeriveOperatorPiece } from '../../../../../models/pieces/DeriveOperatorPiece';
import { ChannelTypes } from "../../../../../models/ChannelType";
import { CognitivityProjectType } from 'src/app/models/cognitivity/CognitivityProject';

@Component({
  selector: 'app-derive-operator-piece',
  templateUrl: './derive-operator-piece.component.html',
  styleUrls: ['./derive-operator-piece.component.scss']
})
export class DeriveOperatorPieceComponent extends BasePieceVM implements OnInit {
  model: DeriveOperator<PERSON>iece;
  showRequiresVoiceCall: boolean = false;
  showSummaryOption: boolean = false;


  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);

    this.flow = this.editorService.getCurrentFlow();

    /* TODO: Cambiar cuando se implemente el summary en Social
    if (this.editorService.isCognitivityEnabled())
    {
      if (thi.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.RASA &&
          this.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.WITAI &&
          this.editorService.getCurrentCognitivityProject().type !== CognitivityProjectType.WATSON)
      {
        this.showSummaryOption = true
      }
    }
    */

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      this.showRequiresVoiceCall = true;
    }
  }

  ngOnInit() {
    this.model = this.context as DeriveOperatorPiece;
  }
}
