import { TranslateService } from '@ngx-translate/core';
import { ModalService } from './../../../../../services/Tools/ModalService';
import { EditorService } from 'src/app/services/editor.service';
import { VariableDefinition } from './../../../../../models/VariableDefinition';
import { TypeDefinition } from './../../../../../models/TypeDefinition';
import { MultipleAttachmentPiece } from './../../../../../models/pieces/MultipleAttachmentPiece';
import { BasePieceVM } from './../BasePieceVM';
import { Component, OnInit } from '@angular/core';
import { SourceTypes } from 'src/app/models/pieces/AttachmentPiece';
import { isStringValid } from '../../../../../urlutils.module';
import {ChannelTypes} from "../../../../../models/ChannelType";

@Component({
  selector: 'app-multiple-attachments-piece',
  templateUrl: './multiple-attachments-piece.component.html',
  styleUrls: ['./multiple-attachments-piece.component.scss']
})
export class MultipleAttachmentsPieceComponent extends BasePieceVM implements OnInit {
  model: MultipleAttachmentPiece;
  variableFilter = [TypeDefinition.Array];
  outputVariableFilter = [TypeDefinition.Text];
  ActiveIdString: string;
  urlMode: boolean;

  get customVariables(): VariableDefinition[] {
    return MultipleAttachmentPiece.SpecialVar;
  }

  get isWhatsappChannel(): boolean {
    return this.model.Channel === ChannelTypes.WhatsApp;
  }

  get isTwitterFlow(): boolean {
    return this.model.Channel == ChannelTypes.Twitter;
  }

  get InputVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.SourceVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  constructor(editorService: EditorService, public modalService: ModalService, private translate: TranslateService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as MultipleAttachmentPiece;
    this.ActiveIdString = this.getActiveTab();
    this.urlMode = this.ActiveIdString === "tab-variable";
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  get source(): string {
    switch (this.model.Source) {
      case SourceTypes.Url:
        return "URL";
      case SourceTypes.Variable:
        return this.translate.instant('VARIABLE');
    }
  }

  onTabChange() {
    let activeTab = this.getActiveTab();
    switch (activeTab) {
      case "tab-url":
        this.model.Source = SourceTypes.Variable;
        return;
      case "tab-variable":
        this.model.Source = SourceTypes.Url;
        return;
    }
  }

  setInputVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
        variable !== null) {
      this.model.SourceVariableId = variable.Id;
    } else {
      this.model.SourceVariableId = null;
    }
  }

  validateVariable(value: string) {
    return false;
  }

  validateTextArea(value: string) {
    return this.model.hasTextToAsign();
  }

  getValidator() {
    return this.validateTextArea.bind(this);
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }
}
