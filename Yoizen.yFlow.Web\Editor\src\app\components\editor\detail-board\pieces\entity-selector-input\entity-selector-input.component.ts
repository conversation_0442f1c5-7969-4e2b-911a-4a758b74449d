import { ServerService } from 'src/app/services/server.service';
import { EditorService } from 'src/app/services/editor.service';
import { Entity } from './../../../../../models/cognitivity/Entity';
import { Component, OnInit, Input, ViewChild, ElementRef, Renderer2, Output, EventEmitter } from '@angular/core';
import { normalizeString } from 'src/app/urlutils.module';

@Component({
  selector: 'app-entity-selector-input',
  templateUrl: './entity-selector-input.component.html',
  styleUrls: ['./entity-selector-input.component.scss']
})
export class EntitySelectorInputComponent implements OnInit {
  @Input() readOnly : boolean = false;
  @Input() entity: Entity;
  @Output() setEntity = new EventEmitter<Entity>();
  availableEntities: Entity[] = [];
  currentEntities: Entity[] = [];
  searchEntityString: String;
  lastSearchEntityString: String;

  @ViewChild('entityPicker', { static: false }) EntityPicker : ElementRef;

  constructor(private renderer: Renderer2, public editorService: EditorService, public serverService: ServerService) { }

  get Entities() : Entity[] {
    if (this.searchEntityString !== this.lastSearchEntityString || this.currentEntities === null) {
      this.currentEntities = this.updateEntities();
      this.lastSearchEntityString = this.searchEntityString;
    }
    return this.currentEntities;
  }

  get EmptyEntitySet() : boolean {
    return this.currentEntities.length === 0;
  }

  updateEntities() : Entity[] {
    let entities = [];

    const lowerEntityname = normalizeString(this.searchEntityString.toLowerCase());
    this.availableEntities.forEach( entity => {
      if (normalizeString(entity.name.toLowerCase()).includes(lowerEntityname)) {
        entities.push(entity);
      }
    });
    return entities;
  }

  ngOnInit() {
    this.availableEntities = this.editorService.getEntities();
    this.currentEntities = this.availableEntities;
  }

  selectEntity(entityData: Entity) {
    this.entity = entityData;
    this.searchEntityString = entityData.name;
    this.setEntity.emit(entityData);
  }

  deleteEntity() {
    this.entity = null;
    this.searchEntityString = "";
    this.setEntity.emit(null);
  }

  onInputFocusIn() {
    this.renderer.removeClass(this.EntityPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {
    setTimeout(() => {
      this.renderer.addClass(this.EntityPicker.nativeElement, 'hide');
    }, 500);
  }

  isValid() {
    return this.entity !== null;
  }
}
