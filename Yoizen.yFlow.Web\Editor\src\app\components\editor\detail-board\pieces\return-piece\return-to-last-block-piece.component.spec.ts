import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnToLastBlockPieceComponent } from './return-to-last-block-piece.component';

describe('ReturnToLastBlockPieceConponent', () => {
  let component: ReturnToLastBlockPieceComponent;
  let fixture: ComponentFixture<ReturnToLastBlockPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnToLastBlockPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnToLastBlockPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
