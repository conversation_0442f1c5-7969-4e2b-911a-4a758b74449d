import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}

export class HistoryDailyByGroupsSequence extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare total: number;
    declare sourceGroupId: string;
    declare destGroupId: string;
    declare typeSequence: number;

    init(datetime: Moment, data) {
        this.initBase(datetime);

        this.channel = data.channel;
        this.flowId = data.flowId;
        this.sourceGroupId = data.sourceGroupId;
        this.destGroupId = data.destGroupId;
        this.version = data.version;
        this.total = data.total;
        this.typeSequence = data.type;
    }

    type() {
        return HistoryDailyInfoTypes.GroupSequence;
    }
}

HistoryDailyByGroupsSequence.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    sourceGroupId: {
        type: DataTypes.INTEGER,
        field: 'source_group_id',
    },
    destGroupId: {
        type: DataTypes.INTEGER,
        field: 'dest_group_id',
    },
    typeSequence: {
        type: DataTypes.INTEGER,
        field: 'type',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_groups_sequence',
    tableName: 'history_daily_by_groups_sequence',
    timestamps: false
})