﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<log4net>
		<root>
			<level value="ALL" />
			<appender-ref ref="LogFileAppender" />
		</root>
		<appender name="LogFileAppender" type="log4net.Appender.RollingFileAppender">
			<file type="log4net.Util.PatternString" value="../../logs/yFlowUtils.txt" />
			<maxSizeRollBackups value="50" />
			<appendToFile value="true"/>
			<rollingStyle value="Composite" />
			<datePattern value="yyyyMMdd" />
			<maximumFileSize value="10MB" />
			<staticLogFileName value="false" />
			<preserveLogFileNameExtension value="true" />
			<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
			<layout type="log4net.Layout.PatternLayout">
				<!-- %M show the method where the logging occured, but it's better to avoid it for performance considerations -->
				<conversionPattern value="[%d{HH:mm:ss}] %level %logger - %message%n" />
			</layout>
		</appender>
	</log4net>
</configuration>