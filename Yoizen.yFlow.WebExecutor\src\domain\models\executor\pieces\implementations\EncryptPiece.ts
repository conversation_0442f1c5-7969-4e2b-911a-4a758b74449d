import { Base<PERSON>iece, BasePieceExecutor } from "../BasePiece";
import { Control } from "../../Control";
import { FlowExecutor } from "../../../../../application/executors/FlowExecutorBase";
import CipherExecutorClass from "../../../../../application/utils/CipherExecutor";
import { TextUtils } from "../../../../../application/utils/textUtils";
import { logger } from "../../../../../../../Yoizen.yFlow.Helpers/src/Logger";

const CipherExecutor = CipherExecutorClass.Class;

export class EncryptPiece extends BasePiece {
    EncryptKey: string;
    VariableToEncryptId: string;
    VariableEncryptedId: string;
    EncryptType: number;
    EncryptModeType: number;
    EncryptPaddingType: number;
    ErrorMessage: string;
    ErrorBlockId: string;
    // New properties for custom method support
    CustomMethod: boolean;
    Delimiter: string;
    IvBytesLength: number;
    ReplaceCharacters: boolean;
    CharactersToReplace: Array<{ value: string; replaceWith: string }>;
}

export class EncryptPieceExecutor extends BasePieceExecutor {
    async execute(piece: EncryptPiece, control: Control, executor: FlowExecutor, definition: any, jumpedToAnotherBlock: { value: boolean }, block: any, pieceIndex: number): Promise<void> {
        let encryptKey = TextUtils.replaceVariablesInText(piece.EncryptKey, control);

        let variableToEncryptDefinition = definition.VariableList.find(variable => variable.Id === piece.VariableToEncryptId);
        let variableToEncrypt = null;
        if (variableToEncryptDefinition && variableToEncryptDefinition.Name in control.runtimeVariables) {
            variableToEncrypt = control.runtimeVariables[variableToEncryptDefinition.Name];
        }

        let variableEncryptedDefinition = definition.VariableList.find(variable => variable.Id === piece.VariableEncryptedId);

        if (variableEncryptedDefinition) {
            try {
                let cipher;
                
                // Set default values for custom method parameters if not provided
                const customMethod = piece.CustomMethod || false;
                const delimiter = piece.Delimiter || null;
                const ivBytesLength = piece.IvBytesLength || null;
                const replaceCharacters = piece.ReplaceCharacters || false;
                const charactersToReplace = piece.CharactersToReplace || [];

                //@ts-ignore
                if (piece.EncryptType === 'rabbit' || piece.EncryptType === 2) {
                    // Rabbit doesn't require mode or padding, but still needs all constructor parameters
                    cipher = new CipherExecutor(
                        piece.EncryptType, 
                        null, 
                        null, 
                        encryptKey,
                        customMethod,
                        delimiter,
                        ivBytesLength,
                        replaceCharacters,
                        charactersToReplace
                    );
                } else {
                    // AES and TripleDES require all parameters
                    cipher = new CipherExecutor(
                        piece.EncryptType, 
                        piece.EncryptModeType, 
                        piece.EncryptPaddingType, 
                        encryptKey,
                        customMethod,
                        delimiter,
                        ivBytesLength,
                        replaceCharacters,
                        charactersToReplace
                    );
                }

                if (typeof variableToEncrypt !== 'string') {
                    variableToEncrypt = JSON.stringify(variableToEncrypt);
                }

                let valueToEncrypt = variableToEncrypt;
                if (typeof valueToEncrypt === 'object') {
                    logger.info({ caseId: control.body.case.id, messageId: control.body.message.id, userId: control.body.user.id },
                        `[${control.body.message.id}] Se procesa el valor a encriptar con stringify}`);
                    valueToEncrypt = JSON.stringify(valueToEncrypt);
                }

                let encryptedValue = cipher.encrypt(valueToEncrypt);
                control.runtimeVariables[variableEncryptedDefinition.Name] = encryptedValue;

            } catch (error) {
                logger.info({ caseId: control.body.case.id, messageId: control.body.message.id, userId: control.body.user.id },
                    `[${control.body.message.id}] Error al encriptar los datos: ${error}`);

                let errorMessage = piece.ErrorMessage
                    ? TextUtils.replaceVariablesInText(piece.ErrorMessage, control)
                    : 'Error al encriptar los datos.';

                let pieceMessage = executor.createTextMessage(errorMessage, control);
                if (pieceMessage !== null) {
                    if (!Array.isArray(control.messages)) {
                        control.messages = [];
                    }
                    control.messages.push(pieceMessage);
                    control.conversationMessagesToSave.push(errorMessage);
                }

                if (piece.ErrorBlockId && piece.ErrorBlockId !== '-1') {
                    control.stoppedOnPiece = null;
                    control.inputPieceInfo = null;
                    control.startOnPiece = null;

                    let errorBlock = definition.findBlockById(piece.ErrorBlockId);
                    if (errorBlock) {
                        await executor.execute(definition, errorBlock, control);
                        return;
                    } else {
                        logger.info({ caseId: control.body.case.id, messageId: control.body.message.id, userId: control.body.user.id },
                            `[${control.body.message.id}] El bloque de error con ID ${piece.ErrorBlockId} no existe.`);
                        throw error;
                    }
                } else {
                    throw error;
                }
            }
        }
    }
}

