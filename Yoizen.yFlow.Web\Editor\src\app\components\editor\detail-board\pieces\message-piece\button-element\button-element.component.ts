import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { ButtonPiece, ButtonType } from '../../../../../../models/pieces/ButtonPiece';
import {TranslateService} from "@ngx-translate/core";
import {EditorService} from "../../../../../../services/editor.service";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";

@Component({
  selector: 'app-button-element',
  templateUrl: './button-element.component.html',
  styleUrls: ['./button-element.component.scss']
})
export class ButtonElementComponent implements OnInit {
  buttonTypes = ButtonType;
  @ViewChild('buttonDetail', {read: ElementRef, static:false}) ButtonDetail : ElementRef;
  @Input() Model : ButtonPiece;
  @Input() Index : number;
  @Input() stats : any = null;
  @Input() showStats : boolean = true;
  @Input() expandedBtn : ButtonPiece;
  @Input() readOnly: boolean = false;
  @Input() AllowedButtonTypes : ButtonType[] = null;
  @Input() IsIceBreaker : boolean = false;
  @Output() onDelete : EventEmitter<number> = new EventEmitter<number>();
  @Output() onShowDetail : EventEmitter<ButtonPiece> = new EventEmitter<ButtonPiece>();
  @Input() customVariableList : Array<VariableDefinition> = null;
  @Input() EmojisAllowed : boolean = true;
  @Input() MaxLength : number = 524288;

  public get HideDetail(): boolean {
    return this.expandedBtn != this.Model;
  }

  constructor(private translateService: TranslateService, private editorService: EditorService) {

  }

  ngOnInit() {

  }

  isValid() : boolean {
    return this.Model.isValid(this.editorService);
  }

  onClick() {
    this.onShowDetail.emit(this.Model);
  }

  deleteElement() {
  	this.onDelete.emit(this.Index);
  }

  closePopup() {
    this.onShowDetail.emit(null);
  }

  getButtonText() : string {
    if (typeof(this.Model.Name) !== 'undefined' &&
      this.Model.Name !== null) {
      return this.parseText(this.Model.Name);
    }

    return this.translateService.instant('BUTTON_MISSING_NAME');
  }

  parseText(text: string) : string {
    if (typeof(text) === 'undefined' || text === null) {
      return '';
    }

    const regex = /\{\{[a-zA-Z][a-zA-Z0-9_]{2,}\}\}/gm;

    var scriptRegex = new RegExp(/\${.+?}\$/g);

    let processedText = text.replace(regex, function (match) {
      return '<span class="variable">' + match + '</span>';
    });

    processedText = processedText.replace(scriptRegex, function (match) {
      return '<span class="script">' + match + '</span>';
    });

    return processedText;
  }

  withName() : boolean {
    if (typeof(this.Model.Name) !== 'undefined' &&
      this.Model.Name !== null &&
      this.Model.Name.length > 0) {
      return true;
    }

    return false;
  }

  withRedirect() : boolean {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);
    if (this.Model.Type === ButtonType.Redirect &&
      this.Model.BlockID !== null &&
      this.Model.BlockID !== "-1" &&
      block != null &&
      block.validateCurrentModuleBlock(this.editorService)) {
      return true;
    }

    return false;
  }

  withLocation() : boolean {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);
    if (this.Model.Type === ButtonType.Location &&
      this.Model.BlockID !== null &&
      this.Model.BlockID !== "-1" &&
      block != null &&
      block.validateCurrentModuleBlock(this.editorService)) {
      return true;
    }

    return false;
  }

  withLocationWithMap() : boolean {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);

    if (this.Model.Type === ButtonType.LocationWithMap &&
      this.Model.BlockID !== null &&
      this.Model.BlockID !== "-1" &&
      block != null &&
      block.validateCurrentModuleBlock(this.editorService)) {
      return true;
    }

    return false;
  }

  getBlockName() : string {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);
    if (typeof(block) !== 'undefined' &&
      block !== null &&
      block.validateCurrentModuleBlock(this.editorService)) {
      return block.Name;
    }
    return '';
  }

  withUrl() : boolean {
    if (this.Model.Type === ButtonType.Url &&
      typeof(this.Model.Url) !== 'undefined' &&
      this.Model.Url !== null &&
      this.Model.Url.length > 0) {
      return true;
    }

    return false;
  }

  withDial() : boolean {
    if (this.Model.Type === ButtonType.Dial &&
      typeof(this.Model.PhoneNumber) !== 'undefined' &&
      this.Model.PhoneNumber !== null &&
      this.Model.PhoneNumber.length > 0) {
      return true;
    }

    return false;
  }

  withSendLocation() : boolean {
    if (this.Model.Type === ButtonType.SendLocation &&
      typeof(this.Model.Latitude) !== 'undefined' &&
      this.Model.Latitude !== null &&
      this.Model.Latitude.length > 0 &&
      typeof(this.Model.Longitude) !== 'undefined' &&
      this.Model.Longitude !== null &&
      this.Model.Longitude.length > 0) {
      return true;
    }

    return false;
  }

  withAuthLoginUrl() : boolean {
    if (this.Model.Type === ButtonType.Auth &&
      typeof(this.Model.AuthLoginUrl) !== 'undefined' &&
      this.Model.AuthLoginUrl !== null &&
      this.Model.AuthLoginUrl.length > 0) {
      return true;
    }

    return false;
  }

  goToBlock() {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);
    if (typeof(block) !== 'undefined' &&
      block !== null &&
      block.validateCurrentModuleBlock(this.editorService)) {
      this.editorService.selectedBlock(block);
    }
  }
}
