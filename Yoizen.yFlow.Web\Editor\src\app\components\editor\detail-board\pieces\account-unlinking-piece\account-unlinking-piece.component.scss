@import '_variables';
@import '_mixins';

.account-linking {
	background-color: #fff;
	padding: 10px;
	width: 500px;

  .next, .name {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input ,.input-variable-area {
      flex-grow: 1;
    }
	}

  .next {
    margin-top: 10px;
  }

  .bussiness {
    margin-top: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    .bussiness-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .bussiness-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .bussiness-value {
          width: 150px;
        }

        .bussiness-variable {
          width: 200px;
        }

        .trash {
          width: 30px;

          & > div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }

    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }
}
