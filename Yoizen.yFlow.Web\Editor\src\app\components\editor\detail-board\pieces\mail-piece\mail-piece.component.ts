import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {MailAttachment, MailAttachmentSources, MailPiece} from '../../../../../models/pieces/MailPiece';
import { isMailAddressValid, isStringValid } from '../../../../../urlutils.module'
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";

@Component({
  selector: 'app-mail-piece',
  templateUrl: './mail-piece.component.html',
  styleUrls: ['./mail-piece.component.scss']
})
export class MailPieceComponent extends BasePieceVM implements OnInit {
  model : MailPiece;
  mailAttachmentSources = MailAttachmentSources;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

  ngOnInit() {
    this.model = this.context as MailPiece;
  }

  getMessageValidator() {
    return this.isMesageValid.bind(this)
  }

  isMesageValid(str) {
    return this.model.isMessageValid();
  }

  getSubjectValidator() {
    return str => { return this.model.isSubjectValid(); };
  }

  getVariableDefinition(attachment: MailAttachment): VariableDefinition {
    return this.editorService.getVariableWithId(attachment.VariableId);
  }

  setVariable(attachment: MailAttachment, variable: VariableDefinition) {
    if (variable != null) {
      attachment.VariableId = variable.Id;
      attachment.VariableName = variable.Name;
    }
    else {
      attachment.VariableId = -1;
      attachment.VariableName = null;
    }
  }

  addNewAttachment() {
    this.model.Attachments.push(new MailAttachment());
  }

  deleteAttachment(index: number) {
    this.model.Attachments.splice(index, 1);
  }
}
