<div class="received-message">
  <div class="title">{{ 'APPLE_INTERACTIVE_RECEIVEDMESSAGE_TITLE' | translate }}</div>
  <div class="options">
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_TITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_TITLE' | translate"
        [(value)]="receivedMessage.title"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="receivedMessage.isTitleValid.bind(receivedMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_SUBTITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_SUBTITLE' | translate"
        [(value)]="receivedMessage.subtitle"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="receivedMessage.isSubtitleValid.bind(receivedMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_STYLE' | translate}}:</span>
      <select [(ngModel)]="receivedMessage.style" [disabled]="readOnly" class="select">
        <option [value]="appleInteractiveStyles.icon">{{'APPLE_INTERACTIVE_STYLE_ICON' | translate}}</option>
        <option [value]="appleInteractiveStyles.small">{{'APPLE_INTERACTIVE_STYLE_SMALL' | translate}}</option>
        <option [value]="appleInteractiveStyles.large">{{'APPLE_INTERACTIVE_STYLE_LARGE' | translate}}</option>
      </select>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_IMAGE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_IMAGE' | translate"
        [(value)]="receivedMessage.image"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="receivedMessage.isImageValid.bind(receivedMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
  </div>
</div>
<div class="reply-message">
  <div class="title">{{ 'APPLE_INTERACTIVE_REPLYMESSAGE_TITLE' | translate }}</div>
  <div class="options">
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_TITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_TITLE' | translate"
        [(value)]="replyMessage.title"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="replyMessage.isTitleValid.bind(replyMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_SUBTITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_SUBTITLE' | translate"
        [(value)]="replyMessage.subtitle"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="replyMessage.isSubtitleValid.bind(replyMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_STYLE' | translate}}:</span>
      <select [(ngModel)]="replyMessage.style" [disabled]="readOnly" class="select">
        <option [value]="appleInteractiveStyles.icon">{{'APPLE_INTERACTIVE_STYLE_ICON' | translate}}</option>
        <option [value]="appleInteractiveStyles.small">{{'APPLE_INTERACTIVE_STYLE_SMALL' | translate}}</option>
        <option [value]="appleInteractiveStyles.large">{{'APPLE_INTERACTIVE_STYLE_LARGE' | translate}}</option>
      </select>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERACTIVE_IMAGE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERACTIVE_IMAGE' | translate"
        [(value)]="replyMessage.image"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="replyMessage.isImageValid.bind(replyMessage)"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
  </div>
</div>
