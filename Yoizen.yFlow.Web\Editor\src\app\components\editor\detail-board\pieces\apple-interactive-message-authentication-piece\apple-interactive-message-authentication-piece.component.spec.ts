import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AppleInteractiveMessageAuthenticationPieceComponent } from './apple-interactive-message-authentication-piece.component';

describe('AppleInteractiveMessageAuthenticationPieceComponent', () => {
  let component: AppleInteractiveMessageAuthenticationPieceComponent;
  let fixture: ComponentFixture<AppleInteractiveMessageAuthenticationPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AppleInteractiveMessageAuthenticationPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppleInteractiveMessageAuthenticationPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
