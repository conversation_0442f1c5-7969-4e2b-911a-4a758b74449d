@import "_variables";
@import "_mixins";

.comment {
  min-width: 300px;
  width: 100% !important;
  background: #fff;
  position: relative;

  .fa-comment {
    color: black;
  }

  .end {
    float: right;
    .fa {
      cursor: pointer;
      margin: 2px 6px;
      font-weight: 100;
    }
  }
  
  .card-title {
    margin-bottom: 0px;
    width: 100%;
  }
  
  .content {
    overflow: hidden;
    .text {
      width: 100%;
      textarea {
          width: 100%;
          min-height: 200px;
          background: transparent;
          white-space: pre-wrap;
          height: 33px;
          font-weight: normal;
          border-radius: 10px;
          border: 1px solid #9a9a9a;
          padding-left: 10px;
      }
    }
    
  }
  
}
