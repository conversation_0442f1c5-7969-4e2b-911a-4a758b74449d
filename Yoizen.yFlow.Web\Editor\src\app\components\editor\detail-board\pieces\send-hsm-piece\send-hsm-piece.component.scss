@import "_variables";
@import "_mixins";

.send-hsm {
  background-color: #fff;
  width: 600px;

  .description {
    .select {
      width: 100%;
    }
  }

  .option {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .header, .footer, .buttons-area{
    min-width: 450px;
    margin-top: 10px;

    .attachment, .buttons {
      .row {
        display: flex;
        margin: 1rem;
        align-items: center;

        p {
          margin: auto 0;
        }

        span {
          font-weight: 900;
          margin-right: 4px;
          display: flex;

          p {
            font-weight: 500 !important;
            margin-left: 4px;
          }
        }
      }

      .definition {
        padding: 10px;

        .name, .mimetype, .url, .publicurl {
          display: flex;
          flex-direction: row;
          margin-top: 10px;
          width: 100%;

          .title {
            font-family: $fontFamilyTitles;
            font-weight: bold;
            margin-right: 10px;
            justify-self: center;
            align-self: center;
            text-align: center;
            flex-grow: 0;
            flex-shrink: 0;
          }

          .input-variable-area {
            flex-grow: 1;
          }
        }

        .url, .name {
          margin-top: 0;
        }
      }

      .sub-title {
        font-weight: bold;
      }
    }

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    textarea {
      min-width: 450px;
      width: 100%;
      height: 50px;
      resize: none;
    }
  }

  .card {
    position: relative;
    font-weight: normal;
    background-color: #ffffff;
    border-color: transparent;
    border-radius: 7px;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.08), 0 2px 0 0 rgba(0, 0, 0, 0.12);
    padding: 10px;
    margin-bottom: 5px;
    margin-top: 5px;
  }

  .example-card {
    width: 60%;
    position: relative;
    font-weight: normal;
    background-color: #ffffff;
    border-color: transparent;
    border-radius: 7px;
    box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.08), 0 2px 0 0 rgba(0, 0, 0, 0.12);
    padding: 10px;
    margin-bottom: 5px;
    margin-top: 5px;

    .card-title {
      font-family: $fontFamilyTitles;
      font-size: 1.4em;
      font-weight: bold;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    .card-info {
      font-style: italic;
      color: #aaaaaa;
      white-space: pre-wrap;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    .card-footer{
      font-style: italic;
      color: #aaaaaa;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    .card-buttons{
      font-family: $fontFamilyCondensed;
      color: $hsmButtons;
      font-weight: bold;

      .quick-reply, .call-to-action{
        display: flex;
        flex-direction: column;

        .row {
          display: flex;
          margin: 0.5rem;
          justify-content: center;
        }
      }
    }
  }

  .description {
    min-width: 450px;
    margin-top: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    textarea {
      min-width: 450px;
      width: 100%;
      height: 100px;
      resize: none;
    }
  }

  .example {
    min-width: 450px;
    margin-top: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }
  }

  .parameters {
    margin-top: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    .parameters-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .parameters-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .parameters-key {
          display: flex;
          align-items: center;
          height: 40px;
          p {
            margin-top: 0!important;
            margin-bottom: 0!important;
            margin-left: 4px;
            //align-items: center;
          }
        }

        .parameters-value {
          width: 200px;
        }

        .parameters-variable {
          width: 200px;
        }

        .trash {
          width: 30px;

          & > div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }
    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }
}
