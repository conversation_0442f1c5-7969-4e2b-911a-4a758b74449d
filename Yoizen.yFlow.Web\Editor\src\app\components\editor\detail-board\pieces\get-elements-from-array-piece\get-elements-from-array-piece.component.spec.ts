import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { GetElementsFromArrayPieceComponent } from './get-elements-from-array-piece.component';

describe('GetElementsFromArrayPieceComponent', () => {
  let component: GetElementsFromArrayPieceComponent;
  let fixture: ComponentFixture<GetElementsFromArrayPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ GetElementsFromArrayPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GetElementsFromArrayPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
