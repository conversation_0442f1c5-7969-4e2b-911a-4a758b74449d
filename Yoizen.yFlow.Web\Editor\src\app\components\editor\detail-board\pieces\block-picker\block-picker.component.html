<div class="app-block-picker">
  <div class="next block-picker">
    <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
      <input class="input" [ngClass]="{'invalid-state': isInvalid}" type="text"
             autocomplete="nomecompletesnadaaca"
             placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}" [(ngModel)]="searchBlockString" LimitLength
             (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
      <div #blockPicker class="block-selection-anchor" *ngIf="showBlockPicker">
        <app-block-selector class="select-block" [BlockName]=searchBlockString
                            (onSelectBlock)="onBlockSelect($event)"
                            [ShowVisibleBlocks]="ShowVisibleBlocks"></app-block-selector>
      </div>
    </div>
    <div class="block-selected" [ngClass]="{'hide': !hasBlock(), 'invalid-state': isInvalid}"
         (dblclick)="goToBlock()" data-toggle="tooltip" ngbTooltip="{{ 'GO_TO_BLOCK_DBLCLICK' | translate }}"
         [placement]="tooltipPlacement" container="body" [tooltipClass]="tooltipClass" [disableTooltip]="readOnly">
      <div class="block-display">{{blockData?.Name}}</div>
      <div class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
           placement="top" container="body" tooltipClass="tooltip-trash"></div>
    </div>
  </div>
</div>
