<div class="options">
  <div class="title">{{ 'FORM_PAGE_TYPE_INPUT_OPTIONS' | translate }}</div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_REGEX' | translate}}:</span>
    <input type="text"
           [(ngModel)]="option.regex"
           [ngClass]="{'invalid-input': !option.isRegexValid() }"
           [disabled]="readOnly"
           class="input" spellcheck="false" autocomplete="off" />
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_HINTTEXT' | translate}}:</span>
    <app-input-with-variables
      [(value)]="option.hintText"
      [validator]="option.isHintTextValid.bind(option)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_PLACEHOLDER' | translate}}:</span>
    <app-input-with-variables
      [(value)]="option.placeholder"
      [validator]="option.isPlaceholderValid.bind(option)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_REQUIRED' | translate}}:</span>
    <ui-switch [(ngModel)]="option.required" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_INPUTTYPE' | translate}}:</span>
    <select [(ngModel)]="option.inputType" [disabled]="readOnly" class="select">
      <option [value]="inputTypes.singleline">{{'FORM_PAGE_TYPE_INPUT_OPTION_INPUTTYPE_SINGLELINE' | translate}}</option>
      <option [value]="inputTypes.multiline">{{'FORM_PAGE_TYPE_INPUT_OPTION_INPUTTYPE_MULTILINE' | translate}}</option>
    </select>
  </div>
  <div class="option" *ngIf="option.inputType === inputTypes.singleline">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_LABELTEXT' | translate}}:</span>
    <app-input-with-variables
      [(value)]="option.labelText"
      [validator]="option.isLabelTextValid.bind(option)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option" *ngIf="option.inputType === inputTypes.singleline">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_PREFIXTEXT' | translate}}:</span>
    <app-input-with-variables
      [(value)]="option.prefixText"
      [validator]="option.isPrefixTextValid.bind(option)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_MAXIMUM' | translate}}:</span>
    <input type="number"
      [(ngModel)]="option.maximumCharacterCount"
      [disabled]="readOnly"
      class="input" />
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE' | translate}}:</span>
    <select [(ngModel)]="option.keyboardType" [disabled]="readOnly" class="select">
      <option [value]="keyboardTypes.default">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_DEFAULT' | translate}}</option>
      <option [value]="keyboardTypes.asciiCapable">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_ASCIICAPABLE' | translate}}</option>
      <option [value]="keyboardTypes.numbersAndPunctuation">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_NUMBERSANDPUNCTUATION' | translate}}</option>
      <option [value]="keyboardTypes.URL">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_URL' | translate}}</option>
      <option [value]="keyboardTypes.numberPad">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_NUMBERPAD' | translate}}</option>
      <option [value]="keyboardTypes.phonePad">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_PHONEPAD' | translate}}</option>
      <option [value]="keyboardTypes.namePhonePad">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_NAMEPHONEPAD' | translate}}</option>
      <option [value]="keyboardTypes.emailAddress">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_EMAILADDRESS' | translate}}</option>
      <option [value]="keyboardTypes.decimalPad">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_DECIMALPAD' | translate}}</option>
      <option [value]="keyboardTypes.UIKeyboardTypeTwitter">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_UIKEYBOARDTYPETWITTER' | translate}}</option>
      <option [value]="keyboardTypes.webSearch">{{'FORM_PAGE_TYPE_INPUT_OPTION_KEYBOARDTYPE_WEBSEARCH' | translate}}</option>
    </select>
  </div>
  <div class="option">
    <span class="title">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE' | translate}}:</span>
    <select [(ngModel)]="option.textContentType" [disabled]="readOnly" class="select">
      <option [value]="textContentTypes.none">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_NONE' | translate}}</option>
      <option [value]="textContentTypes.namePrefix">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_NAMEPREFIX' | translate}}</option>
      <option [value]="textContentTypes.givenName">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_GIVENNAME' | translate}}</option>
      <option [value]="textContentTypes.middleName">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_MIDDLENAME' | translate}}</option>
      <option [value]="textContentTypes.familyName">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_FAMILYNAME' | translate}}</option>
      <option [value]="textContentTypes.nameSuffix">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_NAMESUFFIX' | translate}}</option>
      <option [value]="textContentTypes.nickname">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_NICKNAME' | translate}}</option>
      <option [value]="textContentTypes.jobTitle">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_JOBTITLE' | translate}}</option>
      <option [value]="textContentTypes.organizationName">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_ORGANIZATIONNAME' | translate}}</option>
      <option [value]="textContentTypes.location">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_LOCATION' | translate}}</option>
      <option [value]="textContentTypes.fullStreetAddress">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_FULLSTREETADDRESS' | translate}}</option>
      <option [value]="textContentTypes.streetAddressLine1">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_STREETADDRESSLINE1' | translate}}</option>
      <option [value]="textContentTypes.streetAddressLine2">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_STREETADDRESSLINE2' | translate}}</option>
      <option [value]="textContentTypes.addressCity">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_ADDRESSCITY' | translate}}</option>
      <option [value]="textContentTypes.addressState">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_ADDRESSSTATE' | translate}}</option>
      <option [value]="textContentTypes.addressCityAndState">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_ADDRESSCITYANDSTATE' | translate}}</option>
      <option [value]="textContentTypes.sublocality">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_SUBLOCALITY' | translate}}</option>
      <option [value]="textContentTypes.countryName">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_COUNTRYNAME' | translate}}</option>
      <option [value]="textContentTypes.postalCode">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_POSTALCODE' | translate}}</option>
      <option [value]="textContentTypes.telephoneNumber">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_TELEPHONENUMBER' | translate}}</option>
      <option [value]="textContentTypes.emailAddress">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_EMAILADDRESS' | translate}}</option>
      <option [value]="textContentTypes.URL">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_URL' | translate}}</option>
      <option [value]="textContentTypes.creditCardNumber">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_CREDITCARDNUMBER' | translate}}</option>
      <option [value]="textContentTypes.username">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_USERNAME' | translate}}</option>
      <option [value]="textContentTypes.password">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_PASSWORD' | translate}}</option>
      <option [value]="textContentTypes.newPassword">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_NEWPASSWORD' | translate}}</option>
      <option [value]="textContentTypes.oneTimeCode">{{'FORM_PAGE_TYPE_INPUT_OPTION_TEXTCONTEXTTYPE_ONETIMECODE' | translate}}</option>
    </select>
  </div>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_INPUT_VARIABLE' | translate}}:</span>
  <app-variable-selector-input class="input"
                               [VariableData]="variableData"
                               (setVariable)="setVariable($event)"
                               [canSelectConstants]="false"
                               [readOnly]="readOnly"
                               [typeFilters]="[ variableTypes.Text ]"
                               [validator]="page.isVariableIdValid.bind(page, editorService)"></app-variable-selector-input>
</div>
