@import '_variables';
@import '_mixins';

.evaluate-commands {
	background-color: #fff;
	padding: 10px;
	width: 500px;

  .commands, .expression {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }
  }

  .commands {
    margin-top: 5px;
  }
}
