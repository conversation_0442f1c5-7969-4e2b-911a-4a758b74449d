import {Component, OnInit, Input, OnDestroy} from '@angular/core';
import { Text } from '../../../../../models/pieces/MessagePieceType';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';
import {
  InteractiveMessageListPiece,
  InteractiveMessageListSection, InteractiveMessageListSectionRow
} from "../../../../../models/pieces/InteractiveMessageListPiece";
import {BlockDefinition} from "../../../../../models/BlockDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {ChannelTypes} from "../../../../../models/ChannelType";
import {TypedJSON} from "typedjson";
import { ErrorPopupComponent } from 'src/app/components/error-popup/error-popup.component';

@Component({
  selector: 'app-interactive-message-list-piece',
  templateUrl: './interactive-message-list-piece.component.html',
  styleUrls: ['./interactive-message-list-piece.component.scss']
})
export class InteractiveMessageListPieceComponent extends BasePieceVM implements OnInit, OnDestroy {
  model: InteractiveMessageListPiece;
  subs = new Subscription();
  variableFilter = [TypeDefinition.Text];
  channelTypes = ChannelTypes;

  constructor(editorService: EditorService,
              public modalService: ModalService,
              private singleOverlay: SingleOverlayService,
              private dragulaService: DragulaService) {
    super(editorService, modalService);
  }

  get variableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.AssignToVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  ngOnInit() {
    this.model = this.context as InteractiveMessageListPiece;
    this.flow = this.editorService.getCurrentFlow();

    if (this.model.Channel === ChannelTypes.WhatsApp) {
      this.variableFilter = [TypeDefinition.Text];
    } else {
      this.variableFilter = [TypeDefinition.Array];
    }

    if (this.dragulaService.find('SECTIONS_GROUP') == null) {
      this.dragulaService.createGroup('SECTIONS_GROUP', {
        moves: (el, container, handle) => {
          return !this.readOnly &&
                 (handle.classList.contains('section-drag-handle') ||
                  handle.parentElement.classList.contains('section-drag-handle'));
        }
      });
    }

    if (this.dragulaService.find('BUTTONS_GROUP') == null) {
      this.dragulaService.createGroup('BUTTONS_GROUP', {
        moves: (el, container, handle) => {
          return !this.readOnly &&
                 (handle.classList.contains('button-drag-handle') ||
                  handle.parentElement.classList.contains('button-drag-handle'));
        },
        accepts: (el, target, source) => {
          return target.closest('.section-rows') === source.closest('.section-rows');
        }
      });
    }

    this.model.Sections.forEach((_, index) => {
      const bagName = `SECTION_ROWS_${index}`;
      if (this.dragulaService.find(bagName) == null) {
        this.dragulaService.createGroup(bagName, {
          moves: (el, container, handle) => {
            return !this.readOnly &&
                   (handle.classList.contains('drag-handle') ||
                    handle.parentElement.classList.contains('drag-handle'));
          },
          accepts: (el, target, source) => {
            return source.getAttribute('data-section-id') === target.getAttribute('data-section-id');
          }
        });
      }
    });

    this.subs.add(
      this.dragulaService.dropModel("SECTIONS_GROUP").subscribe(() => {
        this.onSectionsReordered();
      })
    );

    this.model.Sections.forEach((_, index) => {
      this.subs.add(
        this.dragulaService.dropModel(`SECTION_ROWS_${index}`).subscribe(() => {
          this.onRowsReordered(index);
        })
      );
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.dragulaService.destroy('SECTIONS_GROUP');
    this.model.Sections.forEach((_, index) => {
      this.dragulaService.destroy(`SECTION_ROWS_${index}`);
    });
  }

  onSectionsReordered() {
    this.model.Sections = [...this.model.Sections];
    this.model.Sections.forEach((_, index) => {
      const bagName = `SECTION_ROWS_${index}`;
      this.dragulaService.destroy(bagName);
      this.dragulaService.createGroup(bagName, {
        moves: (el, container, handle) => {
          return !this.readOnly &&
                 (handle.classList.contains('drag-handle') ||
                  handle.parentElement.classList.contains('drag-handle'));
        },
        accepts: (el, target, source) => {
          return source.getAttribute('data-section-id') === target.getAttribute('data-section-id');
        }
      });
    });
  }

  onRowsReordered(sectionIndex: number) {
    this.model.Sections[sectionIndex].Rows = [...this.model.Sections[sectionIndex].Rows];
    if (this.getTotalRows() > 10) {
      const errorInfo = {
        title: 'INTERACTIVE_MESSAGE_LIST_MAX_ROWS_WARNING',
        desc: 'INTERACTIVE_MESSAGE_LIST_MAX_ROWS_DESCRIPTION'
      };
      this.modalService.init(ErrorPopupComponent, { Title: errorInfo.title, Desc: errorInfo.desc }, {});
    }
  }

  addNewText() {
    this.model.TextList.push(new Text());
  }

  canAddTextOptions(): boolean {
    return this.model.TextList.length < 3;
  }

  isTextValid(index: number): () => boolean {
    let text = this.model.TextList[index];
    if (typeof(text.text) === 'undefined') {
      return () => false;
    }
    return () => { return this.model.isTextValid(text.text, 1024, true, this.editorService); };
  }

  isButtonShowTextValid() {
    return () => { return this.model.isTextValid(this.model.ButtonShowListText, 20, false, this.editorService); };
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.BlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.BlockId = null;
  }

  onSelectBlockSectionRow(row: InteractiveMessageListSectionRow, blockData: BlockDefinition) {
    row.BlockID = blockData.Id;
  }

  onDeleteBlockSectionRow(row: InteractiveMessageListSectionRow) {
    row.BlockID = null;
  }

  canAddSection(): boolean {
    return this.getTotalRows() < 10;
  }

  addNewSection() {
    this.model.Sections.push(new InteractiveMessageListSection());
  }

  addNewSectionRow(section: InteractiveMessageListSection) {
    section.Rows.push(new InteractiveMessageListSectionRow());
  }

  canAddSectionRow(section: InteractiveMessageListSection): boolean {
    return this.getTotalRows() < 10;
  }

  getTotalRows(): number {
    return this.model.Sections.reduce((total, section) => total + section.Rows.length, 0);
  }

  deleteSection(index: number) {
    this.model.Sections.splice(index, 1);
  }

  cloneSection(section: InteractiveMessageListSection) {
    let json = TypedJSON.stringify(section, InteractiveMessageListSection, {preserveNull: true});
    let newSection = TypedJSON.parse<InteractiveMessageListSection>(json, InteractiveMessageListSection);
    newSection.Title = `${newSection.Title}_${this.model.Sections.length + 1}`;
    newSection.Rows.forEach(row => {
      row.Value = `${row.Value}_${this.model.Sections.length + 1}`;
    });
    this.model.Sections.push(newSection);
  }

  cloneRow(section: InteractiveMessageListSection, row: InteractiveMessageListSectionRow) {
    let json = TypedJSON.stringify(row, InteractiveMessageListSectionRow, {preserveNull: true});
    let newRow = TypedJSON.parse<InteractiveMessageListSectionRow>(json, InteractiveMessageListSectionRow);
    newRow.Value = `${newRow.Value}_${section.Rows.length + 1}`;
    section.Rows.push(newRow);
  }

  deleteRow(section: InteractiveMessageListSection, index: number) {
    section.Rows.splice(index, 1);
  }

  setVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.model.AssignToVariableId = variable.Id;
    }
    else {
      this.model.AssignToVariableId = null;
    }
  }

  validateVariable(): () => boolean {
    return () => {
      return this.model.isAssignToVariableIdValid(this.editorService);
    };
  }
}
