import {Component, OnInit} from '@angular/core';
import {BaseDynamicComponent} from '../../../../utils/component-holder/BaseDynamicComponent';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {BlockDefinition} from '../../../../../models/BlockDefinition';
import {JumpToBlockPiece} from "../../../../../models/pieces/JumpToBlockPiece";
import {ReturnToLastBlockPiece} from "../../../../../models/pieces/ReturnToLastBlockPiece";

@Component({
  selector: 'app-return-to-last-block-piece',
  templateUrl: './return-to-last-block-piece.component.html',
  styleUrls: ['./return-to-last-block-piece.component.scss']
})
export class ReturnToLastBlockPieceComponent extends BasePieceVM implements OnInit {
  model: ReturnToLastBlockPiece;
  parentBlock : BlockDefinition;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as ReturnToLastBlockPiece;
    this.parentBlock = this.editorService.getSelectedBlock();
  }

  isInInvalidPosition(): boolean {
    if (this.parentBlock !== null) {
      let currentIndex = this.parentBlock.Pieces.findIndex(p => p.Id === this.model.Id);
      if (currentIndex !== -1) {
        return currentIndex !== this.parentBlock.Pieces.length - 1;
      }
    }
    return false;
  }
}
