import { Component, OnInit, Input } from '@angular/core';
import {MimeType, MimeTypeDefinition} from '../../../../../models/MimeType';
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'app-mime-type-picker',
  templateUrl: './mime-type-picker.component.html',
  styleUrls: ['./mime-type-picker.component.scss']
})
export class MimeTypePickerComponent {
  @Input() MimeTypeDefinition: MimeTypeDefinition;

  constructor(private translateService: TranslateService) {
  }

  getLabel(): string {
    switch (this.MimeTypeDefinition.Type) {
      case MimeType.All:
        return this.translateService.instant('FILE_ALL');
      case MimeType.Image:
        return this.translateService.instant('FILE_IMAGE');
      case MimeType.Text:
        return this.translateService.instant('FILE_TEXT');
      case MimeType.Pdf:
        return this.translateService.instant('FILE_PDF');
      case MimeType.Word:
        return this.translateService.instant('FILE_WORD');
      case MimeType.Audio:
        return this.translateService.instant('FILE_AUDIO');
      case MimeType.Video:
        return this.translateService.instant('FILE_VIDEO');
      case MimeType.Excel:
        return this.translateService.instant('FILE_EXCEL');
      case MimeType.Ppt:
        return this.translateService.instant('FILE_PPT');
      case MimeType.Zip:
        return this.translateService.instant('FILE_ZIP');
    }
  }
}
