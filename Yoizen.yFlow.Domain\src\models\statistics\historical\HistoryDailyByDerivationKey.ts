import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByDerivationKey extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare total: number;
    declare derivationKey: string;

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.version = data.version;
        this.total = data.total;
        this.derivationKey = data.derivationKey
    }

    type() {
        return HistoryDailyInfoTypes.DerivationKey;
    }
}

HistoryDailyByDerivationKey.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    derivationKey: {
        type: DataTypes.STRING,
        field: 'derivation_key',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_derivation_key',
    tableName: 'history_daily_by_derivation_key',
    timestamps: false
})