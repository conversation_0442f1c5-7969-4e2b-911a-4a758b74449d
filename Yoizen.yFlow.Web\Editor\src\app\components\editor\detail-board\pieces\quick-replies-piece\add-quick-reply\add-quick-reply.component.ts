﻿import {Component, ElementRef, EventEmitter, Input, OnInit, AfterViewInit, Output, ViewChild} from '@angular/core';
import {QuickReplyOption, QuickReplyType} from "../../../../../../models/pieces/QuickReplyPiece";
import {BlockDefinition} from "../../../../../../models/BlockDefinition";
import {EditorService} from "../../../../../../services/editor.service";
import {isStringValid, isUrlValid} from "../../../../../../urlutils.module";
import {OperatorDefinitions} from 'src/app/models/OperatorType';
import {VariableDefinition} from 'src/app/models/VariableDefinition';
import {ChannelTypes} from "../../../../../../models/ChannelType";

@Component({
  selector: 'app-add-quick-reply',
  templateUrl: './add-quick-reply.component.html',
  styleUrls: ['./add-quick-reply.component.scss'],
})
export class AddQuickReplyComponent implements OnInit, AfterViewInit {
  @ViewChild('tabset', { static: false }) tabSet;
  @ViewChild('nameInput', { static: false }) NameInput: ElementRef;
  @ViewChild('blockInput', { static: false }) blockInput: ElementRef;

  @Input() Model: QuickReplyOption;
  @Input() AllowedButtonTypes: QuickReplyType[] = null;
  @Input() DisplayCreateMenuOption: boolean = false;
  @Input() IsPersistentMenuButton: boolean = false;
  @Input() readOnly: boolean = false;
  @Output() onClose: EventEmitter<string> = new EventEmitter();

  ActiveIdString: string;
  SearchBlockString: string = "";
  hideBlockSelector: boolean = true;
  BlockData: BlockDefinition = null;

  constructor(private editorService: EditorService) {}

  ngOnInit() {
    this.ActiveIdString = this.getActiveTab();
    if (this.Model && this.Model.BlockId) {
      this.BlockData = this.editorService.findBlockWithId(this.Model.BlockId);
      this.SearchBlockString = this.BlockData && this.BlockData.Name || "";
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.NameInput && this.NameInput.nativeElement) {
        this.NameInput.nativeElement.select();
      }
    });
  }

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId( this.Model.AssignToVariableId);
  }

  getActiveTab(): string {
    switch (this.Model.Type) {
      case QuickReplyType.Redirect:
        return "tab-redirect-to-block";
      case QuickReplyType.Location:
        return "tab-location";
      case QuickReplyType.LocationWithMap:
        return "tab-locationwithmap";
      case QuickReplyType.UserPhoneNumber:
        return "tab-userphonenumber";
      case QuickReplyType.UserEmail:
        return "tab-useremail";
      case QuickReplyType.Url:
        return "tab-url-input";
    }

    return "tab-redirect-to-block";
  }

  isNameValid(): boolean {
    return isStringValid(this.Model.Text);
  }

  isButtonTypeAllowed(type: string) : boolean {
    if (this.AllowedButtonTypes === null) {
      return true;
    }

    switch (type) {
      case 'redirect':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.Redirect) >= 0;
      case 'location':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.Location) >= 0;
      case 'location_with_map':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.LocationWithMap) >= 0;
      case 'user_phone_number':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.UserPhoneNumber) >= 0;
      case 'user_email':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.UserEmail) >= 0;
      case 'url':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.Url) >= 0;
      case 'text':
        return this.AllowedButtonTypes.indexOf(QuickReplyType.Text) >= 0;
    }

    return false;
  }

  isStringValid(str: string): boolean {
    return isStringValid(str);
  }

  isValidURL(str: string) {
    return isUrlValid(str);
  }

  onTabChange(eventInfo) {
    this.hideBlockSelector = true;
    switch (eventInfo.nextId) {
      case "tab-redirect-to-block":
        this.Model.Type = QuickReplyType.Redirect;
        return;
      case "tab-location":
        this.Model.Type = QuickReplyType.Location;
        return;
      case "tab-locationwithmap":
        this.Model.Type = QuickReplyType.LocationWithMap;
        return;
      case "tab-userphonenumber":
        this.Model.Type = QuickReplyType.UserPhoneNumber;
        return;
      case "tab-useremail":
        this.Model.Type = QuickReplyType.UserEmail;
        return;
      case "tab-url-input":
        this.Model.Type = QuickReplyType.Url;
        return;
      case "tab-text":
        this.Model.Type = QuickReplyType.Text;
        return;
    }
  }

  onBlockSelect(blockData) {
    if (!blockData) return;

    this.Model.BlockId = blockData.Id;
    this.SearchBlockString = blockData.Name;
    this.BlockData = this.editorService.findBlockWithId(this.Model.BlockId);

    setTimeout(() => {
      if (this.NameInput && this.NameInput.nativeElement) {
        this.NameInput.nativeElement.select();
      }
    });
  }

  hasBlock(): boolean {
    return !!this.BlockData;
  }

  deleteBlock() {
    this.BlockData = null;
    this.Model.BlockId = "-1";
    this.SearchBlockString = "";

    setTimeout(() => {
      if (this.blockInput && this.blockInput.nativeElement) {
        this.blockInput.nativeElement.select();
      }
    });
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.Model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }

  getFirstValidator() {
    return str => { return this.Model.isFirstOperandValid();};
  }
  getSecondValidator() {
    return str => { return this.Model.isSecondOperandValid();};
  }

  setVariableOnOutput(variable : VariableDefinition) {
    if( variable != null) {
      this.Model.AssignToVariableId = variable.Id;
    }
    else {
      this.Model.AssignToVariableId = null;
    }
  }

  getVariableValidator() {
    return str => { return this.Model.isAssignValueValid();};
  }

  getAssignVariableIdValid() {
    return str => { return this.Model.isAssignToVariableIdValid(this.editorService);};
  }
}
