'use strict';


const IJS = require('image-js').Image;


const { parse } = require('mrz');

module.exports = function (options) {
  const { getMrz, readMrz } = require('mrz-detection');
  return async function detectAndParseMrz(image, result, progress) {
    result = result || {};

    if (progress) {
      console.time('detecting');
      progress('detecting');
    }
    result.detected = {};
    try {
      await getMrz(await IJS.load(image), {
        debug: true,
        out: result.detected
      });
    } catch (e) {
      result.error = e;
    }
    if (progress) console.timeEnd('detecting');
    if (result.error) return result;

    if (progress) {
      console.time('ocrizing');
      progress('ocrizing');
    }
    try {
      result.ocrized = await readMrz(await IJS.load(result.detected.crop.toDataURL()), {
        debug: true
      });
    } catch (e) {
      result.error = e;
    }
    if (progress) console.timeEnd('ocrizing');
    if (result.error) return result;

    if (progress) {
      console.time('parsing');
      progress('parsing');
    }
    try {
      result.parsed = parse(result.ocrized);
    } catch (e) {
      result.error = e;
    }
    if (progress) console.timeEnd('parsing');
    return result;
  }
};