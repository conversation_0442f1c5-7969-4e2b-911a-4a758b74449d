import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router'
import { TranslateService } from '@ngx-translate/core';
import { EditorService } from '../../../services/editor.service';
import { FlowDefinition } from '../../../models/FlowDefinition';
import { ChannelTypes } from '../../../models/ChannelType'
import { ServerService } from "../../../services/server.service";
import { geflowsTypes, getTokenPayload } from 'src/app/Utils/window';
import { FlowTypesToString } from 'src/app/models/FlowType';
import { FlowModuleResponse } from 'src/app/models/FlowModuleResponse';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { finalize } from 'rxjs/operators';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { DashboardModuleModalComponent } from '../dashboard-module-modal/dashboard-module-modal.component';
import { ToasterService } from 'angular2-toaster';

@Component({
  selector: 'app-dashboard-flow',
  templateUrl: './dashboard-flow.component.html',
  styleUrls: ['./dashboard-flow.component.scss']
})
export class DashboardFlowComponent implements OnInit, OnDestroy {
  @Input() model: FlowDefinition;
  @Input() showActions: boolean = true;
  canPublish: boolean = true;
  canEdit: boolean = true;
  canSeeStatistics: boolean = false;
  @Input() showCloneButton: boolean = true;
  @Input() onSelect: Function = null;

  @Output() onDelete: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onClone: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onUpload: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onDownload: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onDownloadProd: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onPublish: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onselectFlow: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  flowsTypes: string[] = [];
  icon: string;
  paramsSub: any;
  showFlowType: boolean = false;
  isFolder: boolean = false;

  constructor(protected editorService: EditorService,
    private serverService: ServerService,
    private routerService: Router,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private modalService: ModalService,
    private toasterService: ToasterService,
    private translateService: TranslateService) {
  }

  ngOnInit() {
    let tokenPayload = getTokenPayload();
    if (typeof (geflowsTypes()) !== 'undefined') {
      geflowsTypes().split(',').forEach(item => {
        this.flowsTypes.push(item);
      });
    }
    this.showFlowType = this.flowsTypes.length === 2;
    this.canPublish = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canPublish : tokenPayload.publish;
    this.canEdit = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canEdit : tokenPayload.edit;
    this.isFolder = this.model.modules !== undefined && this.model.modules.length > 0;
    this.canSeeStatistics = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canSeeStatistics : tokenPayload.seeStatistics;
  }

  selectFlow($event: Event, isStaging) {
    if (typeof (this.onSelect) !== 'undefined' && this.onSelect !== null) {
      this.onSelect(this.model, isStaging);
    }
    else {
      let target = <Element>$event.target;
      if (target.classList.contains('dropdown-toggle') || target.parentElement.classList.contains('dropdown-toggle')) {
        return;
      }
      this.onselectFlow.emit();
      if (!this.isFolder && !this.canEdit && !this.canPublish) {
        if (!this.canSeeStatistics) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PERMISSION'));
          return;
        }

        if (this.model.ActiveProductionVersion === undefined || this.model.ActiveProductionVersion === null) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PRODUCTION_VERSION'));
          return;
        }

        if (this.model.master_flow_id !== null) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PERMISSION'));
          return;
        }
      }

      if (this.model.modules !== undefined && this.model.modules.length > 0) {
        this.editorService.setCurrentFlow(this.model, !isStaging);
        this.paramsSub = this.route.params.subscribe(params => {
          if (params['companyName']) {
            this.routerService.navigateByUrl(`${params['companyName']}/flow/${this.model.id}/modules/${!isStaging}`);
          }
          else {
            this.routerService.navigateByUrl(`/flow/${this.model.id}/modules/${!isStaging}`);
          }
        });
        return;
      }

      this.serverService.getFlowModules(this.model.id)
        .pipe(finalize(() => {
        }))
        .subscribe((response: FlowModuleResponse) => {
          if (response !== null) {
            let master = new ModuleDefinition();
            master.createMaster();
            master.id = Number(this.model.id);

            response.flows = response.flows.map(flow => {
              let module = new ModuleDefinition();
              module.init(flow);
              return module
            });
            response.flows.push(master);
            this.editorService.setModules(response.flows);

            this.serverService.getFlow(this.model.id)
              .subscribe((flow: FlowDefinition) => {
                this.model.ActiveProductionVersion = flow.ActiveProductionVersion;
                this.model.ActiveStagingVersion = flow.ActiveStagingVersion;
                this.model.isMasterBot = flow.isMasterBot;
                let blob: string = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;
                this.editorService.restoreFromSerializedString(
                  blob,
                  this.model.channel);

                this.paramsSub = this.route.params.subscribe(params => {
                  if (params['companyName']) {
                    this.routerService.navigateByUrl(`${params['companyName']}/edit-flow/${this.model.id}/${!isStaging}`);
                  }
                  else {
                    this.routerService.navigateByUrl(`edit-flow/${this.model.id}/${!isStaging}`);
                  }
                });

                this.editorService.setCurrentFlow(this.model, !isStaging);
              });
          }
        });
    }
  }

  ngOnDestroy() {
    if (this.paramsSub != null) {
      this.paramsSub.unsubscribe();
    }
  }

  getFlowType(): string {
    return FlowTypesToString(this.model.type);
  }

  getClass(): string {
    return FlowDefinition.getFlowClass(this.model.channel);
  }

  getIcon(): string {
    return FlowDefinition.getFlowIcon(this.model.channel);
  }

  getTooltip(): string {
    var comments: string = '';
    comments = this.model.ActiveStagingVersion.comments && this.model.ActiveStagingVersion.comments.length > 0 ? this.model.ActiveStagingVersion.comments : null;
    if (comments != null) {
      return this.translate.instant('COMMENTS', { value: comments });
    }

    return comments;
  }
}
