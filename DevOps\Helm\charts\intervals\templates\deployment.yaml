apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ printf "yflow-%s-%s-intervals" (lower .Values.global.client) .Values.global.environment }}
  namespace: {{ .Release.Namespace }}
  labels:
    product: {{ $.Values.global.productName }}
    app: {{ printf "%s-%s" $.Values.global.productName .Values.appName }}
spec:
  {{- if not .Values.enableHPA }}
  replicas:  {{ .Values.replicas | default 0 }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ printf "yflow-%s-%s-intervals" (lower .Values.global.client) .Values.global.environment }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0      
  template:
    metadata:
      labels:
        app: {{ printf "yflow-%s-%s-intervals" (lower .Values.global.client) .Values.global.environment }}
    spec:
      {{- if .Values.global.imagePullSecrets }}
      imagePullSecrets:
      - name: {{ .Values.global.imagePullSecrets | default "dockerhub-secret" }}
      {{- end }}

      {{- if or .Values.affinity .Values.global.nodeSelector }}
      affinity:
        {{- include "common-helpers.affinity" . | nindent 8 }}
      {{- end }}
      {{- if or .Values.tolerations .Values.global.nodeSelector }}
      tolerations:
        {{- include "common-helpers.tolerations" . | nindent 8 }}
      {{- end }}
        
      containers:
      - name: {{ printf "yflow-%s-%s-intervals" (lower .Values.global.client) .Values.global.environment }}
        image: {{ printf "%s/yflow-intervals:%s" .Values.global.registry .Chart.Version }}
        imagePullPolicy: {{ .Values.imagePullPolicy | default "IfNotPresent" }}        
        ports:
        - containerPort: 3000

        # Environment Variables
        env:
        - name: client
          value: {{ .Values.global.client | quote }}

        # Required ConfigMap environments  
        {{ include "common-helpers.requiredConfigMapEnvVars" . | nindent 6 }}
        # Optional ConfigMap environments
        {{ include "common-helpers.optionalConfigMapEnvVars" . | nindent 6 }}
        # Required Secret environments
        {{ include "common-helpers.requiredSecretEnvVars" . | nindent 6 }}
        # Optional Secret environments
        {{ include "common-helpers.optionalSecretEnvVars" . | nindent 6 }}

        {{- if .Values.healthcheck }}
        startupProbe:
          httpGet:
            path: {{ .Values.healthcheck | quote }}
            port: {{ .Values.containerPort | default 3000 }}
          initialDelaySeconds: 15
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: {{ .Values.healthcheck | quote }}
            port: {{ .Values.containerPort | default 3000 }}
          initialDelaySeconds: 5
          periodSeconds: 15
        livenessProbe:
          httpGet:
            path: {{ .Values.healthcheck | quote }}
            port: {{ .Values.containerPort | default 3000 }}
          initialDelaySeconds: 5
          periodSeconds: 15
        {{- end }}

        # Resources
        {{- if or .Values.limitsCPU .Values.limitMEM .Values.requestsCPU .Values.requestsMEM }}
        resources:
          {{- if or .Values.requestsCPU .Values.requestsMEM }}
          requests:
            {{- if .Values.requestsCPU }}
            cpu: {{ .Values.requestsCPU | quote }}
            {{- end }}
            {{- if .Values.requestsMEM }}
            memory: {{ .Values.requestsMEM | quote }}
            {{- end }}
          {{- end }}        
          {{- if or .Values.limitsCPU .Values.limitMEM }}
          limits:
            {{- if .Values.limitsCPU }}
            cpu: {{ .Values.limitsCPU | quote }}
            {{- end }}
            {{- if .Values.limitMEM }}
            memory: {{ .Values.limitMEM | quote }}
            {{- end }}
          {{- end }}
        {{- end }}

        # Persistencia 
        {{- if .Values.mountPath  }}
        volumeMounts:
        - name: persistent-storage
          mountPath: {{ .Values.mountPath | quote }}
      volumes:
      - name: persistent-storage
        persistentVolumeClaim:
          claimName: {{ printf "pvc-yflow-%s-%s" (lower .Values.global.client) .Values.global.environment }}
        {{- end }} 

      restartPolicy: Always      