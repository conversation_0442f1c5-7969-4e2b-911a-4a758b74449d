import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VariableConditionPieceComponent } from './variable-condition-piece.component';

describe('VariableConditionPieceComponent', () => {
  let component: VariableConditionPieceComponent;
  let fixture: ComponentFixture<VariableConditionPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VariableConditionPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VariableConditionPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
