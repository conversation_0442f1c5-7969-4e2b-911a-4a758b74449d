@import '_variables';
@import '_mixins';

.wa-menu {
  background-color: #fff;
  padding: 10px;
  width: 830px;

  .message {
    flex-direction: column !important;
        .messages {
            display: flex;
            flex-direction: row;
            border-bottom: solid 1px $gray;

            app-text-list-message {
                width: 218px;
            }

            .addText {
                @include addButton;
                width: 40px;
                flex-grow: 0;
                flex-shrink: 0;
            }
        }

        &.whatsapp {
            .max-length {
                display: block;

                .whatsapp {
                    display: block;
                }
            }

            .messages {
                border-bottom-style: none;
            }
        }
  }

  .jump, .message, .destvariableparseformat, .regex, .errormessage, .retries, .next, .command.validation {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }



  .validation-table {
    display: table;
    width: 100%;

    .header {
      display: table-header-group;
      font-family: $fontFamilyTitles;
      font-weight: bold;

      &>div {
        display: table-cell;
        vertical-align: middle;
        border-bottom: 1px solid $sidebarBorderColor;
        padding-left: 5px;
        padding-right: 5px;

        &.center {
          text-align: center;
          width: 180px;
        }
      }
    }

    .row {
      display: table-row;
      height: 40px;

      .center {
        .circle-button {
          cursor: pointer;
        }
      }

      /*&:hover {
        background: lighten($version-highlight, 10%);
      }*/

      .readonly-button {
        cursor: not-allowed !important;
        filter: brightness(65%);
      }

      &>div {
        display: table-cell;
        vertical-align: middle;
        border-bottom: 1px solid $sidebarBorderColor;
        padding-left: 5px;
        padding-right: 5px;

        &.center {
          text-align: center;
          width: 130px;
        }
      }

    }

    &:last-child {
      margin-bottom: 4px;
      &>div {
        border-bottom: 2px none $sidebarBorderColor;
      }
    }
  }

  .validationerror {
    margin-top: 4px;
    margin-bottom: 10px;

    .messages {
      display: flex;
      flex-direction: row;

      app-text-list-error-message {
        width: 218px;
      }

      .addText {
        @include addButton;
        width: 40px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }

    .errormessage {
      .inputerrormessage {
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .retries {
      .tries {
        width: 80px;
      }
    }
  }

  .regex, .destvariableparseformat {
    .input {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .validationerror, .commands {
    margin-bottom: 10px;
  }

  .commands {
    border-bottom: 1px solid $gray;
  }

  .regex-container {
    display: flex;
    width: 100%;

    .input {
      padding-right: 40px;
    }

    .icon {
      position: relative;
      min-width: 30px;
      line-height: 30px;
      text-align: center;
      top: 0;
      left: -30px;
    }

    .fa-question-circle {
      color: $flowColor;
      cursor: pointer;
      &:hover {
        color: lighten($flowColor, 15%);
      }
    }
  }

  .template-container {
    display: flex;
    width: 100%;

    .input {
      padding-right: 70px;
    }

    .icon {
      position: relative;
      min-width: 30px;
      line-height: 30px;
      text-align: center;
      top: 0;
      left: -60px;
    }

    .fa-trash {
      color: red;
      cursor: pointer;
      &:hover {
        color: lighten(red, 15%);
      }
    }

    .fa-edit {
      color: green;
      cursor: pointer;
      &:hover {
        color: lighten(green, 15%);
      }
    }
  }

  .regex-help {
      text-align: center;
      text-transform: uppercase;
      padding: 10px 0;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      font-weight: normal;
      color: $linkActionColor;
      border-top: 1px solid $gray;
      border-bottom: 1px solid $gray;
      margin-bottom: 10px;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }

      span {
        display: inline-block;
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 13px;
        margin-left: 30px;
      }
  }

  .add-condition {
    text-align: center;
    text-transform: uppercase;
    padding: 10px 0;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    font-weight: normal;
    color: $linkActionColor;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;

    &:hover {
      color: lighten($linkActionColor, 10%);
    }

    span {
      display: inline-block;
      margin-right: 10px;
      position: absolute;
      left: -10px;
      top: 13px;
      margin-left: 30px;
    }
  }

  .condition {
    position: relative;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    padding-right: 20px;

    .info {
      padding: 5px 0;
      justify-content: flex-start;
      align-content: center;
      display: flex;

      .value {
        flex-grow: 1;
        flex-shrink: 1;
        padding-left: 10px;
      }
    }

    .trash {
      @include trash;
      position: absolute;
      right: -13px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        opacity: 1;
      }
    }
  }

  .mark-as-pending-reply {
    .alert-info {
      color: #0c5460;
      background-color: #d1ecf1;
      border-color: #bee5eb;
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .option {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .mark-as-pending-reply {
    border-top: 1px solid $gray;

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .jump {
    border-bottom: 1px solid $gray;

      .conditions {
        margin-top: 10px;

        & > .title {
          text-align: left;
          font-family: $fontFamilyTitles;
          font-weight: bold;
          font-size: 120%;
          margin-bottom: 5px;
        }

        .conditions-table {
          display: table;
          width: 100%;

          .header {
            display: table-header-group;
            font-family: $fontFamilyTitles;

            & > div {
              display: table-cell;
              vertical-align: middle;
              border-bottom: 1px solid $sidebarBorderColor;
            }
          }

          .condition-row {
            display: table-row;
            width: 100%;
            margin-top: 5px;
            height: 40px;
            min-height: 40px;

            & > div {
              display: table-cell;
              vertical-align: middle;
              border-bottom: 1px solid $sidebarBorderColor;
              padding-left: 3px;
              padding-right: 3px;
            }

            .condition-value {
              width: 150px;
            }

            .condition-variable {
              width: 200px;
            }

            .trash {
              width: 30px;

              & > div {
                @include trash;
                cursor: pointer;

                &:hover {
                  color: #555;
                }
              }
            }

            &:hover {
              .trash {
                & > div {
                  @include trashOver;
                }
              }
            }
          }
        }

        .add {
          color: $linkActionColor;
          text-transform: uppercase;
          font-size: 12px;
          margin: 20px 10px;
          width: max-content;
          cursor: pointer;

          &:hover {
            color: lighten($linkActionColor, 10%);
          }
        }
      }
  }
}
