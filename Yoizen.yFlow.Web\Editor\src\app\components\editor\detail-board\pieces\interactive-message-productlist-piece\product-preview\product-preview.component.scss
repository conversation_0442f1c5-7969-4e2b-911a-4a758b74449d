@import "_variables";
@import "_mixins";

.product-preview {
  .product {
    margin-right: 10px;
    position: relative;
    padding-left: 80px;
    padding-right: 10px;
    height: 80px;
    flex-direction: column;
    justify-content: center;
    display: flex;
    border: 1px solid $cardSeparatorBorderColor;
    width: 100%;

    .image {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;

      img {
        height: 60px;
      }
    }

    .name {
      font-weight: bold;
      font-size: 110%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow-x: hidden;
      max-width: 100%;

      .id {
        margin-left: 5px;
      }
    }

    .description {
      font-style: italic;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow-x: hidden;
      max-width: 100%;
    }
  }
}
