import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { PersistentMenuEntry } from '../../../../../models/PersistentMenuEntry';
import { ButtonPiece, ButtonType } from '../../../../../models/pieces/ButtonPiece';
import { PersistentMenu } from '../../../../../models/PersistentMenu';

@Component({
  selector: 'app-persistent-menu-option-list',
  templateUrl: './persistent-menu-option-list.component.html',
  styleUrls: ['./persistent-menu-option-list.component.scss']
})
export class PersistentMenuOptionListComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @Input() model : PersistentMenuEntry;
  @Input() buttonLimit: number = 3;
  @Input() title: string;
  @Output() onExpandMenu: EventEmitter<number> = new EventEmitter<number>();
  @Output() onRemoveSubMenu: EventEmitter<number> = new EventEmitter<number>();
  @Input() DisplayCreateMenuOption: boolean = false;
  @Input() AllowedButtonTypes: ButtonType[] = null;

  expandButton : ButtonPiece;
  constructor() { }

  ngOnInit() {
  }

  addNewButton() {
    this.expandButton = new ButtonPiece();
    this.model.buttons.push(this.expandButton);
  }

  deleteButtonElement(element) {
    let btn = this.model.buttons[element];
    if( btn.Type == ButtonType.PersistentMenu) {
      this.onRemoveSubMenu.emit( btn.PersistentMenuId);
    }
    this.model.buttons.splice(element, 1);

  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.expandButton = btn;
  }

  displaySubMenu(menuToexpand:number) {
    this.onExpandMenu.emit( menuToexpand);
  }

  canCreateElement() : boolean {
    if (this.readOnly) {
      return false;
    }

    if (typeof(this.model) === 'undefined') {
      return true;
    }

    return this.buttonLimit > this.model.buttons.length;
  }
}
