@import '_variables';
@import '_mixins';

.multiple-attachments {
  background-color: $block-base;
  width: 600px;

  .card-info, .card-title, .sourcearray {
    padding-left: 10px;
    p {
      margin-bottom: 0.2rem;
    }
  }

  .definition {
    padding: 10px;

    .publicurl {
      display: flex;
      flex-direction: row;
      margin-top: 0.5rem;
      margin-bottom: 10px;
      width: 30%;
      justify-content: space-between;
    }

    .source-button {
        width: 30%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
  }

  .body {
    width: 100%;
    padding-left: 10px;
    padding-bottom: 10px;
    padding-top: 10px;
    border-top: 1px solid $gray;

    label {
      font-family: $fontFamilyTitles;
      font-weight: bold;
    }

    select {
      margin-right: 10px;
      width: 29%;
    }

    textarea {
      width: 100%;
      background: $block-base;
      white-space: pre-wrap;
      height: 180px;
    }

    label {
      display: block;
    }

    .implicit-variables {
      padding: 0 10px;
      margin-bottom: 10px;

      .variables-info {
        font-family: $fontFamily;
        margin-bottom: 5px;
        color: #767676;
      }

      .variables-table {
        display: table;
        width: 100%;

        .variables-header,
        .variable-row {
          height: 30px;
        }

        .variables-header {
          display: table-row;
          font-family: $fontFamilyTitles;

          div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;
          }
        }

        .variable-row {
          display: table-row;

          .variable-cell {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;

            .variable-name {
              @include variable-name;
            }

            .variable-type {
              @include variable-type;
            }
          }

          &:last-child {
            .variable-cell {
              border-bottom-style: none;
            }
          }
        }
      }
    }

    .template, .mimetype, .data {
      display: flex;
      flex-direction: row;
      margin-top: 1rem;
      margin-bottom: 10px;
      width: 100%;

      .title {
        width: 15%;
      }

      .input-variable-area {
        flex-grow: 1;
      }
    }

    .mimetype, .template, .data {
      margin-bottom: 0;
    }

    .twitter {
      margin-top: 1rem;
      .empty {
        margin-top: 5px;
        .alert {
          margin-bottom: 0;
        }
      }
  
      .text {
        margin-top: 5px;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        border-bottom: $cardSeparator;
  
        .title {
          font-family: $fontFamilyTitles;
          font-weight: bold;
          margin-bottom: 5px;
          justify-self: center;
          align-self: flex-start;
          text-align: center;
          flex-grow: 0;
          flex-shrink: 0;
        }
      }
    }
  }

  .sourcearray {
    display: flex;
    flex-direction: row;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
    }
  }

  .sourcearray {
    margin-bottom: 10px;
  }
}

.title {
  font-family: $fontFamilyTitles;
  font-weight: bold;
  margin-right: 10px;
  align-self: center;
  text-align: center;
  flex-grow: 0;
  flex-shrink: 0;
}