import {Component, OnInit, Input, OnDestroy} from '@angular/core';
import { MessagePieceType, Text } from '../../../../../models/pieces/MessagePieceType';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import {FacebookCatalog, FacebookCatalogProduct, WhatsappCatalog} from "../../../../../models/WhatsappCatalog";
import {InteractiveMessageProductPiece} from "../../../../../models/pieces/InteractiveMessageProductPiece";


@Component({
  selector: 'app-interactive-message-product-piece',
  templateUrl: './interactive-message-product-piece.component.html',
  styleUrls: ['./interactive-message-product-piece.component.scss']
})
export class InteractiveMessageProductPieceComponent extends BasePieceVM implements OnInit {
  constructor( editorService : EditorService, public modalService : ModalService, private singleOverlay: SingleOverlayService, private dragulaService : DragulaService ) {
    super(editorService, modalService);
  }

  ready: boolean = false;
  model: InteractiveMessageProductPiece;
  catalogsInfo: WhatsappCatalog[];

  ngOnInit() {
    this.model = this.context as InteractiveMessageProductPiece;
    this.flow = this.editorService.getCurrentFlow();

    this.catalogsInfo = this.editorService.getWhatsappCatalogs();
    if (this.catalogsInfo !== null) {
      this.ready = true;
    }
    else {
      this.editorService.onWhatsappCatalogsLoaded.subscribe(() => {
        this.catalogsInfo = this.editorService.getWhatsappCatalogs();
        this.ready = true;
      });
    }
  }

  addNewText() {
  	this.model.TextList.push(new Text());
  }

  getCatalogProducts(): FacebookCatalogProduct[] {
    if (this.model.CatalogId !== null && this.model.CatalogId.length > 0) {
      let whatsappCatalog: WhatsappCatalog = this.catalogsInfo.find(c => c.Catalog.Id === this.model.CatalogId);
      if (typeof(whatsappCatalog) !== 'undefined' && whatsappCatalog !== null) {
        return whatsappCatalog.Catalog.Products;
      }
    }

    return null;
  }

  getCatalogProduct(productId: string): FacebookCatalogProduct {
    let products = this.getCatalogProducts();
    if (products !== null) {
      return products.find(p => p.RetailerId === productId);
    }

    return null;
  }

  canAddTextOptions() : boolean {
  	var value = this.model.TextList.length < 3;
  	return value;
  }

  isTextValid(index: number) {
    let text = this.model.TextList[index];
    if (typeof(text.text) === 'undefined') {
      return false;
    }

    return str => { return this.model.isTextValid(text.text, 1024, true, this.editorService); };
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }
}
