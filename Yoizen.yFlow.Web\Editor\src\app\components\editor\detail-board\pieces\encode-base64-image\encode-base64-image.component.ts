import { Component, OnInit } from '@angular/core';
import { EncodeBase64ImagePiece } from 'src/app/models/pieces/EncodeBase64ImagePiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-encode-base64-image',
  templateUrl: './encode-base64-image.component.html',
  styleUrls: ['./encode-base64-image.component.scss']
})
export class EncodeBase64ImageComponent extends BasePieceVM implements OnInit {
  model : EncodeBase64ImagePiece;
  variableFilter: TypeDefinition[] = [ TypeDefinition.Text];

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }

  ngOnInit() {
    this.model = this.context as EncodeBase64ImagePiece;
  }

  setVariableOnOutput(variable : VariableDefinition) {
    if( variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = null;
    }
  }

}
