﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using YFlowUtils.Models;
using YFlowUtils.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Dynamic;

namespace YFlowUtils.Controllers
{
	[ApiController]
	[Route("api/signature-pad")]
	public class SignaturePadController : ControllerBase
    {
		[HttpPost]
		[Route("Send")]
		public async Task<IActionResult> Send([FromBody]SignaturePad signature)
		{
			StatusResponse response = new StatusResponse();
			try
			{
				string key = "6+0WAvwNXuEtpst2l6ruHHBdHLUVfTVFJQhsECxNUPI=";
				string IV = "YJY2yfEyqHiGeS7dRnDSKw==";

				const int MIMETPYE = 2;

				Cryptography crypto = new Cryptography(key, IV);
				string tokenPlainData = crypto.Decrypt(signature.Token);

				dynamic tokenData = JObject.Parse(tokenPlainData);

				string guid = tokenData["uuid"];
				int messageId = tokenData["messageId"];
				string client = tokenData["client"];
				if (signature.IsGuidAlreadyAnswered(messageId, client))
				{
					response.Success = -1;
					response.Message = "The signature has already been sent";
					response.Data = null;
				} 
				else
				{
					var fileData = new JObject();

					fileData["msg"] = new JObject();
					fileData["msg"]["attach"] = new JObject();

					fileData["msg"]["attach"]["data"] = signature.Base64image.Split(',')[1];
					fileData["msg"]["attach"]["mimeType"] = MIMETPYE;
					fileData["msg"]["attach"]["name"] = Guid.NewGuid().ToString() + ".png";

					switch (tokenData["sst"].ToObject<string>())
					{
						case "1":
							fileData["account"] = tokenData["sc"]["UserId"].ToObject<string>();
							break;
						case "2":
							fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
							break;
						/*case "4":
							fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
							break;
						case "8":
							fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
							break;*/
						case "16":
							fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
							break;
						case "32":
							fileData["account"] = tokenData["sc"]["number"].ToObject<string>();
							break;
						/*case "64":
							fileData["account"] = tokenData["sc"]["userId"].ToObject<string>();
							break;*/
						case "128":
							fileData["account"] = tokenData["sc"]["FullPhoneNumber"].ToObject<string>();
							break;
						case "256":
							fileData["account"] = tokenData["sc"]["AccountId"].ToObject<string>();
							break;
						case "512":
							fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
							break;
						case "1024":
							fileData["account"] = tokenData["sc"]["ID"].ToObject<string>();
							break;
						case "2048":
							fileData["account"] = tokenData["sc"]["PageId"].ToObject<string>();
							break;
						case "4096":
							fileData["account"] = tokenData["sc"]["UserID"].ToObject<string>();
							break;
					}
					Executor exectorHelper = new Executor();
					string URL = exectorHelper.UploadFile(fileData, fileData["account"].ToObject<string>());
					fileData["msg"]["attach"]["url"] = URL;
					response.Data.Interaction = await exectorHelper.newInteraction(tokenData, fileData, InteractionType.digitalSignature);
					response.Data.URL = URL;

					response.Success = 1;
					response.Message = "OK";
					signature.SaveSignature(messageId, client);
				}
			}
			catch(Exception exception) 
			{
				response.Success = 0;
				response.Message = exception.Message;
				response.Data = null;
			}
			return Ok(response);
		}

		[HttpGet]
		[Route("Version")]
		public IActionResult Version()
		{
			StatusResponse response = new StatusResponse();
			response.Message = "v1.0.2";
			return Ok(response);
		}
	}
}