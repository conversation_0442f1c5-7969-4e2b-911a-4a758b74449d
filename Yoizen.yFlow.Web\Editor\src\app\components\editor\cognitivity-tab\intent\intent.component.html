<div class="app-intent">
  <div class="next intent">
    <div [ngClass]="{'hide': hasIntent()}" *ngIf="!readOnly">
      <input class="input" [ngClass]="{'invalid-state': !isValid()}" type="text"
             autocomplete="nomecompletesnadaaca"
             placeholder="{{'INTENT' | translate}}" [(ngModel)]="searchIntentString" LimitLength
             (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
      <div #intentPicker class="hide intent-selection-anchor">
        <div class="selector-container">
          <div class="scroll-area">
            <div class="intent-container" *ngFor="let int of Intents">
              <div class="intent-name" (click)="selectIntent(int); $event.stopPropagation();">{{int.name}}</div>
            </div>
            <div class="intent-container" *ngIf="EmptyIntentSet">
              <div class="empty-intent-set">{{'EMPTY_INTENT_SET' | translate}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="intent-selected" [ngClass]="{'hide': !hasIntent()}">
      <div class="intent-display">{{intent?.name}}</div>
      <div class="fa fa-unlink trash" (click)="deleteIntent()" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'INTENT_REMOVESELECTED' | translate }}"
           placement="top" container="body" tooltipClass="tooltip-trash"></div>
    </div>
  </div>
</div>
