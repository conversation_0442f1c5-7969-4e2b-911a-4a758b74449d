import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef, AfterViewInit, ViewChildren } from '@angular/core';
import { ModalService } from "../../../../services/Tools/ModalService"
import { EditorService } from "../../../../services/editor.service";
import { ServerService } from "../../../../services/server.service";
import * as XLSX from 'xlsx';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { StatusResponse } from '../../../../models/StatusResponse';
import { finalize } from 'rxjs/operators';
import { ErrorPopupComponent } from 'src/app/components/error-popup/error-popup.component';
import { TranslateService } from '@ngx-translate/core';
import { Tables } from 'src/app/models/Tables';
import { ShowTableHeadersComponent } from '../../popups/show-table-headers/show-table-headers.component';
import { TablesStatus } from 'src/app/models/TablesStatus';
import { TableHeader } from 'src/app/models/TableHeader';

@Component({
  selector: 'app-excel-import',
  templateUrl: './excel-import.component.html',
  styleUrls: ['./excel-import.component.scss']
})
export class ExcelImportComponent implements OnInit {
  uploading: boolean = false;
  @ViewChild('inputFile', { static: false }) inputFile: ElementRef;
  headers: any[] = [];
  data: any[] = [];
  flowtables: Tables[] = [];
  flowtablesStatus: TablesStatus[] = [];
  name: string;
  operationType: string;
  file: any;
  filename: string;
  extension: string;
  progressBarValue: number = 0;
  totalChunks: number = 0;
  timesList: number[] = [];
  timeLeft: string = '';
  tablenameStart: string = '';
  isDeleting: boolean = false;

  constructor(private modalService: ModalService,
    public editorService: EditorService,
    public serverService: ServerService,
    public translateService: TranslateService,
    private changeDetection: ChangeDetectorRef) { }

  ngOnInit() {
    this.flowtables = this.editorService.getFlowTables();
    this.flowtablesStatus = this.editorService.getFlowTablesStatus();

    this.editorService.onTablesStatusSet.subscribe((tablesStatus: TablesStatus[]) => {
      this.flowtablesStatus = tablesStatus;
    })
    const mCurrentFlow = this.editorService.getCurrentFlow();
    const flowId = mCurrentFlow.master_flow_id ? mCurrentFlow.master_flow_id : mCurrentFlow.id
    this.tablenameStart = `flow_${flowId}_`;
  }

  onChange(event, table, operation) {
    this.operationType = operation;
    const target: DataTransfer = <DataTransfer>(event.target);
    this.file = target.files[0];
    let name = target.files[0].name.split('.').slice(0, -1).join('.');
    var patt = new RegExp(/^[A-Za-z0-9\s]+$/g);
    var res = patt.test(name);

    if(res){
      this.filename = `flow_${this.editorService.getCurrentFlowId()}_${name.replace(/\s/g, "_").toLowerCase()}`
      this.extension = this.file.name.split('.').slice(-1)[0];

      if (target.files.length > 1) {
        this.inputFile.nativeElement.value = '';
      } else {
        if (target.files[0].name.match(/(.xls|.xlsx)/)) {
          this.readExcel(table);
        } else if (target.files[0].name.match(/(.csv)/)) {
          this.readCsv(table);
        } else {
          this.inputFile.nativeElement.value = '';
        }
      }
    }else{
      let inputs: any = {};
      inputs.Title = 'ERROR';
      inputs.Desc = this.translateService.instant('EXCELCSV_IMPORT_BAD_CHARACTERS_ALERT');
      this.modalService.init(ErrorPopupComponent, inputs, {});
    }
  }

  readCsv(table) {
    let csv;
    const reader: FileReader = new FileReader();

    reader.onload = (e: any) => {
      csv = (<string>reader.result).split(/\r\n|\n/);
    };

    reader.readAsBinaryString(this.file.slice(0, 1024 * 10))

    reader.onloadend = (e) => {
      this.onLoadEndData(csv[0].split(';'), table)
    }

  }

  readExcel(table) {
    let data;
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      /* read workbook */
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: 'binary' });

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      /* save data */
      data = XLSX.utils.sheet_to_json(ws);
    };

    reader.readAsBinaryString(this.file.slice(0, 1024 * 10))

    reader.onloadend = (e) => {
      this.onLoadEndData(Object.keys(data[0]), table)
    }

  }

  onLoadEndData(data, table) {
    if (!data.find(d => d.includes(" "))) {
      this.headers = data.map(n => new TableHeader(false, new VariableDefinition(n, null)));
      if (this.operationType == 'Update') {
        this.filename = table.table_name;
        this.updateTables(table);
        this.headers = [];
      }
    } else {
      this.headers = [];
    }
  }


  getVariablesTypes() {
    const value = VariableDefinition.variableType.filter(v =>
      v.value !== TypeDefinition.Any &&
      v.value !== TypeDefinition.Array &&
      v.value !== TypeDefinition.Object &&
      v.value !== TypeDefinition.Base64 &&
      v.value !== TypeDefinition.ByteArray &&
      v.value !== TypeDefinition.Timestamp &&
      v.value !== TypeDefinition.StringDate);
    return value;
  }

  uploadExcel() {
    var inputs: any = {};
    if (this.headers.some(x => x == null || x.variable == null || x.variable.Type == null)) {
      inputs.Title = 'ERROR';
      inputs.Desc = this.translateService.instant('EXCELCSV_IMPORT_SELECT_ALL_VARIABLES_ERROR');
      this.modalService.init(ErrorPopupComponent, inputs, {});
    }else if(!this.headers.some(x => x.index)){
      inputs.Title = 'ERROR';
      inputs.Desc = this.translateService.instant('EXCELCSV_IMPORT_SELECT_INDEX');
      this.modalService.init(ErrorPopupComponent, inputs, {});
    }else {
      let excelData = {
        name: this.filename,
        headers: this.headers,
        flowId: this.editorService.getCurrentFlowId(),
        statusId: 0,
        extension: this.extension
      }

      this.serverService.createExcelTable(excelData)
        .pipe(finalize(() => {
        }))
        .subscribe((status: StatusResponse) => {
          let table: Tables = new Tables(excelData.name, excelData.headers);
          this.editorService.addTable(table);

          let tablesStatus: TablesStatus = new TablesStatus(1, this.editorService.getCurrentFlowId(), 1, excelData.name, "PROCESSING"); // TODO: Set id
          this.editorService.addTableStatus(tablesStatus);

          if (status.success) {
            this.name = null;
            this.headers = [];
            this.uploadFile();
            this.inputFile.nativeElement.value = ''
          } else {
            inputs.Title = 'ERROR';
            inputs.Desc = inputs.Desc = this.translateService.instant('EXCELCSV_IMPORT_TABLE_ALREADY_EXISTS');
            this.modalService.init(ErrorPopupComponent, inputs, {});
          }
        },
          error => {
            console.log(error);
            inputs.Title = 'ERROR';
            inputs.Desc = this.translateService.instant('EXCELCSV_IMPORT_TABLE_ALREADY_EXISTS');
            this.modalService.init(ErrorPopupComponent, inputs, {});
          });
    }
  }

  uploadFile() {
    this.uploading = true
    this.uploadFileChunks(this.sliceFile());
  }

  sliceFile() {
    const CHUNKS_SIZE = 1024 * 50; //50KB 50KB Section size
    let fileChunks = [];
    let index = 0;
    for (let cur = 0; cur < this.file.size; cur += CHUNKS_SIZE) {
      fileChunks.push({
        hash: index++,
        chunk: this.file.slice(cur, cur + CHUNKS_SIZE),
      });
    }
    this.totalChunks = fileChunks.length;
    return fileChunks;
  }

  updateProgressBarValue(finishedChunks: number) {
    this.progressBarValue = parseInt(((finishedChunks / this.totalChunks) * 100).toString());
  }

  progressBar() {
    return this.progressBarValue + '%';
  }

  uploadFileChunks = async (list: any[]) => {
    this.updateProgressBarValue((this.totalChunks - list.length));
    if (list.length === 0) {
      this.serverService.mergePersonalizedTable(this.filename, this.extension)
        .subscribe((status: StatusResponse) => {
          this.progressBarValue = 0;
          this.uploading = false;
          if (this.operationType == 'Update') {
            for(let i = 0; i < this.flowtablesStatus.length; i++) {
              if(this.flowtablesStatus[i].table_name == this.filename) {
                this.flowtablesStatus[i].status = "PROCESSING";
              }
            }
            this.changeDetection.detectChanges();
            this.editorService.setFlowTablesStatus(this.flowtablesStatus);
          }
        });
      return;
    }

    let item = list.shift()
    const formData = new FormData()
    formData.append("filename", this.filename);
    formData.append("hash", item.hash);
    formData.append("chunk", item.chunk);

    let tStart = new Date().getTime();
    this.serverService.uploadPersonalizedTable(formData)
      .subscribe((status: StatusResponse) => {
        let tEnd = new Date().getTime();
        this.timesList.push(tEnd - tStart);
        this.getTimeLeft(50, list);
        if (status.success) {
          this.uploadFileChunks(list)
        } else {
          list.push(item)
          this.uploadFileChunks(list)
        }
      });
  }


  updateTables(table) {
    var inputs: any = {};
    let t = this.flowtables.find(x => x.name === table.table_name);
    if (t === null || t === undefined) {
      inputs.Title = 'ERROR';
      inputs.Desc = this.translateService.instant('TABLES_NOT_MATCH');
      this.modalService.init(ErrorPopupComponent, inputs, {});
    } else if (this.headers.map((header) => header.variable.Name).toString() === t.headers.map((header) => header.variable.Name).toString()) {
      let excelData = {
        tableStatusId: table.id,
        tableName: table.table_name
      }
      this.serverService.updateExcelData(excelData)
        .subscribe((status: StatusResponse) => {
          if (status.success) {
            this.uploadFile();
          } else {
            inputs.Title = 'ERROR';
            inputs.Desc = status.message;
            this.modalService.init(ErrorPopupComponent, inputs, {});
          }
        },
          error => {
            if (error.error != null) {
              inputs.Title = 'ERROR';
              inputs.Desc = error.error.message;
            }
            this.modalService.init(ErrorPopupComponent, inputs, {});
          });
    } else {
      inputs.Title = 'ERROR';
      inputs.Desc = this.translateService.instant('TABLES_NOT_MATCH');
      this.modalService.init(ErrorPopupComponent, inputs, {});
    }
  }

  toggleIsDeleting() {
    this.isDeleting = !this.isDeleting;
  }

  deleteTable(table) {
    const name = table.table_name;
    const id = table.id;

    if (table.status === "FINISHED") {
      this.serverService.deleteExcelTable(id)
        .subscribe((status: StatusResponse) => {
          if (status.success) {
            this.flowtables = this.flowtables.filter(t => t.name !== name);
            this.flowtablesStatus = this.flowtablesStatus.filter(t => t.table_name !== name);
            this.editorService.setFlowTables(this.flowtables);
            this.editorService.setFlowTablesStatus(this.flowtablesStatus);
          }
        })
    }
  }

  seeHeaders(table) {
    let t = this.flowtables.find(x => x.name === table);
    let table_status = this.flowtablesStatus.find(x => x.table_name === t.name)
    this.modalService.init(ShowTableHeadersComponent, {
      id: table_status.id, name: table, headers: t.headers, setHeaders: (name, headers) => {
        this.flowtables.find(f => f.name === name).headers = JSON.parse(headers);
        this.editorService.setFlowTables(this.flowtables);
      }
    }, {});
  }

  getTableName(table_name){
    return table_name.split(this.tablenameStart)[1].replaceAll('_', ' ')
  }

  onChangeIndex(event) {
  }

  isValidType(type) {
    return (type === TypeDefinition.Number || type === TypeDefinition.Text || type === TypeDefinition.Bool)
  }

  //Calcular tiempo que falta para que se termine la subida
  getTimeLeft(cutting, chunksLeft) {
    let auxList = this.timesList.slice(-cutting);
    const avgTime = auxList.reduce((a, b) => a + b, 0) / auxList.length;
    const auxTimeLeft = (avgTime * chunksLeft.length) / 1000;
    if(auxTimeLeft < 60) {
      this.timeLeft = `Quedan ${auxTimeLeft.toFixed(0)} segundos`;
    }else if(auxTimeLeft < 3600) {
      this.timeLeft = `Quedan ${(auxTimeLeft/60).toFixed(0)} minutos`;
    }else{
      this.timeLeft = `Quedan ${(auxTimeLeft/3600).toFixed(0)} horas`;
    }
  }

  refreshTables() {
    const mCurrentFlow = this.editorService.getCurrentFlow();
    const flowId = mCurrentFlow.master_flow_id ? mCurrentFlow.master_flow_id : mCurrentFlow.id;
    this.serverService.getExcelTablesStatus(flowId)
      .subscribe((status: StatusResponse) => {
        this.editorService.setFlowTablesStatus(status.data);
      })
  }
}
