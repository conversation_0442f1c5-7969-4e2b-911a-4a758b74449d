import { Model, DataTypes, Sequelize } from 'sequelize';

// Definición de la interfaz de atributos
interface CaseContextBase {
    case_id: string | number;
    json: object;
}

// Clase extendiendo de Sequelize Model
export class CaseContextSequelize extends Model<CaseContextBase> {
    declare case_id: number;
    declare json: object;

    static initialize(sequelizeInstance: Sequelize) {
        CaseContextSequelize.init({
            case_id: {
                type: new DataTypes.BIGINT,
                primaryKey: true,
                autoIncrement: false
            },
            json: new DataTypes.STRING
        }, {
            sequelize: sequelizeInstance,
            modelName: 'case_contexts',
            tableName: 'case_contexts',
            timestamps: true
        });
    }
}

export class CaseContextStringSequelize extends Model<CaseContextBase> {
    declare case_id: string;
    declare json: object;

    static initialize(sequelizeInstance: Sequelize) {
        CaseContextStringSequelize.init({
            case_id: {
                type: new DataTypes.STRING,
                primaryKey: true,
                autoIncrement: false
            },
            json: new DataTypes.STRING
        }, {
            sequelize: sequelizeInstance,
            modelName: 'case_contexts_string',
            tableName: 'case_contexts_string',
            timestamps: true
        });
    }
}

export class CaseContext implements CaseContextBase {
    declare case_id: string | number;
    declare json: object;
}
