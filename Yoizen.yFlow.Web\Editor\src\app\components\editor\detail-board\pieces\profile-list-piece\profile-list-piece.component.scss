@import "_variables";
@import "_mixins";

.profile-list {
  background-color: #fff;
  min-width: 500px;

  .option {
    width: 45%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 0;
    }
  }

  .profile-list-item {
    display: flex;
    flex-direction: row;
  }

  .add-profile-list {
    margin-top: 10px;

    gap: 5px;

    &>.title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    .add-profile-list-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .add-profile-list-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        margin-bottom: 5px;
        height: 40px;
        min-height: 40px;

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .trash {
          width: 30px;
          padding: 10px 0px;
          display: flex;
          align-items: center;
          justify-content: center;

          &>div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            &>div {
              @include trashOver;
            }
          }
        }
      }

    }

    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin-top: 5px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }

}
