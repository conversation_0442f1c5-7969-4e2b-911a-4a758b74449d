@import "_variables";
@import "_mixins";

.decrypt<PERSON>iece {
  background-color: #fff;
  min-width: 500px;
  position: relative;

  textarea {
    border-radius: 10px;
    width: 100%;
    margin-bottom: 10px;
    font-weight: normal;
    min-width: 150px;
    min-height: 150px;
  }

  .card-title {
    margin-bottom: 10px;
  }

  .definition {
    padding: 10px;

    .option {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      .input {
        margin: 0 4px;
        width: 100%;
      }

    }
  }

}

.replacement-row {
  display: flex;
  margin-bottom: 10px;
  flex-direction: row;
  align-items: center;
}

.bussiness-value {
  margin-right: 10px;
  box-sizing: border-box;

  .title {
    margin-right: 5px; // Espacio entre el título y el input
  }
}



.add {
  color: $linkActionColor;
  text-transform: uppercase;
  font-size: 12px;
  margin: 20px 10px;
  width: max-content;
  cursor: pointer;

  &:hover {
    color: lighten($linkActionColor, 10%);
  }
}

.trash {
  cursor: pointer;
}