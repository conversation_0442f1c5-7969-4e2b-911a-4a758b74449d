<div class="condition-group depth-{{currentDepth}}" [ngClass]="{ 'invalid': !conditionGroup.isValid() }">
  <div class="header">
    <select class="select" name="" id=""
            [(ngModel)]="conditionGroup.comparisonOperator"
            [disabled]="readOnly"
            *ngIf="conditionGroup.conditionList.length > 1">
      <option *ngFor="let op of operators" [value]="op">{{ op | translate }}</option>
    </select>
    <div class="button-area">
      <div class="add-element-button" (click)="onAddGroup()" *ngIf="currentDepth < 4 && !readOnly">
        <span class="fa fa-plus"></span> {{ 'ADD_GROUP' | translate }}
      </div>
      <div class="add-element-button" (click)="onAddSingle()" *ngIf="!readOnly">
        <span class="fa fa-plus"></span> {{ 'ADD_CONDITION' | translate }}
      </div>
    </div>
  </div>
  <div class="conditions">
    <div *ngFor="let condition of conditionGroup.conditionList; let index=index;" class="conditionEntry">
      <app-condition-group *ngIf="isGroup(condition)"
                           [currentDepth]="currentDepth + 1"
                           [totalDepth]="totalDepth"
                           [readOnly]="readOnly"
                           [conditionGroup]="condition"></app-condition-group>
      <app-single-condition *ngIf="isSingle(condition)"
                            [readOnly]="readOnly"
                            [singleCondition]="condition"></app-single-condition>
      <div class="trash" (click)="onDelete(index); $event.stopPropagation()" *ngIf="!readOnly"
           ngbTooltip="{{ (isGroup(condition) ? 'CONDITION_GROUP_DELETE' : 'CONDITION_SINGLE_DELETE') | translate }}"
           data-toggle="tooltip" placement="left" container="body" tooltipClass="tooltip-trash-left">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>
  </div>
</div>
