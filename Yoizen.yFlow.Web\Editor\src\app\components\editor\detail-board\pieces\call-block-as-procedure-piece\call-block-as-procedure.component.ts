import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {JumpToBlockPiece} from "../../../../../models/pieces/JumpToBlockPiece";
import {CallBlockAsProcedurePiece} from "../../../../../models/pieces/CallBlockAsProcedurePiece";

@Component({
  selector: 'app-call-block-as-procedure-piece',
  templateUrl: './call-block-as-procedure.component.html',
  styleUrls: ['./call-block-as-procedure.component.scss']
})
export class CallBlockAsProcedureComponent extends BasePieceVM implements OnInit {

  model: CallBlockAsProcedurePiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

   ngOnInit() {
    this.model = this.context as CallBlockAsProcedurePiece;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.BlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.BlockId = null;
  }
}
