import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EntitySelectorInputComponent } from './entity-selector-input.component';

describe('EntitySelectorInputComponent', () => {
  let component: EntitySelectorInputComponent;
  let fixture: ComponentFixture<EntitySelectorInputComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EntitySelectorInputComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EntitySelectorInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
