import { Moment } from "moment";
import { DailyByAbandonedCase } from "./DailyByAbandonedCase";
import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { DailyByBlocks } from "./DailyByBlocks";
import { DailyByBlocksSequence } from "./DailyByBlocksSequence";
import { DailyByCommands } from "./DailyByCommands";
import { DailyByDefaultAnswer } from "./DailyByDefaultAnswer";
import { DailyByDerivationKey } from "./DailyByDerivationKey";
import { DailyByFlow } from "./DailyByFlow";
import { DailyByGroups } from "./DailyByGroups";
import { DailyByGroupsSequence } from "./DailyByGroupsSequence";
import { DailyByIntegrations } from "./DailyByIntegrations";
import { DailyByStatisticEvent } from "./DailyByStatisticEvent";
import { SystemStatusType } from "../SystemStatus";
import { Daily } from "./Daily";

export class DailyFactory {

    static CreateDaily(type: DailyInfoTypes, datetime: Moment) {
        switch (type) {
            case DailyInfoTypes.Normal:
                return new Daily(datetime);
            case DailyInfoTypes.AbandonedCases:
                return new DailyByAbandonedCase(datetime);
            case DailyInfoTypes.Blocks:
                return new DailyByBlocks(datetime);
            case DailyInfoTypes.BlocksSequence:
                return new DailyByBlocksSequence(datetime);
            case DailyInfoTypes.Commnads:
                return new DailyByCommands(datetime);
            case DailyInfoTypes.DefaultAnswers:
                return new DailyByDefaultAnswer(datetime);
            case DailyInfoTypes.DerivationKey:
                return new DailyByDerivationKey(datetime);
            case DailyInfoTypes.ByFlow:
                return new DailyByFlow(datetime);
            case DailyInfoTypes.Groups:
                return new DailyByGroups(datetime);
            case DailyInfoTypes.GroupSequence:
                return new DailyByGroupsSequence(datetime);
            case DailyInfoTypes.Integrations:
                return new DailyByIntegrations(datetime);
            case DailyInfoTypes.StatisticEvent:
                return new DailyByStatisticEvent(datetime);
            default:
                throw new Error('Invalid totalizer type');
        }
    }

    static CreateDailyBySystemStatus(type: SystemStatusType, datetime: Moment) {
        switch (type) {
            case SystemStatusType.LastIntervalDaily:
                return new Daily(datetime);
            case SystemStatusType.LastIntervalByAbandonedCase:
                return new DailyByAbandonedCase(datetime);
            case SystemStatusType.LastIntervalDailyByBlocks:
                return new DailyByBlocks(datetime);
            case SystemStatusType.LastIntervalDailyByBlocksSequence:
                return new DailyByBlocksSequence(datetime);
            case SystemStatusType.LastIntervalDailyByCommands:
                return new DailyByCommands(datetime);
            case SystemStatusType.LastIntervalDailyByDefaultAnswers:
                return new DailyByDefaultAnswer(datetime);
            case SystemStatusType.LastIntervalDailyByDerivationKey:
                return new DailyByDerivationKey(datetime);
            case SystemStatusType.LastIntervalDailyByFlow:
                return new DailyByFlow(datetime);
            case SystemStatusType.LastIntervalDailyByGroups:
                return new DailyByGroups(datetime);
            case SystemStatusType.LastIntervalDailyByGroupsSequence:
                return new DailyByGroupsSequence(datetime);
            case SystemStatusType.LastIntervalDailyByIntegrations:
                return new DailyByIntegrations(datetime);
            case SystemStatusType.LastIntervalDailyByStatisticEvent:
                return new DailyByStatisticEvent(datetime);
            default:
                throw new Error('Invalid totalizer type');
        }
    }
}