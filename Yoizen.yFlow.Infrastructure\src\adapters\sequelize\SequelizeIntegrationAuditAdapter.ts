import { IIntegrationAuditPort } from "../../ports/IIntegrationAuditPort";
import { IntegrationAudit, IntegrationAuditSequelize } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/IntegrationAudit";
import { DataTypes, Sequelize, Options as SequelizeOptions, Dialect as SequelizeDialect } from "sequelize";
import { dbIntegrationsAudit } from "../../../../Yoizen.yFlow.Helpers/src/ConfigDbIntegrationsAudit";
import { config as configAux } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import moment from "moment";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeIntegrationAuditAdapter implements IIntegrationAuditPort {
    private sequelize: Sequelize;
    public constructor() {
        if (dbIntegrationsAudit.enable) {
            const config: SequelizeOptions = {
                dialect: dbIntegrationsAudit.dialect as SequelizeDialect,
                host: dbIntegrationsAudit.host,
                port: dbIntegrationsAudit.port,
                database: dbIntegrationsAudit.name,
                username: dbIntegrationsAudit.username,
                password: dbIntegrationsAudit.password,
                ssl: dbIntegrationsAudit.ssl,
                logging: configAux.isDebug ? function (str) {
                    logger.trace(str);
                } : false,
                define: {
                    underscored: true
                },
            };

            if (config.dialect === 'mssql') {
                config.dialectOptions = {
                    options: {
                        requestTimeout: dbIntegrationsAudit.dbtimeout,
                        cancelTimeout: dbIntegrationsAudit.dbcanceltimeout
                    },
                    ssl: {
                        require: dbIntegrationsAudit.ssl,
                    }
                }
            } else if (config.dialect === 'mysql') {
                config.dialectOptions = {
                    connectTimeout: dbIntegrationsAudit.dbtimeout,
                    ssl: {
                        require: dbIntegrationsAudit.ssl,
                    }
                }
            }

            if (!dbIntegrationsAudit.ssl) {
                // @ts-ignore: Lo quito ya que no encontré otra forma elegante de quitar el warning
                delete config.dialectOptions.ssl;
            }

            if (typeof (dbIntegrationsAudit.poolMaxSize) === 'number') {
                config.pool = {
                    max: dbIntegrationsAudit.poolMaxSize,
                    min: 0,
                    acquire: 30000,
                    idle: 10000
                };
            }

            this.sequelize = new Sequelize(config);
            IntegrationAuditSequelize.init({
                type: DataTypes.INTEGER,
                messageId: {
                    type: new DataTypes.STRING,
                    field: 'message_id'
                },
                caseId: {
                    type: new DataTypes.STRING,
                    field: 'case_id'
                },
                socialUserId: {
                    type: new DataTypes.STRING,
                    field: 'social_user_id'
                },
                flowId: {
                    type: new DataTypes.STRING,
                    field: 'flow_id'
                },
                statusCode: {
                    type: new DataTypes.NUMBER,
                    field: 'status_code'
                },
                wait: DataTypes.BIGINT,
                dns: DataTypes.BIGINT,
                tcp: DataTypes.BIGINT,
                tls: DataTypes.BIGINT,
                request: DataTypes.BIGINT,
                firstByte: {
                    type: new DataTypes.BIGINT,
                    field: 'first_byte'
                },
                download: DataTypes.BIGINT,
                total: DataTypes.BIGINT,
                yFlow: {
                    type: new DataTypes.BIGINT,
                    field: 'yflow'
                },
                integrationId: {
                    type: new DataTypes.STRING,
                    field: 'integration_id'
                },
                integrationName: {
                    type: new DataTypes.STRING,
                    field: 'integration_name'
                },
                timestamp: {
                    type: new DataTypes.BIGINT,
                    allowNull: false,
                    defaultValue: () => Math.floor(moment().valueOf() / 1000),
                },
            }, {
                sequelize: this.sequelize,
                modelName: 'integrations_audit',
                tableName: 'integrations_audit',
                timestamps: true
            });
        } else {
            logger.info(`No se utiliza la base IntegrationsAudit`);
        }
    }

    async connect(): Promise<void> {
        try {
            if (!dbIntegrationsAudit.enable)
                return;

            await this.sequelize.authenticate();
            logger.info('Database Connection has been established successfully  - DbIntegrationsAudit');
        }
        catch (err) {
            logger.error({ error: err }, 'Unable to connect to the database - DbIntegrationsAudit:');
            process.exit(9);
        }
    }

    async Save(integrationAudit: IntegrationAudit): Promise<IntegrationAudit> {
        try {
            if (!dbIntegrationsAudit.enable)
                return null;

            return await IntegrationAuditSequelize.create({
                ...integrationAudit
            });
        } catch (error) {
            logger.error({ error: error }, `Error guardando la auditoría de integración '${integrationAudit.type}'`);
            return null;
        }
    }
}