import { ISystemStatusPort } from "../../ports/ISystemStatusPort";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { TableHints } from "sequelize";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeSystemStatusAdapter implements ISystemStatusPort {

    async Get(type: SystemStatusType): Promise<SystemStatus> {
        try {
            return await SystemStatus.findOne({
                where: {
                    type: type
                },
                order: [
                    ['date', 'DESC']
                ],
                //@ts-ignore
                tableHint: TableHints.NOLOCK
            });
        } catch (error) {
            logger.error({ error: error }, `Error al obtener el estado del sistema:`);
        }
    }

    async Save(status: SystemStatus): Promise<SystemStatus> {
        try {
            return await status.save();
        } catch (error) {
            throw new Error(`<PERSON><PERSON>r actualizando la fecha del system_status '${status.type}': ${error}`);
        }
    }

}