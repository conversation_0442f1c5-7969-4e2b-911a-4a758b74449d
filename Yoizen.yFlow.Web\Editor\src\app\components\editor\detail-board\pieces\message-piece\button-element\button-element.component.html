<div class="button-content" [ngClass]="{ 'invalid': !isValid() }">
  <div class="addName" (click)="onClick(); $event.stopPropagation();">
    <div class="trash" (click)="deleteElement()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'BUTTON_DELETE_TEXT' | translate }}" placement="right" container="body"
         tooltipClass="tooltip-trash-right">
      <span class="fa fa-trash-alt"></span>
    </div>
    <div class="name" *ngIf="withName()">
      <span class="fa fa-location-arrow icon" *ngIf="Model.Type === buttonTypes.Location"></span>
      <span class="text" [innerHTML]="getButtonText()"></span>
    </div>
    <div class="name-invalid" *ngIf="!withName()" [innerHTML]="getButtonText()"></div>
    <div class="block" *ngIf="withRedirect()">
      <span class="fa fa-table"></span> {{ getBlockName() }}
      <div class="goto" (click)="goToBlock(); $event.stopPropagation();" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'GO_TO_BLOCK' | translate }}" placement="bottom" container="body"
           tooltipClass="tooltip-gotoblock">
        <span class="fas fa-external-link-alt"></span>
      </div>
    </div>
    <div class="block" *ngIf="withLocation()">
      <span class="fa fa-table"></span> {{ getBlockName() }}
      <div class="goto" (click)="goToBlock(); $event.stopPropagation();" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'GO_TO_BLOCK' | translate }}" placement="bottom" container="body"
           tooltipClass="tooltip-gotoblock">
        <span class="fas fa-external-link-alt"></span>
      </div>
    </div>
    <div class="block" *ngIf="withLocationWithMap()">
      <span class="fa fa-table"></span> {{ getBlockName() }}
      <div class="goto" (click)="goToBlock(); $event.stopPropagation();" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'GO_TO_BLOCK' | translate }}" placement="bottom" container="body"
           tooltipClass="tooltip-gotoblock">
        <span class="fas fa-external-link-alt"></span>
      </div>
    </div>
    <div class="url" *ngIf="withUrl()"><span class="fa fa-link"></span> {{ Model.Url }}</div>
    <div class="dial" *ngIf="withDial()"><span class="fa fa-phone"></span> {{ Model.PhoneNumber }}</div>
    <div class="send-location" *ngIf="withSendLocation()"><span class="fa fa-map"></span> {{ Model.Latitude }},{{ Model.Longitude }}</div>
    <div class="login" *ngIf="withAuthLoginUrl()"><span class="fa fa-certificate"></span> {{ Model.AuthLoginUrl}}</div>
    <div class="stats" *ngIf="showStats && withRedirect()">
      <div class="title"><span class="fa fa-chart-line"></span>{{ 'BUTTON_STATS_TITLE' | translate }}</div>
      <div class="stats-empty" *ngIf="stats === null">
        <div class="alert alert-info">
          {{ 'BUTTON_STATS_DESC_EMPTY' | translate }}
        </div>
      </div>
      <div class="stats-info" *ngIf="stats !== null">
        <ul>
          <li>
            <span class="title">{{ 'BLOCK_STATS_FIRSTEXECUTION' | translate }}:</span>
            <span class="value">{{ stats.firstExecution | dateFormat: 'LLL' }}</span>
          </li>
          <li>
            <span class="title">{{ 'BLOCK_STATS_LASTEXECUTION' | translate }}:</span>
            <span class="value">{{ stats.lastExecution | dateFormat: 'LLL' }}</span>
          </li>
          <li>
            <span class="title">{{ 'BLOCK_STATS_COUNT' | translate }}:</span>
            <span class="value">{{ stats.count }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="invalid">
    <span class="fa fa-exclamation-triangle"></span>
  </div>
  <app-add-button-piece #buttonDetail class="button-details"
                        (onClose)="closePopup()"
                        [Model]="Model"
                        *ngIf="!HideDetail"
                        [customVariableList]="customVariableList"
                        [AllowedButtonTypes]="AllowedButtonTypes"
                        [IsIceBreaker]="IsIceBreaker"
                        [MaxLength]="MaxLength"
                        [EmojisAllowed]="EmojisAllowed"
                        [readOnly]="readOnly"></app-add-button-piece>
</div>
