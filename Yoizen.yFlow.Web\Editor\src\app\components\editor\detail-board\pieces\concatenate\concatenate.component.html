<div class="concatenate-piece card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sitemap"></span> {{ 'CARD_CONCATENATE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_CONCATENATE_INFO' | translate }}
  </div>
  <div class="sourcearray">
    <span class="title">{{'CONCATENATE_SOURCE_ARRAY' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="InputVariableData"
      (setVariable)="setInputVariable($event)"
      [validator]="validateVariable"
      [readOnly]="readOnly"
      [typeFilters]="variableFilter"
      [includeImplicit]="true">
    </app-variable-selector-input>
  </div>
  <div class="header">
    <label>{{'CONCATENATE_HEADER' | translate}}</label>
    <app-input-with-variables
      [isTextArea]="true"
      [wideInput]="true"
      [placeholder]="''"
      [(value)]="model.Header"
      [validator]="getValidator()"
      [disabled]="readOnly"
      [extendedStyles]="{'height': '50px', 'min-height': '50px', 'max-height': '50px'}"
      ></app-input-with-variables>
  </div>
  <div class="body">
    <label>{{'CONCATENATE_TEMPLATE' | translate}}</label>
    <div class="implicit-variables">
      <div class="variables-info">{{'CONCATENATE_VARIABLELIST' | translate}}</div>
      <div class="variables-table">
        <div class="variables-header">
          <div>{{'NAME' | translate}}</div>
          <div>{{'DESCRIPTION' | translate}}</div>
        </div>
        <div class="variable-row" *ngFor="let variable of customVariables">
          <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
          <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
        </div>
      </div>
    </div>
    <app-input-with-variables
      [isTextArea]="true"
      [wideInput]="true"
      [placeholder]="''"
      [validator]="getValidator()"
      [variableFinder]="searchForVariable.bind(this)"
      [extendedStyles]="{'height': '100px', 'min-height': '100px', 'max-height': '400px'}"
      [customVariableList]="customVariables"
      [JoinCustomVariable]="true"
      [disabled]="readOnly"
      [(value)]="model.Template"></app-input-with-variables>
  </div>

  <div class="footer">
    <label>{{'CONCATENATE_FOOTER' | translate}}</label>
    <app-input-with-variables
      [isTextArea]="true"
      [wideInput]="true"
      [placeholder]="''"
      [validator]="getValidator()"
      [extendedStyles]="{'height': '50px', 'min-height': '50px', 'max-height': '50px'}"
      [disabled]="readOnly"
      [(value)]="model.Footer"></app-input-with-variables>
  </div>
  <div class="saveinto">
    <span class="title">{{'SAVE_INTO' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="OutputVariableData"
      (setVariable)="setOutputVariable($event)"
      [validator]="validateVariable"
      [canSelectConstants]="false"
      [readOnly]="readOnly"
      [typeFilters]="outputVariableFilter">
    </app-variable-selector-input>
  </div>
</div>
