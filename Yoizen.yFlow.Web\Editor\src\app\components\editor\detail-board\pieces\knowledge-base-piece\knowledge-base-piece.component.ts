import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { EditorService } from 'src/app/services/editor.service';
import { DbQueryPiece } from 'src/app/models/pieces/DbQueryPiece';
import { KnowledgeBasePiece } from 'src/app/models/pieces/KnowledgeBasePiece';
import { Category } from 'src/app/models/cognitivity/Category';

@Component({
  selector: 'app-knowledge-base-piece',
  templateUrl: './knowledge-base-piece.component.html',
  styleUrls: ['./knowledge-base-piece.component.scss']
})
export class KnowledgeBasePieceComponent extends BasePieceVM implements OnInit {
  model: KnowledgeBasePiece;
  selectedCategory: number = -1;
  categories: Category[] = [];

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as KnowledgeBasePiece;
    this.categories = this.editorService.getCategories();
    console.log(this.categories)

    if (this.model.CategoryId != -1) {
      this.selectedCategory = this.categories.find(c => c.Id === this.model.CategoryId).Id;
    }

  }

  onSelectCategory(event) {
    this.model.CategoryId = event;
  }

}
