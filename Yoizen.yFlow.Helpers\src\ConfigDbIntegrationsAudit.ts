import { DbConfigBase } from "./ConfigDbBase";
import { isEnvironmentVariableValid, parseToInt, parseToBoolean } from "./Helpers";
import { logger } from "./Logger";

export class DbIntegrationsAudit extends DbConfigBase {
}

export const dbIntegrationsAudit = new DbIntegrationsAudit()
dbIntegrationsAudit.host = process.env.dbintegrationsaudithost;
dbIntegrationsAudit.port = parseToInt(process.env.dbintegrationsauditport);
dbIntegrationsAudit.name = process.env.dbintegrationsauditname
dbIntegrationsAudit.username = process.env.dbintegrationsauditusername
dbIntegrationsAudit.password = process.env.dbintegrationsauditpassword;
dbIntegrationsAudit.dialect = process.env.dbintegrationsauditdialect;
dbIntegrationsAudit.dbtimeout = parseToInt(process.env.dbintegrationsauditdbtimeout, 30000)
dbIntegrationsAudit.dbcanceltimeout = parseToInt(process.env.dbintegrationsauditdbcanceltimeout, 5000);
dbIntegrationsAudit.ssl = parseToBoolean(process.env.dbintegrationsauditssl, false)
dbIntegrationsAudit.enable = true;
dbIntegrationsAudit.connectionString = process.env.dbintegrationAuditConnectionString;
dbIntegrationsAudit.poolMaxSize = parseToInt(process.env.dbintegrationAuditMaxPoolSize, 5);

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(dbIntegrationsAudit.dialect)) {
        logger.error(`Faltan datos de conexión al Sql de dbintegrationsauditdialect - Si no va usar esta base de datos, pase la palabra 'disabled'`);
        process.exit(9);
    }

    dbIntegrationsAudit.dialect = dbIntegrationsAudit.dialect.toLowerCase();

    if (dbIntegrationsAudit.dialect !== 'disabled') {

        dbIntegrationsAudit.parseConnectionString();

        if (!(isEnvironmentVariableValid(dbIntegrationsAudit.host) &&
            isEnvironmentVariableValid(dbIntegrationsAudit.port) &&
            isEnvironmentVariableValid(dbIntegrationsAudit.name) &&
            isEnvironmentVariableValid(dbIntegrationsAudit.username) &&
            isEnvironmentVariableValid(dbIntegrationsAudit.password) &&
            isEnvironmentVariableValid(dbIntegrationsAudit.dialect))) {
            logger.error(`Faltan datos de conexión al Sql de IntegrationsAudit`);
            process.exit(9);
        }
    }
    else {
        dbIntegrationsAudit.enable = false;
    }
}

validateAndFormatConfig();