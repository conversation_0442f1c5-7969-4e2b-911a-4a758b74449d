@import "_variables";

.update-case {
  background-color: #fff;
  width: 560px;

  .close-case, .mark-as-pending-reply {
    .alert-info {
      color: #0c5460;
      background-color: #d1ecf1;
      border-color: #bee5eb;
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .option {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .mark-as-pending-reply {
    border-top: $cardSeparator;

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }
}
