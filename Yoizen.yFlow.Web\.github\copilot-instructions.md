# yFlow Project - AI Development Guide

## Project Overview

yFlow is a conversational bot flow designer and runtime system consisting of:
- **Backend**: Node.js/Express REST API with Sequelize ORM
- **Frontend**: Angular 8 flow editor with drag-and-drop interface
- **Database**: SQL Server/MySQL with comprehensive analytics and reporting
- **Real-time**: Socket.io for collaborative editing and live updates

## Architecture & Key Components

### Backend Structure (`/`)
- `app.js` - Express application entry point with middleware configuration
- `routes/` - REST API endpoints organized by feature (flows, users, statistics, etc.)
- `models/` - Sequelize models with comprehensive relationships and custom methods
- `helpers/` - Utilities for database, authentication, logging (Pino), Redis, validation
- `services/` - Background services and business logic
- `migrations/` - Database schema versioning

### Frontend Structure (`/Editor/`)
- Angular 8 application with drag-and-drop flow editor
- `src/app/components/editor/` - Main flow design interface
- Real-time collaboration via Socket.io
- Multi-language support (ES/EN/PT)

### Database Models Pattern
All models follow consistent patterns:
- Historical analytics models in `models/historical/` (dailyByFlow, dailyByBlocks, etc.)
- Detailed tracking models in `models/detailed/`
- Base classes provide common functionality (`dailyBase.js`, `detailedBase.js`)
- Models include extensive aggregation methods and Excel/CSV export capabilities

## Development Workflows

### Build & Development
```bash
# Backend development
npm run dev          # Start with preload.js and debugging
npm run debug        # Debug mode on port 9229

# Frontend development  
cd Editor
ng serve            # Development server
ng build            # Production build
```

### Production Build
```bash
# Full production build
grunt prod          # Builds both backend and frontend
webpack             # Backend only
cd Editor && ng build --prod  # Frontend only
```

### Database Operations
- Use `db-migrate` for schema changes
- Models auto-sync in development
- Connection string parsing supports both SQL Server and MySQL formats
- Always use transactions for multi-step operations

## Environment Configuration

### Development Setup
- Create `.env.{username}` for personal development settings
- Use `node dev-setup.js` for interactive environment configuration
- Check configuration with `node check-env.js`

### Critical Environment Variables
```bash
# Database
yFlowConnectionString=Server=host,port;Database=db;User Id=user;Password=pass
dbdialect=mssql|mysql

# Core Services  
executorUrl=http://executor-service
ySocialUrl=http://ysocial-api
storageYFlowPath=/storage/path

# Features
multiCompany=true|false
standAlone=true|false
hostInsideIIS=true|false
```

## Project-Specific Patterns

### Model Conventions
- Use `TableHints.NOLOCK` for read-heavy analytics queries
- Implement streaming for large datasets (`request.stream = true`)
- Custom aggregation methods named like `getGroupByDay()`, `getGroupByIntegration()`
- Excel export methods return workbook objects: `generateExcel()`

### Socket.io Integration
- Authentication via JWT tokens in socket events
- Flow collaboration: `user_request` events for connect/disconnect/save
- User session tracking with automatic logout detection
- Grace period handling for connection drops

### Security & Multi-tenancy
- JWT-based authentication with `express-jwt`
- Virtual directory support via `virtualDirPath`
- Multi-company isolation when `multiCompany=true`
- Licensed customer validation in `licensedCustomers.json`

### Error Handling
- Use `ErrorHandler` wrapper for route controllers
- Pino structured logging throughout
- Comprehensive validation in `helpers/validationEnvironment.js`
- Database connection retry logic with process.exit(9) on failure

### Build System Quirks
- **Important**: Disable webpack mangling to prevent runtime errors
- Modify `Editor/ToReplace/common.js` with `uglifyOptions: {mangle: false}`
- Dual build system: Webpack for backend, Angular CLI for frontend
- Grunt orchestrates full production builds with file injection

## Integration Points

### External Services
- **ySocial**: Social media platform integration (when `standAlone=false`)
- **Cognitive Services**: AI/ML capabilities via external APIs
- **WhatsApp HSM**: Template message management
- **Azure Blob Storage**: File storage (configurable)

### Data Flow Patterns
- Flow definitions stored as compressed JSON blobs in `flow_versions`
- Statistics aggregated in real-time to historical tables
- Excel/CSV reports generated on-demand with streaming
- Redis for session storage and caching

## Common Debugging Areas

- **Socket disconnections**: Check authentication and grace period handling
- **Build failures**: Verify Angular CLI version and webpack config
- **Database timeouts**: Review connection pool settings and query complexity  
- **Multi-company issues**: Validate `licensedCustomers.json` and routing
- **Environment loading**: Use `check-env.js` and verify `.env.{username}` files

## Testing & Deployment

- Use `npm test` for backend unit tests
- Frontend testing via Karma/Jasmine: `ng test`
- Production deployment via `deployProduction.bat` or Docker
- Health check endpoint: `/api/health_check`
