<div class="geocoder-google card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-map-marker-alt"></span> {{ 'CARD_GEOCODER_GOOGLE_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_GEOCODER_GOOGLE_INFO_LAT_LONG' | translate }}
  </div>
  <div class="latitude">
    <span class="title">{{ 'GEOCODER_GOOGLE_LATITUDE' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.LatitudeVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('LatitudeVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="LatLongFilter">
      </app-variable-selector-input>
  </div>
  <div class="longitude">
    <span class="title">{{ 'GEOCODER_GOOGLE_LONGITUDE' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.LongitudeVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('LongitudeVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="LatLongFilter">
      </app-variable-selector-input>
  </div>
  <div class="indexArray">
    <span class="title">{{ 'GEOCODER_GOOGLE_INDEX_ARRAY' | translate }}:</span>
    <input class="input indexArray" type="number" placeholder="0" min="0" max="5" [disabled]="readOnly"
      [(ngModel)]="model.IndexArray" [ngClass]="{'invalid-input': !model.isVarValid(model.IndexArray)}">
  </div>
  <div class="border-bottom">
  </div>
  <div class="card-info">
    {{ 'CARD_GEOCODER_GOOGLE_INFO_ADDRESS_COMPONENT' | translate }}
  </div>
  <div class="street">
    <span class="title">{{ 'GEOCODER_GOOGLE_STREET' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.StreetVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('StreetVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="street-number">
    <span class="title">{{ 'GEOCODER_GOOGLE_STREET_NUMBER' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.StreetNumberVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('StreetNumberVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="country">
    <span class="title">{{ 'GEOCODER_GOOGLE_COUNTRY' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.CountryVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('CountryVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="province">
    <span class="title">{{ 'GEOCODER_GOOGLE_PROVINCE' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.ProvinceVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('ProvinceVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="department">
    <span class="title">{{ 'GEOCODER_GOOGLE_DEPARTMENT' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.DepartmentVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('DepartmentVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="locality">
    <span class="title">{{ 'GEOCODER_GOOGLE_LOCALITY' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.LocalityVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('LocalityVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="sublocality">
    <span class="title">{{ 'GEOCODER_GOOGLE_SUBLOCALITY' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.SublocalityVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('SublocalityVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="zip-code">
    <span class="title">{{ 'GEOCODER_GOOGLE_ZIP_CODE' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.ZipCodeVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('ZipCodeVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="zip-code-suffix">
    <span class="title">{{ 'GEOCODER_GOOGLE_ZIP_CODE_SUFFIX' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.ZipCodeSuffixVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('ZipCodeSuffixVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressComponentFilter">
      </app-variable-selector-input>
  </div>
  <div class="border-bottom">
  </div>
  <div class="card-info">
    {{ 'CARD_GEOCODER_GOOGLE_INFO_ADDRESS' | translate }}
  </div>
  <div class="address">
    <span class="title">{{ 'GEOCODER_GOOGLE_ADDRESS' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.AddressVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('AddressVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressFilter">
      </app-variable-selector-input>
  </div>
  <div class="place-id">
    <span class="title">{{ 'GEOCODER_GOOGLE_PLACE_ID' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.PlaceIdVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('PlaceIdVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="addressFilter">
      </app-variable-selector-input>
  </div>
  <div class="border-bottom">
  </div>
  <div class="card-info">
    {{ 'CARD_GEOCODER_GOOGLE_INFO_OBJECT' | translate }} <a target="_blank" href="https://developers.google.com/maps/documentation/geocoding/intro#GeocodingResponses">https://developers.google.com/maps/documentation/geocoding/intro#GeocodingResponses</a>
  </div>
  <div class="object">
    <span class="title">{{ 'GEOCODER_GOOGLE_OBJECT' | translate }}:</span>
    <app-variable-selector-input
        [VariableData]="VariableData(model.ObjectVariableId)"
        [includeImplicit]="false"
        (setVariable)="setVariable('ObjectVariableId', $event)"
        [readOnly]="readOnly"
        [typeFilters]="variableObjectFilter">
      </app-variable-selector-input>
  </div>
  <div class="border-bottom">
  </div>
  <div class="next">
    <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
    <app-block-picker class="input" [blockId]="model.ErrorBlockId" (onSelectNewBlock)="onSelectBlock($event)"
      (onDeleteBlock)="onDeleteBlock($event)" [readOnly]="readOnly"
      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
</div>
