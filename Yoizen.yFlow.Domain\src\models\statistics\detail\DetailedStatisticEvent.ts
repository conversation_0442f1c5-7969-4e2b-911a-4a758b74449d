import { DetailedBase, DetailedInfoTypes } from "./DetailedBase";
import { Moment } from "moment";
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';
import { DataTypes } from "sequelize";

export class DetailedStatisticEvent extends DetailedBase {
    declare flowId: number;
    declare channel: string;
    declare version: number;
    declare blockId: string;
    declare statisticEventId: number;
    declare blockGroupId: string;
    declare data: { texts: any; structuredData: any; messageId: string; caseId: string; userId: string; blockGroupId: string; };

    init(datetime: Moment, data: { flowId: number, channel: string, version: number, blockId: string, statisticEventId: number, messageId: string, caseId: string, userId: string, blockGroupId: string }) {
        this.initBase(datetime);
        this.flowId = data.flowId;
        this.channel = data.channel;
        this.version = data.version;
        this.blockId = data.blockId;
        this.statisticEventId = data.statisticEventId;
        this.blockGroupId = data.blockGroupId;

        this.data = {
            texts: null,
            structuredData: null,
            messageId: data.messageId,
            caseId: data.caseId,
            userId: data.userId,
            blockGroupId: data.blockGroupId
        };
    }

    setStructuredData(structuredData) {
        this.data.structuredData = structuredData;
    }

    setText(text) {
        this.data.texts = text;
    }

    type() {
        return DetailedInfoTypes.StatisticEvent;
    }
}


DetailedStatisticEvent.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    data: DataTypes.JSON,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    statisticEventId: {
        type: DataTypes.INTEGER,
        field: 'statistic_event_id',
    },
    blockId: {
        type: DataTypes.STRING,
        field: 'block_id',
    },
    blockGroupId:{
        type: DataTypes.STRING,
        field: 'block_group_id'
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'detail_statistic_event',
    tableName: 'detail_statistic_event',
    timestamps: false
});