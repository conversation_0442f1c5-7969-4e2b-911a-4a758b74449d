import {Component, ViewChild, Inject, OnInit, AfterViewInit, HostListener, ElementRef} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SignaturePad } from 'angular2-signaturepad';
import { ServerService } from '../../services/ServerService';
import { finalize } from "rxjs/operators";
import { StatusResponse } from '../../models/StatusResponse';
import * as CryptoJS from 'crypto-js'
import { Title } from '@angular/platform-browser';
import { DOCUMENT } from '@angular/common';
import {ToasterService} from 'angular5-toaster/dist';

@Component({
  selector: 'app-account-unlinking.component',
  templateUrl: './account-unlinking.component.html',
  styleUrls: ['./account-unlinking.component.scss'],
})
export class AccountUnlinkingComponent implements OnInit {
  constructor(private route: ActivatedRoute,
              private serverService: ServerService,
              private toasterService: ToasterService) {

  }

  ngOnInit() {
    this.route.queryParams
      .subscribe(params => {
        this.serverService.accountUnlinking(params.token)
          .pipe(finalize(() => {
          }))
          .subscribe((res: StatusResponse) => {
            if (res.success === 1) {
              window.close();
            }
          }, error => {
            this.toasterService.pop('error', 'yFlow', 'Error de conexion con el servidor');
            console.log(error);
          });
      });
  }

  close() {
    window.close();
  }
}
