import { Moment } from "moment";
import { intervalDate, intervalTime } from "../../../../../Yoizen.yFlow.Web/models/historical/interval";

export enum DailyInfoTypes {
    /**
     * Información por intervalo diaria de colas y agentes
     */
    Normal = 1,

    /**
     * Información por intervalo diaria de flows
     */
    ByFlow = 2,

    /**
     * Información por intervalo diaria de bloques
     */
    Blocks = 3,

    /**
     * Información por intervalo diaria de commandos
     */
    Commnads = 4,

    /**
     * Información por intervalo diaria de integraciones
     */
    Integrations = 5,

    /**
     * Información por intervalo diaria de key de transferencias
     */
    DerivationKey = 6,

    /**
     * Información por intervalo diaria de respuestas por defecto
     */
    DefaultAnswers = 7,

    /**
     * Información por intervalo diaria de eventos estadísticos
     */
    StatisticEvent = 8,

    /**
     * Información por intervalo diaria de grupos
     */
    Groups = 9,

    /**
     * Información por intervalo de casos abandonados
     */
    AbandonedCases = 10,

    /**
     * Información por intervalo de bloques para gráfico de comportamiento
     */
    BlocksSequence = 11,

    /**
     * Información por intervalo de grupos para gráfico de comportamiento
     */
    GroupSequence = 12
};

export class DailyBase {
    dailyInfoTypes: any;
    datetime: Moment;
    interval: any;
    intervalDate: any;
    date: Moment;

    constructor(datetime: Moment) {
        this.datetime = datetime;
        this.date = datetime;
        this.interval = intervalTime(datetime);
        this.intervalDate = typeof (intervalDate(datetime).replaceAll) === "undefined" ?
            intervalDate(datetime).replace(/-/g, '') :
            intervalDate(datetime).replaceAll('-', '');
    }

    init({ }) {

    }

    /**
     * 
     * @param {Object} obj remueve los campos que son iguales a 0
     */
    removePropWithoutData(obj: object) {
        var dup = {};
        for (var key in obj) {
            if (obj[key] !== 0) {
                dup[key] = obj[key];
            }
        }
        return dup;
    }

    getClassString(): string {
        return JSON.stringify(this, (key, value) => {
            if (key === 'data') {
                return this.removePropWithoutData(value);
            }
            else if (key !== "id") {
                return value;
            }
        });
    }

    getType() {
        return 'daily_';
    }

    getColumns(): Array<string> {
        return null;
    }

    getTotalsColumns(): number {
        return this.getValues().length;
    }

    getValues(): Array<any> {
        return null;
    }
}