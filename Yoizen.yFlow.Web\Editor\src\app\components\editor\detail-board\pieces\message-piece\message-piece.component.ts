import { Component, OnInit, Input, OnD<PERSON>roy } from '@angular/core';
import { MessagePieceType, Text } from '../../../../../models/pieces/MessagePieceType';
import { ButtonPiece, ButtonType } from '../../../../../models/pieces/ButtonPiece';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { ButtonElementComponent } from './button-element/button-element.component'
import { ChannelTypes } from "../../../../../models/ChannelType";
import { BasePiece } from 'src/app/models/pieces/BasePiece';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';
import { PieceType } from "../../../../../models/PieceType";
import { YSocialSettings } from "../../../../../models/YSocialSettings";

@Component({
  selector: 'app-message-piece',
  templateUrl: './message-piece.component.html',
  styleUrls: ['./message-piece.component.scss']
})
export class MessagePieceComponent extends BasePieceVM implements OnInit, OnDestroy {
  subs = new Subscription();

  constructor(editorService: EditorService, public modalService: ModalService, private singleOverlay: SingleOverlayService, private dragulaService: DragulaService) {
    super(editorService, modalService);
  }

  expandButton: ButtonPiece;
  model: MessagePieceType;
  currentBtnDetail: ButtonElementComponent;
  buttonsMaxLength: number = 524288;
  buttonsAllowEmojis: boolean = true;
  dragulaGroupName: string;

  ngOnInit() {
    this.model = this.context as MessagePieceType;
    this.flow = this.editorService.getCurrentFlow();

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      this.buttonsAllowEmojis = false;
      this.buttonsMaxLength = 20;
    }

    if (this.flow.channel === ChannelTypes.Twitter && this.model.TextList.length < 3) {
      let index = this.model.TextList.length;
      while (index < 3) {
        this.model.TextList.push(new Text());
        index++;
      }
    }

    this.dragulaGroupName = `MESSAGE_BUTTON_ELEMEN_${this.model.Uid}`;
    if (this.dragulaService.find(this.dragulaGroupName) == null) {
      this.dragulaService.createGroup(this.dragulaGroupName, {
        moves: function (el: any, container: any, handle: any, sibling: any): any {
          let parentElement: HTMLElement = handle.parentElement;
          while (parentElement !== null) {
            if (parentElement.classList.contains('button-content')) {
              break;
            }

            if (parentElement.classList.contains('button-details')) {
              return false;
            }

            parentElement = parentElement.parentElement;
          }

          return true;
        }
      });
    }

    this.subs.add(this.dragulaService.dropModel(this.dragulaGroupName)
      .subscribe(({ el, target, source, sourceModel, targetModel, item }) => {
        this.model.Buttons = targetModel;
      })
    );
  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.subs.unsubscribe();
  }

  addNewText() {
    this.model.TextList.push(new Text());
  }

  canDelete() {
    if (this.flow.channel === ChannelTypes.Twitter) {
      return !this.readOnly && this.model.TextList.length > 3
    }
    return !this.readOnly && this.model.TextList.length > 1
  }

  addNewButton() {
    this.singleOverlay.closeCurrentAction = () => { this.expandButton = null; };
    this.expandButton = new ButtonPiece();
    this.model.Buttons.push(this.expandButton);
  }

  canAddTextOptions(): boolean {
    if (this.flow.channel === ChannelTypes.Twitter) {
      return this.model.TextList.length < 7;
    }

    return this.model.TextList.length < 3;
  }

  supportButtons(): boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Telegram ||
      this.flow.channel === ChannelTypes.Generic ||
      this.flow.channel === ChannelTypes.GoogleRBM ||
      this.flow.channel === ChannelTypes.Teams) {
      return true;
    }

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return true;
      }
    }

    return false;
  }

  canAddButton(): boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Generic ||
      this.flow.channel === ChannelTypes.GoogleRBM ||
      this.flow.channel === ChannelTypes.Teams) {
      return this.model.Buttons.length < 20;
    }

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return this.model.Buttons.length < 20;
      }
    }

    if (this.flow.channel === ChannelTypes.AppleMessaging) {
      return this.model.Buttons.length < 5;
    }

    if (this.flow.channel === ChannelTypes.Telegram) {
      let nextPiece = this.getNextPiece();

      if (nextPiece === null ||
        nextPiece === undefined ||
        nextPiece.type !== 'quick-reply-piece') {
        return true;
      }
    }

    return false;
  }

  isTextValid(index: number) {
    return str => { return this.model.isTextValid(index, this.editorService) }
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }

  deleteButtonElement(element) {
    this.model.Buttons.splice(element, 1);
  }

  onShowButtonDetail(btn: ButtonPiece) {
    this.singleOverlay.closeCurrentAction = () => { this.expandButton = null; };
    this.expandButton = btn;
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
      this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
      this.model
    );
  }

  getButtonStats(button: ButtonPiece): any {
    if (this.stats === null ||
      typeof (this.stats.buttons) === 'undefined' ||
      this.stats.buttons === null) {
      return null;
    }

    if (button.Type !== ButtonType.Redirect ||
      button.BlockID === "-1") {
      return null;
    }

    let buttonStatsInfo = this.stats.buttons.find(b => b.id === button.Uid && b.destBlock === button.BlockID);
    if (typeof (buttonStatsInfo) === 'undefined') {
      return null;
    }

    return buttonStatsInfo;
  }

  getNextPiece(): BasePiece {
    let pieces = this.editorService.getEditorState().SelectedBlock.Pieces;

    if (pieces.length === this.index + 1) {
      return null;
    }

    return pieces[this.index + 1];
  }

  canCreateQuickReply() {
    if (this.flow.channel !== ChannelTypes.FacebookMessenger &&
      this.flow.channel !== ChannelTypes.Instagram &&
      this.flow.channel !== ChannelTypes.Chat &&
      this.flow.channel !== ChannelTypes.Twitter &&
      this.flow.channel !== ChannelTypes.Skype &&
      this.flow.channel !== ChannelTypes.Telegram &&
      this.flow.channel !== ChannelTypes.Teams) {
      return false;
    }

    if (this.flow.channel === ChannelTypes.Telegram &&
      this.model.Buttons !== null &&
      this.model.Buttons.length > 0) {
      return false;
    }

    let next = this.getNextPiece();
    if (next != null) {
      if (next.type == 'quick-reply-piece') {
        return false;
      }
    }

    return true;
  }

  containsMoreButtonsThanAllowed(): boolean {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Generic ||
      this.flow.channel === ChannelTypes.Teams) {
      if (this.model.Buttons.length > 3) {
        return true;
      }
    }
    else if (this.flow.channel === ChannelTypes.AppleMessaging) {
      if (this.model.Buttons.length > 5) {
        return true;
      }
    }
    else if (this.flow.channel === ChannelTypes.GoogleRBM) {
      if (this.model.Buttons.length > 11) {
        return true;
      }
    }
    else if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return this.model.Buttons.length > 3;
      }
    }
    else if (this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Telegram) {
      if (this.model.Buttons.length > 5) {
        return true;
      }
    }

    return false;
  }

  maxButtonsAllowed(): number {
    if (this.flow.channel === ChannelTypes.FacebookMessenger ||
      this.flow.channel === ChannelTypes.Instagram ||
      this.flow.channel === ChannelTypes.Twitter ||
      this.flow.channel === ChannelTypes.Skype ||
      this.flow.channel === ChannelTypes.Generic) {
      return 3;
    }

    if (this.flow.channel === ChannelTypes.AppleMessaging) {
      return 5;
    }

    if (this.flow.channel === ChannelTypes.GoogleRBM) {
      return 11;
    }

    if (this.flow.channel === ChannelTypes.WhatsApp) {
      let socialSettings: YSocialSettings = this.editorService.getYSocialSettings();
      if (socialSettings.WhatsappUseInteractive) {
        return 3;
      }
    }

    if (this.flow.channel === ChannelTypes.Chat ||
      this.flow.channel === ChannelTypes.Telegram || 
      this.flow.channel === ChannelTypes.Teams) {
      return 5;
    }

    return 0;
  }
}
