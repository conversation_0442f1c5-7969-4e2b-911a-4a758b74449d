<div class="time-picker card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-calendar-check"></span> {{ 'PIECE_TIME_PICKER' | translate }}
  </div>
  <div class="card-info">
    {{ 'PIECE_TIME_PICKER_INFO' | translate }}
  </div>
  <div class="option">
    <span class="title">{{'TIME_PICKER_BLOCK' | translate}}:</span>
    <app-block-picker class="input" [blockId]="model.blockId" (onSelectNewBlock)="onSelectBlock($event)"
                      [readOnly]="readOnly"
                      (onDeleteBlock)="onDeleteBlock()"
                      [isInvalid]="!model.isBlockValid(editorService)"></app-block-picker>
  </div>
  <div class="option">
    <span class="title">{{'TIME_PICKER_DEST_VARIABLE' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="assignToVariableData"
      (setVariable)="setAssignToVariable($event)"
      [validator]="model.isAssignToVariableValid.bind(model)"
      [canSelectConstants]="false"
      [readOnly]="readOnly"
      [typeFilters]="assignToVariableFilter">
    </app-variable-selector-input>
  </div>
  <app-time-picker-event
    [event]="model.event"
    [readOnly]="readOnly"></app-time-picker-event>
  <app-apple-interactive-received-and-reply
    [receivedMessage]="model.receivedMessage"
    [replyMessage]="model.replyMessage"
    [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
</div>
