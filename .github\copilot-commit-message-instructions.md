# Instrucciones para Mensajes de Commit con GitHub Copilot

## 📝 Formato de Mensaje de Commit

Usar **SIEMPRE** el formato de Conventional Commits en **español** con la siguiente estructura:

```
<tipo>(<ámbito>): <descripción breve>

<descripción detallada>
- Qué se cambió
- Por qué se realizó el cambio
- Qué problema resuelve o mejora aporta
```

## 🏷️ Tipos de Commit

### **feat** - Nueva funcionalidad
```
feat(knowledge-base): agregar el prompt en las propiedades de la pieza base de conocimiento

Se agregó el prompt en las propiedades de la pieza base de conocimiento para que el usuario pueda ver el prompt al editar la pieza. Esto mejora la usabilidad del sistema y evita confusiones al editar la pieza.
```

### **fix** - Corrección de bug
```
fix(integration-piece): corregir timeout en llamadas HTTP

Se aumentó el timeout de las integraciones de 5s a 30s para evitar errores en APIs lentas. Esto resuelve los fallos de timeout reportados en integraciones con servicios externos de terceros.
```

### **refactor** - Refactorización de código
```
refactor(flow-executor): extraer lógica de validación a método separado

Se extrajo la validación de mensajes a un método independiente para mejorar la legibilidad y reutilización del código. Esto facilita el mantenimiento y testing de la lógica de validación.
```

### **style** - Cambios de formato
```
style(editor): aplicar formato consistente en componentes Angular

Se aplicó Prettier y ESLint a todos los componentes del editor para mantener consistencia en el estilo de código según las reglas establecidas del proyecto.
```

### **docs** - Documentación
```
docs(api): actualizar documentación de endpoints de configuración

Se actualizó la documentación de los endpoints de configuración con los nuevos parámetros y ejemplos de uso. Esto ayuda a los desarrolladores a integrar correctamente las nuevas funcionalidades.
```

### **test** - Pruebas
```
test(piece-executor): agregar tests unitarios para IntegrationPiece

Se agregaron tests unitarios para validar el comportamiento de IntegrationPiece en diferentes escenarios de error y éxito. Esto mejora la cobertura de tests y previene regresiones.
```

### **chore** - Tareas de mantenimiento
```
chore(deps): actualizar dependencias de seguridad

Se actualizaron las dependencias con vulnerabilidades de seguridad reportadas por npm audit. Esto mejora la seguridad del proyecto sin afectar funcionalidades existentes.
```

## 🎯 Ámbitos Comunes por Componente

### **WebExecutor**
- `flow-executor` - Ejecutores de flujo
- `piece-executor` - Ejecutores de piezas
- `integration-piece` - Piezas de integración
- `control-flow` - Control de flujo de mensajes

### **Web (Backend)**
- `api` - Endpoints de API
- `models` - Modelos de base de datos
- `routes` - Rutas de Express
- `middleware` - Middleware de Express

### **Web (Frontend - Editor Angular)**
- `editor` - Componentes del editor
- `piece-components` - Componentes de piezas
- `flow-designer` - Diseñador de flujos
- `configuration` - Componentes de configuración
- `ui` - Elementos de interfaz de usuario

### **IntervalServices**
- `report-service` - Servicio de reportes
- `centralize-service` - Servicio de centralización
- `ftp-service` - Servicio FTP
- `statistics` - Procesamiento de estadísticas

### **Infrastructure**
- `adapters` - Adaptadores de base de datos
- `ports` - Interfaces de puertos
- `redis` - Configuración Redis
- `sequelize` - Configuración Sequelize

### **Domain**
- `models` - Modelos de dominio
- `statistics` - Modelos de estadísticas
- `flow` - Modelos de flujo

### **Helpers**
- `config` - Configuración global
- `logger` - Sistema de logging
- `utils` - Utilidades compartidas

## ✅ Reglas para el Mensaje

### **OBLIGATORIO - Análisis de Archivos Staged**
- **SOLO** analizar archivos que están en `git staged` (preparados para commit)
- **NO** incluir archivos que no están staged
- **NO** mencionar cambios en archivos no staged

### **Estructura del Mensaje**
1. **Línea de título**: Máximo 72 caracteres
2. **Línea en blanco**
3. **Descripción detallada**: Explicar el QUÉ, POR QUÉ y PARA QUÉ

### **Contenido de la Descripción**
- **Qué se cambió**: Descripción específica de los cambios realizados
- **Por qué se realizó**: Justificación o razón del cambio
- **Qué problema resuelve**: Beneficio o mejora que aporta

### **Ejemplos Específicos por Tipo de Cambio**

#### Nueva Pieza de Flujo
```
feat(piece-executor): implementar pieza de validación biométrica

Se implementó una nueva pieza para validación biométrica que integra con el servicio yBiometric. La pieza valida documentos de identidad y extrae datos del usuario.
- Agrega validación automática de DNI frontal y dorsal
- Integra con API de yBiometric para procesamiento
- Mejora la experiencia de usuario en procesos de verificación de identidad
```

#### Corrección de Bug
```
fix(redis-adapter): corregir pérdida de contexto en reconexiones

Se corrigió un bug donde el contexto de conversación se perdía al reconectarse a Redis después de un timeout. 
- Implementa retry automático con backoff exponencial
- Preserva el contexto durante reconexiones
- Evita que los usuarios tengan que reiniciar conversaciones por fallos de red
```

#### Mejora de UI
```
feat(editor): agregar vista previa en tiempo real de mensajes

Se agregó una vista previa que muestra cómo se verán los mensajes en WhatsApp mientras el usuario edita el flujo.
- Renderiza mensajes con formato de WhatsApp en tiempo real
- Incluye soporte para emojis, texto enriquecido y multimedia
- Mejora la experiencia de diseño de flujos y reduce errores de formato
```

## 🚫 Qué NO Hacer

- ❌ No usar "Se actualiza", "Se modifica" - ser específico
- ❌ No ser genérico: "fix: varios bugs" 
- ❌ No mencionar archivos no staged
- ❌ No usar términos técnicos sin contexto
- ❌ No omitir la justificación del cambio

## ✅ Qué SÍ Hacer

- ✅ Ser específico sobre el cambio realizado
- ✅ Explicar el beneficio o problema que resuelve
- ✅ Usar terminología del dominio de yFlow (piezas, flujos, casos, etc.)
- ✅ Mencionar el componente afectado en el ámbito
- ✅ Incluir contexto suficiente para entender el cambio

---

**Recuerda**: El mensaje de commit debe permitir a cualquier desarrollador entender qué cambió, por qué y qué impacto tiene, basándose únicamente en los archivos que están en staging.
