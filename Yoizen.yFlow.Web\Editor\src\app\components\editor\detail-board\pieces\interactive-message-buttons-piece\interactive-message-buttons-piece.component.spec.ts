import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InteractiveMessageButtonsPieceComponent } from './interactive-message-buttons-piece.component';

describe('InteractiveMessageButtonsPieceComponent', () => {
  let component: InteractiveMessageButtonsPieceComponent;
  let fixture: ComponentFixture<InteractiveMessageButtonsPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InteractiveMessageButtonsPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InteractiveMessageButtonsPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
