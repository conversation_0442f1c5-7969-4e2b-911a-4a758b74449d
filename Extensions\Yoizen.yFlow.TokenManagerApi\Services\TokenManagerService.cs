using System.Text;
using System.Text.Json;
using TokenManagerApi.Models;
namespace TokenManagerApi.Services;
public class TokenManagerService : ITokenManagerService
{
    private readonly SemaphoreSlim _lock = new(1, 1);
    private string? _currentToken;
    private DateTime _expirationTime;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<TokenManagerService> _logger;
    private const int REFRESH_MINUTES = 5;
    private TokenResponse? _currentTokenResponse;

    public TokenManagerService(IHttpClientFactory httpClientFactory, ILogger<TokenManagerService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public async Task<TokenResponse> GetTokenAsync(TokenRequest request)
    {
        try
        {
            if (IsTokenValidForUse())
                return _currentTokenResponse;

            await _lock.WaitAsync();
            try
            {
                if (IsTokenValidForUse())
                    return _currentTokenResponse;
                _currentTokenResponse = null;
                await GenerateNewTokenAsync(request);
                return _currentTokenResponse;
            }
            finally
            {
                _lock.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token. Request URL: {Url}", request.Url);
            throw new Exception("Failed to get token", ex);
        }
    }

    private bool IsTokenValidForUse()
    {
        if (_currentTokenResponse?.access_token == null) return false;

        var refreshTime = DateTime.UtcNow.AddMinutes(REFRESH_MINUTES);
        return _expirationTime > refreshTime;
    }

    private async Task GenerateNewTokenAsync(TokenRequest request)
    {
        var client = _httpClientFactory.CreateClient();
        var requestId = Guid.NewGuid().ToString();

        // Log inicial detallado de la request
        _logger.LogInformation(
            "REQUEST TOKEN - ID: {RequestId}\nURL: {Url}\nHeaders: {@Headers}\nTimeout: {Timeout}s",
            requestId, request.Url, request.Headers, request.Timeout);

        foreach (var header in request.Headers.Where(h => !h.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase)))
        {
            client.DefaultRequestHeaders.Add(header.Key, header.Value);
            _logger.LogDebug("Header agregado - RequestId: {RequestId}, Key: {Key}, Value: {Value}",
                requestId, header.Key, header.Value);
        }

        client.Timeout = TimeSpan.FromSeconds(request.Timeout);
        var content = new StringContent(string.Empty, Encoding.UTF8, "application/x-www-form-urlencoded");

        try
        {
            _logger.LogInformation("Enviando solicitud - RequestId: {RequestId}", requestId);
            var response = await client.PostAsync(request.Url, content);

            // Log del response completo
            var responseBody = await response.Content.ReadAsStringAsync();
            _logger.LogInformation(
                "RESPONSE TOKEN - ID: {RequestId}\nStatus: {StatusCode}\nBody: {ResponseBody}",
                requestId, response.StatusCode, responseBody);

            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<TokenResponse>();

            if (result != null)
            {
                _currentTokenResponse = result;
                _currentToken = result.access_token;
                _expirationTime = DateTime.UtcNow.AddSeconds(result.expires_in);

                _logger.LogInformation(
                    "Token generado exitosamente - RequestId: {RequestId}\nToken: {Token}\nExpira en: {ExpiresIn}s",
                    requestId, result.access_token, result.expires_in);
            }
            else
            {
                throw new InvalidOperationException("La respuesta del token es nula");
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex,
                "ERROR HTTP - RequestId: {RequestId}\nStatusCode: {StatusCode}\nMensaje: {Message}\nStackTrace: {StackTrace}",
                requestId, ex.StatusCode, ex.Message, ex.StackTrace);
            throw;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex,
                "ERROR JSON - RequestId: {RequestId}\nMensaje: {Message}\nStackTrace: {StackTrace}",
                requestId, ex.Message, ex.StackTrace);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "ERROR GENERAL - RequestId: {RequestId}\nTipo: {ExceptionType}\nMensaje: {Message}\nStackTrace: {StackTrace}",
                requestId, ex.GetType().Name, ex.Message, ex.StackTrace);
            throw;
        }
    }

}