<div class="attach card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-paperclip"></span> {{ 'CARD_ATTACH_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'ATTACHMENT_INFO' | translate }}
  </div>
  <div class="twitter" *ngIf="isTwitterFlow">
    <div class="empty" *ngIf="isTwitterFlow" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'ATTACHMENT_TWITTER_INFO' | translate }}
      </div>
    </div>
    <div class="text">
      <span class="title">{{ 'ATTACHMENT_TWITTER_MESSAGE' | translate }}</span>
      <app-input-with-variables [placeholder]="'VALUE' | translate"
                                [(value)]="model.Text"
                                [wideInput]="'true'"
                                [isTextArea]="'true'"
                                [minRows]="5"
                                [spellCheck]="true"
                                [disabled]="readOnly"></app-input-with-variables>
    </div>
  </div>
  <div class="definition">
    <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
      <ngb-tab id="tab-url" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-link"></span> URL</ng-template>
        <ng-template ngbTabContent>
          <div class="contents">
            <div class="url">
              <span class="title">{{ 'URL' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'URL'"
                [(value)]="model.Url"
                [validator]="isUrlValid"
                [wideInput]="true"
                [disabled]="readOnly"
                class="input-variable-area">
              </app-input-with-variables>
            </div>
            <div class="publicurl" *ngIf="showIsPublicToggle && (isWhatsappChannel || isTelegramChannel)">
              <span class="title">{{ 'ATTACHMENT_IS_PUBLIC_URL' | translate}}:</span>
              <ui-switch [(ngModel)]="model.IsPublicUrl" [disabled]="readOnly"
                         color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
            </div>
          </div>
        </ng-template>
      </ngb-tab>
      <ngb-tab id="tab-variable" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-database"></span> {{'VARIABLE' | translate}}</ng-template>
        <ng-template ngbTabContent>
          <div class="contents">
            <div class="url">
              <span class="title">{{ 'VARIABLE' | translate}}:</span>
              <app-variable-selector-input
                [VariableData]="variableDefinition"
                (setVariable)="setVariableOnOutput(null, $event)"
                [typeFilters]="variableFilter"
                [readOnly]="readOnly"
                [validator]="getFileVaraibleValidator()">
              </app-variable-selector-input>
            </div>
          </div>
        </ng-template>
      </ngb-tab>
    </ngb-tabset>
    <div class="name">
      <span class="title">{{ 'ATTACHMENT_NAME' | translate}}:</span>
      <app-input-with-variables
        class="input-variable-area"
        [placeholder]="'ATTACHMENT_NAME' | translate"
        [(value)]="model.Name"
        [validator]="isNameValid"
        [disabled]="readOnly"
        [wideInput]="true">
      </app-input-with-variables>
    </div>
    <div class="mimetype">
      <span class="title">{{ 'ATTACHMENT_MIMETYPE' | translate}}:</span>
      <app-input-with-variables
        class="input-variable-area"
        [placeholder]="'ATTACHMENT_MIMETYPE' | translate"
        [(value)]="model.MimeType"
        [validator]="isMimeTypeValid"
        [wideInput]="'true'"
        [list]="'knowncontenttypes'"
        [disabled]="readOnly"
        [spellCheck]="false">
      </app-input-with-variables>
    </div>
  </div>
  <div class="addQuick" *ngIf="canCreateQuickReply()" (click)="addQuickReplyPiece()">
    <span class="fa fa-plus"></span> {{ 'QUICKREPLY_ADD' | translate }}
  </div>
</div>
