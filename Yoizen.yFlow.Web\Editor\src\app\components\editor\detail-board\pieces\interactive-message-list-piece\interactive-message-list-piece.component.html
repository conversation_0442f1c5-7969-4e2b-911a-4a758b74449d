<div class="card interactive-list {{ editorService.getChannelTypeName(model.Channel) }}" [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-list-ul"></span> {{ 'CARD_INTERACTIVE_MESSAGE_LIST_TITLE' | translate }}
  </div>
  <div class="max-length" role="alert" *ngIf="!readOnly && model.Channel === channelTypes.WhatsApp">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_MAX_LENGTH' | translate }}</span>
    </div>
  </div>
  <div class="messages">
    <app-text-list-message *ngFor="let text of model?.TextList let i = index"
                           [(Text)]="text.text"
                           [Index]="i"
                           [CanDelete]="!readOnly && model?.TextList.length > 1"
                           [readOnly]="readOnly"
                           [Validator]="isTextValid(i)"
                           (onDelete)="deleteElement($event)"></app-text-list-message>
    <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
      <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
           placement="right" tooltipClass="tooltip-add">
        <span class="fa fa-plus"></span>
      </div>
    </div>
  </div>
  <div class="option" *ngIf="model.Channel === channelTypes.WhatsApp">
    <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SHOW_BUTTON_TEXT' | translate}}:</span>
    <app-input-with-variables
      [placeholder]="'INTERACTIVE_MESSAGE_LIST_SHOW_BUTTON_TEXT_PLACEHOLDER' | translate"
      [(value)]="model.ButtonShowListText"
      [validator]="isButtonShowTextValid()"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option">
    <span class="title">{{'INTERACTIVE_MESSAGE_LIST_BLOCK' | translate}}:</span>
    <app-block-picker class="input" [blockId]="model.BlockId" (onSelectNewBlock)="onSelectBlock($event)"
                      [readOnly]="readOnly"
                      (onDeleteBlock)="onDeleteBlock()"
                      [isInvalid]="!model.isBlockValid(editorService)"></app-block-picker>
  </div>
  <div class="option">
    <span class="title">{{'INTERACTIVE_MESSAGE_LIST_VARIABLE' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="variableData"
      (setVariable)="setVariable($event)"
      [validator]="validateVariable"
      [canSelectConstants]="false"
      [readOnly]="readOnly"
      [typeFilters]="variableFilter">
    </app-variable-selector-input>
  </div>
  <div class="sections">
    <div class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTIONS' | translate}}</div>
    <div class="info">{{'INTERACTIVE_MESSAGE_LIST_SECTIONS_INFO' | translate}}</div>
    <div class="sections-container" [dragula]="'SECTIONS_GROUP'" [(dragulaModel)]="model.Sections">
      <div class="section" *ngFor="let section of model?.Sections let i = index">
        <div class="section-drag-handle" *ngIf="!readOnly">
          <span class="fa fa-grip-vertical"></span>
        </div>
        <div class="trash" (click)="deleteSection(i)" *ngIf="model?.Sections.length > 1 && !readOnly"
             data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
             tooltipClass="tooltip-trash">
          <span class="fa fa-trash-alt"></span>
        </div>
        <div class="clone" (click)="cloneSection(section)" *ngIf="!readOnly"
             data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
          <span class="fa fa-clone"></span>
        </div>
        <div class="option">
          <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_TITLE' | translate}}:</span>
          <app-input-with-variables
            [(value)]="section.Title"
            [validator]="section.isTitleValid.bind(section, editorService)"
            [wideInput]="true"
            [isTextArea]="false"
            [disabled]="readOnly"
            class="input">
          </app-input-with-variables>
        </div>
        <div class="option" *ngIf="model.Channel === channelTypes.AppleMessaging">
          <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_MULTIPLE' | translate}}:</span>
          <ui-switch [(ngModel)]="section.Multiple" [disabled]="readOnly"
                     color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
        </div>
        <div class="section-rows" [dragula]="'BUTTONS_GROUP'" [(dragulaModel)]="section.Rows">
          <div class="section-row" *ngFor="let row of section.Rows let j = index">
            <div class="button-drag-handle" *ngIf="!readOnly">
              <span class="fa fa-grip-vertical"></span>
            </div>
            <div class="trash" (click)="deleteRow(section, j)" *ngIf="section.Rows.length > 1 && !readOnly"
                 data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
                 tooltipClass="tooltip-trash">
              <span class="fa fa-trash-alt"></span>
            </div>
            <div class="clone" (click)="cloneRow(section, row)" *ngIf="!readOnly"
                 data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
              <span class="fa fa-clone"></span>
            </div>
            <div class="icon">
              <span class="fa fa-dot-circle"></span>
            </div>
            <div class="data">
              <div class="option">
                <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_TITLE' | translate}}:</span>
                <app-input-with-variables
                  [(value)]="row.Title"
                  [validator]="row.isTitleValid.bind(row)"
                  [wideInput]="true"
                  [isTextArea]="false"
                  [disabled]="readOnly"
                  class="input">
                </app-input-with-variables>
              </div>
              <div class="option">
                <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_DESCRIPTION' | translate}}:</span>
                <app-input-with-variables
                  [(value)]="row.Description"
                  [validator]="row.isDescriptionValid.bind(row)"
                  [wideInput]="true"
                  [isTextArea]="false"
                  [disabled]="readOnly"
                  class="input">
                </app-input-with-variables>
              </div>
              <div class="option">
                <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_VALUE' | translate}}:</span>
                <app-input-with-variables
                  [(value)]="row.Value"
                  [validator]="row.isValueValid.bind(row)"
                  [wideInput]="true"
                  [isTextArea]="false"
                  [disabled]="readOnly"
                  class="input">
                </app-input-with-variables>
              </div>
              <div class="option" *ngIf="model.Channel === channelTypes.WhatsApp">
                <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_BLOCK' | translate}}:</span>
                <app-block-picker class="input" [blockId]="row.BlockID" (onSelectNewBlock)="onSelectBlockSectionRow(row, $event)"
                                  [readOnly]="readOnly"
                                  (onDeleteBlock)="onDeleteBlockSectionRow(row)"
                                  [isInvalid]="!row.isBlockValid(editorService)"></app-block-picker>
              </div>
              <div class="option" *ngIf="model.Channel === channelTypes.AppleMessaging">
                <span class="title">{{'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_IMAGE' | translate}}:</span>
                <app-input-with-variables
                  [(value)]="row.Image"
                  [validator]="row.isImageValid.bind(row)"
                  [wideInput]="true"
                  [isTextArea]="false"
                  [disabled]="readOnly"
                  class="input">
                </app-input-with-variables>
              </div>
            </div>
          </div>
          <div class="section-row-add" (click)="addNewSectionRow(section); $event.stopPropagation();" *ngIf="canAddSectionRow(section) && !readOnly">
            <span class="fa fa-plus"></span> {{ 'INTERACTIVE_MESSAGE_LIST_SECTION_ROW_ADD' | translate }}
          </div>
        </div>
      </div>
      <div class="section-add" [ngClass]="{'hide': !canAddSection() }" *ngIf="!readOnly">
        <div (click)="addNewSection()" data-toggle="tooltip" ngbTooltip="{{ 'INTERACTIVE_MESSAGE_LIST_SECTION_ADD' | translate }}"
             placement="right" tooltipClass="tooltip-add">
          <span class="fa fa-plus"></span>
        </div>
      </div>
    </div>
  </div>
  <app-apple-interactive-received-and-reply
    *ngIf="model.Channel === channelTypes.AppleMessaging"
    [receivedMessage]="model.ReceivedMessage"
    [replyMessage]="model.ReplyMessage"
    [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
</div>
