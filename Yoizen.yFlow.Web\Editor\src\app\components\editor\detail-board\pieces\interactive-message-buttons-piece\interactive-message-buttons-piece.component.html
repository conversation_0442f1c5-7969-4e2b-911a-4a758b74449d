<div class="card text {{ editorService.getChannelTypeName(model.Channel) }}" [ngClass]="{'invalid-piece': !model.isValid(editorService), 'buttons-warning': containsMoreButtonsThanAllowed() }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-bars"></span> {{ 'CARD_INTERACTIVE_MESSAGE_BUTTONS_TITLE' | translate }}
  </div>
  <div class="more-buttons-than-allowed" role="alert">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'CARD_MESSAGE_MORE_BUTTONS_THAN_ALLOWED' | translate:{maxButtons: maxButtonsAllowed() } }}
    </div>
  </div>
  <div class="header" *ngIf="model.Channel === channelTypes.WhatsApp">
    <div class="title">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER' | translate }}</div>
    <div class="option">
      <span class="title">{{'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE' | translate}}:</span>
      <select class="select" [(ngModel)]="model.Header.Type">
        <option [value]="HeaderTypes.None">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_NONE' | translate}}</option>
        <option [value]="HeaderTypes.Text">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_TEXT' | translate}}</option>
        <option [value]="HeaderTypes.Image">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_IMAGE' | translate}}</option>
        <option [value]="HeaderTypes.Video">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_VIDEO' | translate}}</option>
        <option [value]="HeaderTypes.Document">{{ 'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_DOCUMENT' | translate}}</option>
      </select>
    </div>
    <div class="option" *ngIf="model.Header.Type == HeaderTypes.Text">
      <span class="title">{{'INTERACTIVE_MESSAGE_BUTTONS_HEADER_TYPE_TEXT' | translate}}:</span>
      <app-input-with-variables
        [(value)]="model.Header.Text"
        [validator]="isHeaderTextValid()"
        [wideInput]="true"
        [isTextArea]="false"
        [disabled]="readOnly"
        class="input">
      </app-input-with-variables>
    </div>
    <div class="multimedia" *ngIf="model.Header.Type == HeaderTypes.Image || model.Header.Type == HeaderTypes.Video || model.Header.Type == HeaderTypes.Document || model.Header.Type == HeaderTypes.Audio">
      <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
        <ngb-tab id="tab-url" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-link"></span> URL</ng-template>
          <ng-template ngbTabContent>
            <div class="contents">
              <div class="url">
                <span class="title">{{ 'URL' | translate}}:</span>
                <app-input-with-variables
                  [placeholder]="'URL'"
                  [(value)]="model.Header.Url"
                  [validator]="isUrlValid"
                  [wideInput]="true"
                  [disabled]="readOnly"
                  class="input-variable-area">
                </app-input-with-variables>
              </div>
              <div class="publicurl" *ngIf="showIsPublicToggle">
                <span class="title">{{ 'ATTACHMENT_IS_PUBLIC_URL' | translate}}:</span>
                <ui-switch [(ngModel)]="model.Header.IsPublicUrl" [disabled]="readOnly"
                           color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-variable" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-database"></span> {{'VARIABLE' | translate}}</ng-template>
          <ng-template ngbTabContent>
            <div class="contents">
              <app-variable-selector-input
                [VariableData]="variableDefinition"
                (setVariable)="setVariableOnOutput(null, $event)"
                [typeFilters]="variableFilter"
                [readOnly]="readOnly"
                [validator]="getFileVaraibleValidator()">
              </app-variable-selector-input>
            </div>
          </ng-template>
        </ngb-tab>
      </ngb-tabset>
      <div class="name" *ngIf="model.Header.Type == HeaderTypes.Document || model.Header.Source != 0">
        <span class="title">{{ 'ATTACHMENT_NAME' | translate}}:</span>
        <app-input-with-variables
          class="input-variable-area"
          [placeholder]="'ATTACHMENT_NAME' | translate"
          [(value)]="model.Header.Name"
          [validator]="isNameValid"
          [disabled]="readOnly"
          [wideInput]="true">
        </app-input-with-variables>
      </div>
    </div>
  </div>
  <div class="body">
    <div class="title">{{ 'INTERACTIVE_MESSAGE_BUTTONS_BODY' | translate }}</div>
    <div class="max-length" role="alert" *ngIf="!readOnly">
      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_MAX_LENGTH' | translate }}</span>
      </div>
    </div>
    <div class="messages">
      <app-text-list-message *ngFor="let text of model?.TextList let i = index"
                             [(Text)]="text.text"
                             [Index]="i"
                             [MinRows]="2"
                             [CanDelete]="!readOnly && model?.TextList.length > 1"
                             [readOnly]="readOnly"
                             [extendedStyles]="{ 'height.px': '100', 'max-height.px': '100', 'min-height.px': '100' }"
                             [Validator]="isTextValid(i)"
                             (onDelete)="deleteElement($event)"></app-text-list-message>
      <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
        <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
             placement="right" tooltipClass="tooltip-add">
          <span class="fa fa-plus"></span>
        </div>
      </div>
    </div>
  </div>
  <div class="footer" *ngIf="model.Channel === channelTypes.WhatsApp">
    <div class="title">{{ 'INTERACTIVE_MESSAGE_BUTTONS_FOOTER' | translate }}</div>
    <div class="option">
      <span class="title">{{'INTERACTIVE_MESSAGE_BUTTONS_FOOTER_TEXT' | translate}}:</span>
      <app-input-with-variables
        [(value)]="model.Footer"
        [validator]="isFooterTextValid()"
        [wideInput]="true"
        [isTextArea]="false"
        [disabled]="readOnly"
        class="input">
      </app-input-with-variables>
    </div>
  </div>
  <ng-container>
    <div [dragula]="dragulaGroupName" [(dragulaModel)]="model.Buttons">
        <app-button-element *ngFor="let button of model?.Buttons let i = index"
                            [Model]="button"
                            [Index]="i"
                            [stats]="getButtonStats(button)"
                            (onDelete)="deleteButtonElement($event)"
                            [expandedBtn]="expandButton"
                            [readOnly]="readOnly"
                            [EmojisAllowed]="false"
                            [MaxLength]="20"
          (onShowDetail)="onShowButtonDetail($event)">
      </app-button-element>
    </div>
  </ng-container>
  <div class="addButton" (click)="addNewButton(); $event.stopPropagation();" *ngIf="canAddButton() && !readOnly">
    <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
  </div>
  <div *ngIf="!canAddButton() && !readOnly">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      {{'MAX_BUTTON_REACHED' | translate}}
    </div>
  </div>
  <div class="quick" *ngIf="canCreateQuickReply() && !readOnly" (click)="addQuickReplyPiece()">
    <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_QUICKREPLY' | translate }}
  </div>
  <div class="summary" *ngIf="model.Channel === channelTypes.AppleMessaging">
    <div class="title">{{ 'INTERACTIVE_MESSAGE_BUTTONS_SUMMARY' | translate }}</div>
    <div class="option">
      <span class="title">{{'INTERACTIVE_MESSAGE_BUTTONS_SUMMARY_TEXT' | translate}}:</span>
      <app-input-with-variables
        [(value)]="model.SummaryText"
        [validator]="model.isSummaryTextValid.bind(model)"
        [wideInput]="true"
        [isTextArea]="false"
        [disabled]="readOnly"
        class="input">
      </app-input-with-variables>
    </div>
  </div>
</div>
