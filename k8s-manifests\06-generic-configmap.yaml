apiVersion: v1
kind: ConfigMap
metadata:
  name: yflow-yoizen-qa-generic-configmap
  namespace: yflow-yoizen-qa
data:
  CLIENT: YoizenTest
  COGNITIVITY_API_VERSION: "2"
  STAND_ALONE: "false"
  NODE_ENV: prod
  GMT: "-03:00"
  YSOCIAL_URL: https://qa.ysocial.net/test/
  YFLOW_URL: https://yflow-yoizen-qa.ysocial.net/
  URL_API_COGNITIVE_SERVICES: https://ysmartlinux-testea92.azurewebsites.net/api/v2/
  URL_CLIENT_APP_COGNITIVE_SERVICES: https://ysmartlinux-testea92.azurewebsites.net/api/home/<USER>
  #  VIRTUAL_DIR_PATH: yflow
  DEFAULT_LANGUAJE: "es"
  REDISCACHEHOSTNAME: "yflow.redis.cache.windows.net"
  REDISPORT: "6380"
  STORAGE_YFLOW_PATH: /home/<USER>/StorageYflow/
  STORAGE_YFLOW_TEMP_PATH: /home/<USER>/StorageYflowTemp
  ENABLED_LANGUAJES: "es,en,pt"
  PORT: '3000'
  HOUR_CENTRALIZE: "3"
  MINUTE_CENTRALIZE: "0"
  URL_CENTRALIZE: https://callback.ysocial.net/api/centralizer
  OFFSET: "-180"
  USE_AZURE_BLOB_STORAGE: "true"
  MULTIPLE_CORES: "true"
  HOST_INSIDE_IIS: "false"
  MULTI_COMPANY: "false"
  USE_WINSTON: "false"
  FLOW_TYPE: "Bot,Lite"
  YSMART_ENABLED: "true"
  SOCKETIO: "true"
  UPDATE_CASE_PIECE_ENABLED_IN_CHAT: "true"
  DOWNLOAD_REPORT_TYPES: "rt,background"
  FILE_PATH: /home/<USER>/app/www.js
  MAX_CONCURRENT_CALLS: '1'
  QUEUE_NAME: yoizentest-yflow-notify-closed-cases
  DATABASE_TYPE: "mssql"
  EXECUTOR_URL: https://yflow-yoizen-qa-executor.local.ysocial.net/
  DNI_SERVICE_URL: https://yflow-dni-validator-qa.ysocial.net
  DNI_SERVICE_API_KEY: "57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ="
  DB_DIALECT: "mssql"
  DB_HOST: "**********"
  DB_PORT: "1433"
  DB_NAME: "yFlowTestK8s"
  DB_TIMEOUT: "30000"
  DB_CANCEL_TIMEOUT: "5000"
  DB_SSL: "false"
  DB_INTEGRATIONS_AUDIT_DIALECT: "mysql"
  DB_INTEGRATIONS_AUDIT_NAME: "yFlowIntegrstionAuditQA"
  DB_INTEGRATIONS_AUDIT_PORT: "3306"
  DB_INTEGRATIONS_AUDIT_HOST: "**********"
  DB_INTEGRATIONS_AUDIT_TIMEOUT: "30000"
  DB_INTEGRATIONS_AUDIT_CANCEL_TIMEOUT: "5000"
  DB_INTEGRATIONS_AUDIT_SSL: "false"
  DB_INTERVALS_DIALECT: mysql
  DB_INTERVALS_HOST: "**********"
  DB_INTERVALS_NAME: "yFlowIntervalsQA"
  DB_INTERVALS_PORT: "3306"
  DB_INTERVALS_MAX_POOL_CONNECTIONS: "10"
  DB_INTERVALS_TIMEOUT: "30000"
  DB_INTERVALS_CANCEL_TIMEOUT: "5000"
  DB_INTERVALS_SSL: "false"
  DB_CONTEXT_DIALECT: mysql
  DB_CONTEXT_HOST: "**********"
  DB_CONTEXT_NAME: "yFlowContextQADB"
  DB_CONTEXT_PORT: "3306"
  DB_CONTEXT_TIMEOUT: "30000"
  DB_CONTEXT_CANCEL_TIMEOUT: "5000"
  DB_CONTEXT_SSL: "false"
