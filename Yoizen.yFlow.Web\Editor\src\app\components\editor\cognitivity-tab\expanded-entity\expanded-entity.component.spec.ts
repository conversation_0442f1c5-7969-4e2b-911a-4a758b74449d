import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ExpandedEntityComponent } from './expanded-entity.component';

describe('ExpandedEntityComponent', () => {
  let component: ExpandedEntityComponent;
  let fixture: ComponentFixture<ExpandedEntityComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ExpandedEntityComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ExpandedEntityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
