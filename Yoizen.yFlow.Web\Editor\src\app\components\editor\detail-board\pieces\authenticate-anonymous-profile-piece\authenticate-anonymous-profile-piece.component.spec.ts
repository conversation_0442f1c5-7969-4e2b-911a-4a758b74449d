import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AuthenticateAnonymousProfilePieceComponent } from './authenticate-anonymous-profile-piece.component';

describe('AuthenticateAnonymousProfilePieceComponent', () => {
  let component: AuthenticateAnonymousProfilePieceComponent;
  let fixture: ComponentFixture<AuthenticateAnonymousProfilePieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AuthenticateAnonymousProfilePieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AuthenticateAnonymousProfilePieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
