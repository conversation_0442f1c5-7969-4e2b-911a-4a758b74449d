import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicGalleryPieceComponent } from './dynamic-gallery-piece.component';

describe('DynamicGalleryPieceComponent', () => {
  let component: DynamicGalleryPieceComponent;
  let fixture: ComponentFixture<DynamicGalleryPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DynamicGalleryPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicGalleryPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
