import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from "../../../../../Yoizen.yFlow.Web/helpers/sequelize";

export class HistoryDailyByAbandonedCase extends HistoryDailyBase {
    declare flowId: number;
    declare channel: string;
    declare blockId: string;
    declare total: number;
    declare version: number;
    data: { messages: any[]; };

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.flowId = data.flowId;
        this.channel = data.channel;
        this.blockId = data.blockId;
        this.total = data.total;
        this.version = data.version;

        this.data = {
            messages: []
        }
    }

    type() {
        return HistoryDailyInfoTypes.AbandonedCases;
    }
}

HistoryDailyByAbandonedCase.init({
    date: DataTypes.DATE,
    interval: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    channel: DataTypes.STRING,
    blockId: {
        type: DataTypes.STRING,
        field: 'block_id'
    },
    data: DataTypes.JSON,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER
}, {
    sequelize: sequelize,
    modelName: 'abandoned_case',
    tableName: 'abandoned_case',
    timestamps: false
})