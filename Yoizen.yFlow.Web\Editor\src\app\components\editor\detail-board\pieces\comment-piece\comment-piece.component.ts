import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { CommentPiece } from 'src/app/models/pieces/CommentPiece';
import { animate, state, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-comment-piece',
  templateUrl: './comment-piece.component.html',
  styleUrls: ['./comment-piece.component.scss'],
  animations: [
    trigger('expanded', [
      state('false', style({ height: '0px', minHeight: '0' })),
      state('true', style({ height: '*', marginTop: '0.75rem' })),
      transition('false <=> true', animate('750ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ]
})
export class CommentPieceComponent extends BasePieceVM implements OnInit {
  expanded: boolean = false;
  shortText: string;
  model: CommentPiece;

  timerId: any;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as CommentPiece;
    if(this.model.Text !== null) {
      if(this.model.Text.length > 100) {
        this.shortText = this.model.Text.slice(0, 50).concat('...')
      } else {
        this.shortText = this.model.Text;
      }
    }

  }

  changeExpandStatus() {
    this.expanded = !this.expanded;
  }

  onTextareaInput(event) {
    if(this.model.Text !== null) {
      if(this.model.Text.length > 100) {
        this.shortText = this.model.Text.slice(0, 100).concat('...')
      } else {
        this.shortText = this.model.Text;
      }
    }
  }

}
