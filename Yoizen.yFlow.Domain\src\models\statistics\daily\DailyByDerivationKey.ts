import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByDerivationKey extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    total: number;
    derivationKey: string;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, derivationKey: string, version: number }) {
        super(datetime);
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.version = data.version;

            this.derivationKey = data.derivationKey
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.DerivationKey;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "derivation_key", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.derivationKey, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_derivation_key';
    }
}