import moment from "moment";
import socialServiceTypes from "../../../../Yoizen.yFlow.Web/helpers/social-service-types";
import { Control } from "../../domain/models/executor/Control";
import { FlowExecutor } from "./FlowExecutorBase";
import * as crypto from 'crypto';
import axios from 'axios';
import { TextUtils } from "../utils/textUtils";

export class FlowExecutorGoogleRBM extends FlowExecutor {
    constructor() {
        super();
    }

    get type() {
        return socialServiceTypes.GoogleRBM;
    };

    /**
     * Retorna si el tipo de pieza es compatible con la red social
     * @param type
     * @return {boolean}
     */
    supportsPiece(type) {
        return type !== 'actions-piece' &&
            type !== 'quick-reply-piece' &&
            type !== 'video-embed-piece' &&
            type !== 'store-message-piece' &&
            type !== 'post-message-piece';
    }

    /**
     * Crea un mensaje que contiene texto
     * @param {string} text El texto del mensaje
     * @param {object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {object} control.variables Contiene la definición de variables
     * @return {object} El mensaje convertido para la red social que contiene únicamente texto
     */
    createTextMessage(text, control) {
        let message = {
            type: 'text',
            text: text
        };

        if (typeof (message.text) !== 'string' ||
            message.text.trim().length === 0) {
            return null;
        }

        return message;
    };

    /**
     * Obtiene la configuracion de un servicio
     * @param {object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {object} params parametros donde se guardá la configuracion
     */
    loadServicesConfig(control, params) {
        if (typeof (control.variables.serviceConfig) !== 'undefined' &&
            control.variables.serviceConfig !== null) {
            params.sc = control.variables.serviceConfig;
            if (typeof (params.sc['$type']) !== 'undefined') {
                delete params.sc['$type'];
            }
            if (typeof (params.sc['FromDate']) !== 'undefined') {
                delete params.sc['FromDate'];
            }
        }
    }

    /**
     * Returna la cantidad máxima de botones que soporta un mensaje
     * @returns {number} La cantidad máxima
     */
    get maxButtonsInMessage() {
        return 11;
    }

    /**
     * Returna la cantidad máxima de botones que soporta un ítem de una galería
     * @returns {number} La cantidad máxima
     */
    get maxButtonsInGalleryItem() {
        return 4;
    }

    get maxItemsInGallery() {
        return 10;
    }

    /**
     * Crea un ítem de una galería
     * @param {object} pieceImage La definición de un ítem de una galería
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @returns {object} El ítem de la galería
     */
    createGalleryItem(pieceImage, control) {
        let element = {
            title: TextUtils.replaceVariablesInText(pieceImage.title, control),
            description: null,
            media: undefined,
            suggestions: []
        };

        if (typeof (pieceImage.subtitle) === 'string' &&
            pieceImage.subtitle.length > 0) {
            element.description = TextUtils.replaceVariablesInText(pieceImage.subtitle, control);
            if (element.description.length > 2000) {
                element.description = element.description.substr(0, 2000);
            }
        }

        if (typeof (pieceImage.imageUrl) === 'string' &&
            pieceImage.imageUrl.length > 0) {
            element.media = {
                contentInfo: {
                    fileUrl: TextUtils.replaceVariablesInText(pieceImage.imageUrl, control)
                }
            };
        }

        if (element.title === null || element.title.length === 0) {
            element.title = 'Título'
        }
        else if (element.title.length > 200) {
            element.title = element.title.substr(0, 200);
        }

        if (typeof (pieceImage.buttons) !== 'undefined' &&
            pieceImage.buttons !== null &&
            pieceImage.buttons.length > 0) {
            for (var j = 0; j < pieceImage.buttons.length; j++) {
                var buttonDefinition = pieceImage.buttons[j];
                var button = this.createButton(buttonDefinition, pieceImage, control);
                if (button !== null) {
                    element.suggestions.push(button);

                    if (element.suggestions.length === this.maxButtonsInGalleryItem) {
                        break;
                    }
                }
            }
        }

        if (element.suggestions.length === 0) {
            delete element.suggestions;
        }

        return element;
    }

    /**
     * Crea un mensaje que contiene una galería
     * @param {object} piece La pieza que se está convirtiendo
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {object[]?} elements Contiene la definición de los elementos a agregar o null para utilizar los que indica la pieza
     * @return {object} El mensaje convertido para la red social
     */
    createMessageWithGallery(piece, control, elements) {
        if (typeof (elements) === 'undefined' ||
            (elements !== null && !Array.isArray(elements))) {
            elements = null;
        }

        let pieceMessage = null;
        if (elements !== null && elements.length > 0) {
            if (elements.length === 1) {
                pieceMessage = {
                    type: 'standaloneCard',
                    standaloneCard: {
                        cardContent: elements[0]
                    }
                };
            }
            else {
                pieceMessage = {
                    type: 'carouselCard',
                    carouselCard: {
                        cardContents: elements
                    }
                };

                if (pieceMessage.carouselCard.cardContents.length > this.maxItemsInGallery) {
                    pieceMessage.carouselCard.cardContents = pieceMessage.carouselCard.cardContents.slice(0, this.maxItemsInGallery);
                }
            }
        }
        else {
            if (typeof (piece.images) === 'object' &&
                Array.isArray(piece.images)) {
                if (piece.images.length === 1) {
                    pieceMessage = {
                        type: 'standaloneCard',
                        standaloneCard: {
                            cardContent: this.createGalleryItem(piece.images[0], control)
                        }
                    };
                }
                else {
                    pieceMessage = {
                        type: 'carouselCard',
                        carouselCard: {
                            cardContents: []
                        }
                    };

                    for (let i = 0; i < piece.images.length; i++) {
                        let pieceImage = piece.images[i];
                        let element = this.createGalleryItem(pieceImage, control);

                        pieceMessage.carouselCard.cardContents.push(element);

                        if (pieceMessage.carouselCard.cardContents.length === this.maxItemsInGallery) {
                            break;
                        }
                    }
                }
            }
        }

        return pieceMessage;
    };

    /**
     * Crea un mensaje que contiene botones
     * @param {object} piece La pieza que se está convirtiendo
     * @param {string} text El texto del mensaje
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @return {object} El mensaje convertido para la red social
     */
    createMessageWithButtons(piece, text, control) {
        var pieceMessage = {
            type: 'text',
            text: text,
            suggestions: []
        };

        for (var j = 0; j < piece.Buttons.length; j++) {
            var pieceButton = piece.Buttons[j];
            var button = this.createButton(pieceButton, piece, control, undefined, j);
            if (button !== null) {
                pieceMessage.suggestions.push(button);
            }

            if (pieceMessage.suggestions.length === this.maxButtonsInMessage)
                break;
        }

        if (pieceMessage.suggestions.length === 0) {
            console.log(`[${control.body.message.id}] El mensaje quedó sin botones por lo que se devuelve un mensaje de texto normal`);
            delete pieceMessage.suggestions;
        }

        return pieceMessage;
    };

    /**
     * Crea un botón para ser agregado a un mensaje
     * @param {object} buttonDefinition La definición de un botón
     * @param {object} parentPiece La definición de la pieza donde está el botón
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {boolean} [shouldReplaceVars] Indica si se debe procesar las variables dentro de los textos
     * @param {number} index Contiene el índice del botón, para generar distintos payloads
     * @return {?object} El botón o null si no se pudo crear el botón porque no es compatible
     */
    createButton(buttonDefinition: any, parentPiece: any, control: Control, shouldReplaceVars = true, index?: number): any {
        if (parentPiece !== null) {
            if (typeof (buttonDefinition.HasVisibilityCondition) !== 'undefined' &&
                buttonDefinition.HasVisibilityCondition === true) {
                if (!this.evaluateCondition(control, buttonDefinition.FirstOperand, buttonDefinition.Operator, buttonDefinition.SecondOperand, false)) {
                    console.log(`[${control.body.message.id}] El botón ${buttonDefinition.Uid} de la pieza ${parentPiece.Uid} no aplica a la condición de visibilidad. Se saltea`);
                    return null;
                }
            }
        }

        let button = null;
        switch (buttonDefinition.Type) {
            case 1: //ButtonType.Url
                button = {
                    type: 'url',
                    text: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Name, control) : buttonDefinition.Name,
                    url: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Url, control) : buttonDefinition.Url,
                    postback: 'pepe'
                };
                break;
            case 2: //ButtonType.Redirect:
                button = {
                    type: 'postback',
                    text: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Name, control) : buttonDefinition.Name,
                    postback: undefined
                };

                let payload = this.createPayload(buttonDefinition, parentPiece, control, buttonDefinition.BlockID, index);
                button.postback = JSON.stringify(payload);
                break;
            case 5: //ButtonType.Location
                button = {
                    type: 'location',
                    text: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Name, control) : buttonDefinition.Name,
                    postback: 'pepe'
                };
                break;
            case 7: //ButtonType.Dial
                button = {
                    type: 'dial',
                    text: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Name, control) : buttonDefinition.Name,
                    phoneNumber: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.PhoneNumber, control) : buttonDefinition.PhoneNumber,
                    postback: 'pepe'
                };
                break;
            case 8: //ButtonType.SendLocation
                let latitude = shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Latitude, control) : buttonDefinition.Latitude;
                if (typeof (latitude) === 'string' && latitude.length === 0) {
                    latitude = undefined;
                }
                else if (isNaN(latitude)) {
                    latitude = undefined;
                }
                else {
                    latitude = parseFloat(latitude);
                }

                let longitude = shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Longitude, control) : buttonDefinition.Longitude;
                if (typeof (longitude) === 'string' && longitude.length === 0) {
                    longitude = undefined;
                }
                else if (isNaN(longitude)) {
                    longitude = undefined;
                }
                else {
                    longitude = parseFloat(longitude);
                }

                if (typeof (longitude) !== 'number' || typeof (latitude) !== 'number') {
                    return null;
                }

                button = {
                    type: 'send_location',
                    text: shouldReplaceVars ? TextUtils.replaceVariablesInText(buttonDefinition.Name, control) : buttonDefinition.Name,
                    coordinates: {
                        latitude: latitude,
                        longitude: longitude
                    },
                    postback: 'pepe'
                };
                break;
        }

        if (button !== null && typeof (button.text) === 'string') {
            if (button.text.length === 0) {
                return null;
            }

            if (button.text.length > 25) {
                button.text = button.text.substr(0, 20);
            }
        }

        return button;
    }


    /**
     * Crea un mensaje de adjunto
     * @param {object} piece La pieza que contiene la definición de la acción
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {?object} [control.runtimeVariables] Contiene la definición de variables de runtime
     * @param {?object} [control.flow] Contiene la definición del flow que se está ejecutando
     * @param {int} control.flow.id Contiene el código del flow que se está ejecutando
     * @param {string} control.flow.name Contiene el nombre del flow que se está ejecutando
     * @return {object} El mensaje convertido para la red social que contiene el adjunto
     */
    createAttachmentFromUrlMessage(piece, control) {
        let message = {
            type: 'attachment',
            attachment: {
                type: 'url',
                mimeType: TextUtils.replaceVariablesInText(piece.MimeType, control),
                name: TextUtils.replaceVariablesInText(piece.Name, control),
                url: TextUtils.replaceVariablesInText(piece.Url, control),
                isPublicUrl: false
            }
        };

        if (typeof (piece.IsPublicUrl) === 'boolean') {
            message.attachment.isPublicUrl = piece.IsPublicUrl;
        }

        return message;
    };

    /**
     * Crea un sticker
     * @param {object} piece La pieza que contiene la definición de la acción
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {?object} [control.runtimeVariables] Contiene la definición de variables de runtime
     * @param {?object} [control.flow] Contiene la definición del flow que se está ejecutando
     * @param {int} control.flow.id Contiene el código del flow que se está ejecutando
     * @param {string} control.flow.name Contiene el nombre del flow que se está ejecutando
     * @return {object} El mensaje convertido para la red social que contiene el adjunto
     */
    createStickerFromUrlMessage(piece, control) {
        let message = {
            type: 'attachment',
            attachment: {
                type: 'url',
                mimeType: TextUtils.replaceVariablesInText(piece.MimeType, control),
                url: TextUtils.replaceVariablesInText(piece.Url, control),
                isPublicUrl: true,
                name: 'sticker'
            }
        };
        return message;
    };

    /**
     * Crea un mensaje de adjunto
     * @param {object} piece La pieza que contiene la definición de la acción
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {?object} [control.runtimeVariables] Contiene la definición de variables de runtime
     * @param {?object} [control.flow] Contiene la definición del flow que se está ejecutando
     * @param {int} control.flow.id Contiene el código del flow que se está ejecutando
     * @param {string} control.flow.name Contiene el nombre del flow que se está ejecutando
     * @param {string} binaryData El contenido del adjunto en base64
     * @return {object} El mensaje convertido para la red social que contiene el adjunto
     */
    async createStickerFromBinaryMessage(piece, control, binaryData) {
        let pieceMessage = {
            type: 'attachment',
            attachment: {
                type: 'file',
                mimeType: TextUtils.replaceVariablesInText(piece.MimeType, control),
                name: TextUtils.replaceVariablesInText(piece.Name, control),
                filename: null
            }
        };

        pieceMessage.attachment.filename = await this.saveAttachmentInTempFile(control.flow.id, binaryData, pieceMessage.attachment.name, pieceMessage.attachment.type);

        return pieceMessage;
    };

    /**
     * Ejecuta el proceso de notificacion de pago
     * @param {object} params Información envíada relacionada en el botón de iniciar sesión
     * @param {string} authorizationCode El código de autorización generado por la página que autenticó al usuario
     */
    async newPayment(params, approved) {
        let now = moment().valueOf();
        let url = `https://callback.ysocial.net/api/payments/new`;
        let data = {
            socialServiceType: params.sst,
            accountId: params.sc.FullPhoneNumber,
            blockDest: approved ? params.ps : params.pe,
            socialUserId: params.u,
            timestamp: now,
            status: approved ? 'approved' : 'denied',
        };

        let signature = crypto.createHmac('sha1', 'd4061499a5eb796b73f4569c0df91988')
            .update(JSON.stringify(data))
            .digest('hex').toLowerCase();

        try {
            console.log(`Se invocará a la nube de ySocial para informar de un mensaje de pago del servicio ${JSON.stringify(params.sc)}`);

            await axios({
                method: 'POST',
                url: url,
                data: data,
                headers: {
                    'X-Hub-Signature': `sha1=${signature}`
                }
            });

            console.log(`Se informó a la nube de ySocial de un mensaje compartido de pago del servicio ${JSON.stringify(params.sc)}`);
        }
        catch (e) {
            console.log(`Se produjo un error invocando a la nube de ySocial para informar de un nuevo mensaje de pago: ${e}`);
            throw e;
        }
    }

    /**
     * Crea un mensaje de adjunto
     * @param {object} piece La pieza que contiene la definición de la acción
     * @param {?object} control Objeto que indica algunos controles extras luego de la ejecución
     * @param {?boolean} control.shouldDerive Indica si el flow tiene indicado que debe ser transferido a un agente
     * @param {?string} control.deriveKey Indica opcionalmente una clave de derivación
     * @param {?object} control.variables Contiene la definición de variables
     * @param {?object} control.lastBlock Contiene el último bloque que se ejecutó
     * @param {?object} [control.runtimeVariables] Contiene la definición de variables de runtime
     * @param {?object} [control.flow] Contiene la definición del flow que se está ejecutando
     * @param {int} control.flow.id Contiene el código del flow que se está ejecutando
     * @param {string} control.flow.name Contiene el nombre del flow que se está ejecutando
     * @param {string} binaryData El contenido del adjunto en base64
     * @return {object} El mensaje convertido para la red social que contiene el adjunto
     */
    async createAttachmentFromBinaryMessage(piece, control, binaryData) {
        let pieceMessage = {
            type: 'attachment',
            attachment: {
                type: 'file',
                mimeType: TextUtils.replaceVariablesInText(piece.MimeType, control),
                name: TextUtils.replaceVariablesInText(piece.Name, control),
                filename: null
            }
        };

        pieceMessage.attachment.filename = await this.saveAttachmentInTempFile(control.flow.id, binaryData, pieceMessage.attachment.name, pieceMessage.attachment.type);

        return pieceMessage;
    };
}