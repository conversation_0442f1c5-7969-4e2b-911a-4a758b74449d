import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {isNumeric} from '../../../../../urlutils.module';
import {CoordinatesPiece} from "../../../../../models/pieces/CoordinatesPiece";
import { VariableDefinition } from "../../../../../models/VariableDefinition";
import { Concatenate } from "../../../../../models/pieces/Concatenate";
import { ChannelTypes } from 'src/app/models/ChannelType';

@Component({
  selector: 'app-coordinates-piece',
  templateUrl: './coordinates-piece.component.html',
  styleUrls: ['./coordinates-piece.component.scss']
})
export class CoordinatesPieceComponent extends BasePieceVM implements OnInit {

  model: CoordinatesPiece;
  channelTypes = ChannelTypes;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as CoordinatesPiece;
  }

  canShowPreview(): boolean {
    if (!isNumeric(this.model.Longitude) || !isNumeric(this.model.Latitude)) {
      return false;
    }

    let latitude = parseFloat(this.model.Latitude);
    if (latitude < -90 || latitude > 90) {
      return false;
    }

    let longitude = parseFloat(this.model.Longitude);
    if (longitude < -180 || longitude > 180) {
      return false;
    }

    return true;
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  get customVariables(): VariableDefinition[] {
    return Concatenate.SpecialVar;
  }

  buildMapsUrl() {
    var GoogleConfiguration = this.editorService.getGoogleConfiguration().ApiKey;
    GoogleConfiguration = GoogleConfiguration === null || GoogleConfiguration === '' ? 'AIzaSyAvQeY3t1kRyhET_7HGSmvnGabyJjhDf3Q' : GoogleConfiguration;
    if (this.model.LabelIcon !== null &&
      this.model.LabelIcon.startsWith('http')) {
      return `https://maps.googleapis.com/maps/api/staticmap?key=${GoogleConfiguration}&markers=icon:${this.model.LabelIcon}|${this.model.Latitude},${this.model.Longitude}&size=360x360&zoom=13`;
    }
    else {
      return `https://maps.googleapis.com/maps/api/staticmap?key=${GoogleConfiguration}&markers=color:red|${this.model.Latitude},${this.model.Longitude}&size=360x360&zoom=13`;
    }
  }
}
