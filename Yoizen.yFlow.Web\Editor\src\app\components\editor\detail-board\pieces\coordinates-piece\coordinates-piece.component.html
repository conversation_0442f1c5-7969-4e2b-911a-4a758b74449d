<div class="coordinates card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-map-pin"></span> {{ 'CARD_COORDINATES_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'COORDINATES_INFO' | translate }}
  </div>
  <div class="latitude">
    <span class="title">{{ 'COORDINATES_LATITUDE' | translate }}:</span>
    <app-input-with-variables class="input"
      [placeholder]="'COORDINATES_LATITUDE' | translate"
      [(value)]="model.Latitude"
      [ngClass]="{'validation-error': !model.isLatitudeValid(editorService)}"
      [wideInput]="true"
      [isTextArea]="false"
      [isHtml]="false"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="longitude">
    <span class="title">{{ 'COORDINATES_LONGITUDE' | translate }}:</span>
    <app-input-with-variables class="input"
      [placeholder]="'COORDINATES_LONGITUDE' | translate"
      [(value)]="model.Longitude"
      [ngClass]="{'validation-error': !model.isLongitudeValid(editorService)}"
      [wideInput]="true"
      [isTextArea]="false"
      [isHtml]="false"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="icon-prop" *ngIf="(model.Channel === channelTypes.Chat)">
    <span class="title">{{'MULTIPLECOORDINATES_ICON_PROP' | translate}}:</span>
    <app-input-with-variables
      [placeholder]="'MULTIPLECOORDINATES_ICON_PROP' | translate"
      [(value)]="model.LabelIcon"
      [wideInput]="true"
      [customVariableList]="customVariables"
      [variableFinder]="searchForVariable.bind(this)"
      [JoinCustomVariable]="true"
      [disabled]="readOnly"
      class="input-variable">
    </app-input-with-variables>
  </div>
  <div class="shorten-url-prop" *ngIf="(model.Channel === channelTypes.WhatsApp)">
    <span class="title">{{'COORDINATES_SENDGOOGLELINK' | translate}}:</span>
    <ui-switch [(ngModel)]="model.SendGoogleLink" [disabled]="readOnly"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="longitude" *ngIf="(model.Channel === channelTypes.WhatsApp && !model.SendGoogleLink)">
    <span class="title">{{ 'COORDINATES_NAME' | translate }}:</span>
    <app-input-with-variables class="input"
                              [placeholder]="'COORDINATES_NAME' | translate"
                              [(value)]="model.Name"
                              [wideInput]="true"
                              [isTextArea]="false"
                              [isHtml]="false"
                              [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="longitude" *ngIf="(model.Channel === channelTypes.WhatsApp && !model.SendGoogleLink)">
    <span class="title">{{ 'COORDINATES_ADDRESS' | translate }}:</span>
    <app-input-with-variables class="input"
                              [placeholder]="'COORDINATES_ADDRESS' | translate"
                              [(value)]="model.Address"
                              [wideInput]="true"
                              [isTextArea]="false"
                              [isHtml]="false"
                              [disabled]="readOnly">
    </app-input-with-variables>
  </div>
  <div class="shorten-url-prop" *ngIf="(model.Channel === channelTypes.WhatsApp && model.SendGoogleLink)">
    <span class="title">{{'COORDINATES_SHORTENURL_PROP' | translate}}:</span>
    <ui-switch [(ngModel)]="model.ShortenUrl" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="map" *ngIf="canShowPreview()">
    <img src="{{buildMapsUrl()}}" />
  </div>
</div>
