import { FtpConfigurations } from './../../models/FtpConfigurations';
import { FTPData } from './../../models/FTPData';
import { Component, EventEmitter, OnInit } from '@angular/core';
import { ServerService } from "../../services/server.service";
import { ActivatedRoute, Router } from "@angular/router";
import { ModalService } from "../../services/Tools/ModalService";
import { EditorService } from "../../services/editor.service";
import { getTokenPayload, updateToken, getDefaultLanguaje, getEnabledLanguajes, ySmartEnabled } from "../../Utils/window";
import { ToasterService } from "angular2-toaster";
import { TranslateService } from "@ngx-translate/core";
import { StatusResponse, StatusResponseUsers } from "../../models/StatusResponse";
import { finalize } from "rxjs/operators";
import { ErrorPopupComponent } from "../error-popup/error-popup.component";
import { UserPopupComponent } from "./user-popup/user-popup.component";
import { UserPublicInfo } from "../../models/UserPublicInfo";
import { FtpConfigComponent } from '../editor/popups/ftp-config/ftp-config.component';
import { TypedJSON } from 'typedjson';
import { SFTPData } from 'src/app/models/SFTPData';
import { SftpConfigComponent } from '../editor/popups/sftp-config/sftp-config.component';
import { clone } from "lodash";
import { SmtpConfiguration } from "../../models/SmtpConfiguration";
import { LoginType } from '../../models/UserPublicInfo';
import { SAMLConfiguration } from '../../models/SAMLConfiguration';
import { DeleteUserPopupComponent } from './delete-user-popup/delete-user-popup.component';

@Component({
  selector: "app-configuration",
  templateUrl: "./configuration.component.html",
  styleUrls: ["./configuration.component.scss"],
  host: { class: "container-fluid-no-padding" },
})
export class ConfigurationComponent implements OnInit {
  homeUrl: string;
  canEditFtp: boolean = false;
  canEditSmtp: boolean = false;
  userId: number = 0;
  isAdmin: boolean = false;
  super: boolean = false;
  isYoizenAdmin: boolean = false;
  paramsSub: any;
  showUsersPanel: boolean = false;
  showDeletePanel: boolean = false;
  userCanValidatePasswords: boolean = false;
  currentPassword: string = null;
  newPassword: string = null;
  confirmationPassword: string = null;
  loading: boolean = false;
  loadingUsers: boolean = false;
  users: UserPublicInfo[] = [];
  passwordStrengthValue: number = 0;
  userName: string = null;
  langs: string[] = [];
  currentLang: string;
  ftpConfig: FtpConfigurations = null;
  smtpConfig: SmtpConfiguration = null;
  ySmartEnabled: boolean = false;
  loginTypes = LoginType;
  samlConfig: SAMLConfiguration = new SAMLConfiguration();
  isContingencyBot: boolean = false;

  // Statistics configuration
  statisticsConfig = {
    consolidatedEnabled: true,
    detailedEnabled: true,
  };

  constructor(
    public editorService: EditorService,
    private serverService: ServerService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute,
    private toasterService: ToasterService,
    private translateService: TranslateService
  ) {
    this.samlConfig.enabled = false;
    this.samlConfig.allowLocalUsers = true;
    this.loadSamlConfig();
    this.isContingencyBot = this.editorService.getIsContingencyBot();
  }

  ngOnInit() {
    var self = this;
    if (typeof getEnabledLanguajes === "function") {
      getEnabledLanguajes()
        .split(",")
        .forEach((item) => {
          this.langs.push(item);
        });
    }

    let tokenPayload = getTokenPayload();
    this.userName = tokenPayload.name;
    this.currentLang = tokenPayload.lang ? tokenPayload.lang : this.langs[0];
    this.canEditFtp = tokenPayload.admin;
    this.canEditSmtp = tokenPayload.admin && !this.isContingencyBot;
    this.showDeletePanel = tokenPayload.delete;
    this.isAdmin = tokenPayload.admin;
    this.userId = tokenPayload.uid;
    this.userCanValidatePasswords = tokenPayload.canValidatePasswords;
    this.super =
      this.userName === "administrador" || this.userName !== "ysocial";
    this.isYoizenAdmin =
      getTokenPayload().name === "Administrador" ||
      getTokenPayload().name === "administrador";

    if (tokenPayload.admin) {
      this.showUsersPanel = true;
      this.showDeletePanel = true;

      this.ftpConfig = new FtpConfigurations();
      this.serverService.getAutomaticReportConfig().subscribe(
        (status: StatusResponse) => {
          let { data } = status;
          if (data) {
            this.ftpConfig = TypedJSON.parse<FtpConfigurations>(
              JSON.stringify(data.content),
              FtpConfigurations
            );
          }
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("FTP_GET_CONFIG_ERROR")
          );
          console.log(error);
        }
      );

      this.smtpConfig = new SmtpConfiguration();
      this.serverService.getSmtpConfig().subscribe(
        (status: StatusResponse) => {
          let { data } = status;
          if (data) {
            this.smtpConfig = TypedJSON.parse<SmtpConfiguration>(
              JSON.stringify(data.content),
              SmtpConfiguration
            );
          }
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("FTP_GET_CONFIG_ERROR")
          );
          console.log(error);
        }
      );

      this.loading = true;
      this.loadingUsers = true;
      this.serverService
        .listUsers()
        .pipe(
          finalize(() => {
            this.loading = false;
            this.loadingUsers = false;
          })
        )
        .subscribe(
          (status: StatusResponseUsers) => {
            for (let i = 0; i < status.data.length; i++) {
              var user = status.data[i];
              if (user instanceof UserPublicInfo) {
                this.users.push(user);
              } else {
                this.users.push(UserPublicInfo.fromAny(<any>user));
              }

              if (user.login_type === null || user.login_type === undefined) {
                user.login_type = LoginType.LOCAL;
              }
            }
          },
          (error) => {
            var errorDesc: any = {
              Title: "CANNOT_RETRIEVE_USERS_TITLE",
              Desc: "CANNOT_RETRIEVE_USERS_DESC",
            };
            this.modalService.init(ErrorPopupComponent, errorDesc, {});
          }
        );
    } else if (tokenPayload.canValidatePasswords) {
      this.loading = true;
      this.loadingUsers = true;
      this.serverService
        .listUsers()
        .pipe(
          finalize(() => {
            this.loading = false;
            this.loadingUsers = false;
          })
        )
        .subscribe(
          (status: StatusResponseUsers) => {
            for (let i = 0; i < status.data.length; i++) {
              var user = status.data[i];
              if (user instanceof UserPublicInfo) {
                this.users.push(user);
              } else {
                this.users.push(UserPublicInfo.fromAny(<any>user));
              }

              if (user.login_type === null || user.login_type === undefined) {
                user.login_type = LoginType.LOCAL;
              }
            }
          },
          (error) => {
            var errorDesc: any = {
              Title: "CANNOT_RETRIEVE_USERS_TITLE",
              Desc: "CANNOT_RETRIEVE_USERS_DESC",
            };
            this.modalService.init(ErrorPopupComponent, errorDesc, {});
          }
        );
    }

    this.paramsSub = this.route.params.subscribe((params) => {
      if (params["companyName"]) {
        self.homeUrl = `/${params["companyName"]}/home`;
      } else {
        self.homeUrl = "/home";
      }
    });

    this.ySmartEnabled = ySmartEnabled().toLocaleLowerCase() === "true";
    this.loadSamlConfig();
    this.loadStatisticsConfig();
  }

  loadUsers(): void {
    this.loading = true;
    this.loadingUsers = true;
    this.serverService
      .listUsers()
      .pipe(
        finalize(() => {
          this.loading = false;
          this.loadingUsers = false;
        })
      )
      .subscribe(
        (status: StatusResponseUsers) => {
          this.users = status.data.map((user) => {
            return user instanceof UserPublicInfo
              ? user
              : UserPublicInfo.fromAny(<any>user);
          });
        },
        (error) => {
          const errorDesc: any = {
            Title: "CANNOT_RETRIEVE_USERS_TITLE",
            Desc: "CANNOT_RETRIEVE_USERS_DESC",
          };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        }
      );
  }

  initAutoConfig() {
    this.serverService.getAutomaticReportConfig().subscribe(
      (status: StatusResponse) => {
        let { data } = status;
        if (data) {
          Object.assign(this.ftpConfig, data.content);
        }
      },
      (error) => {
        this.toasterService.pop(
          "error",
          this.translateService.instant("FTP_GET_CONFIG_ERROR")
        );
        console.log(error);
      }
    );
  }

  showDashboard() {
    this.router.navigateByUrl(this.homeUrl);
  }

  changeLang() {
    let tokenPayload = getTokenPayload();
    this.loading = true;

    let uid = tokenPayload.uid;
    if (typeof uid === "string") {
      uid = parseInt(uid, 10);
      if (isNaN(uid)) {
        uid = tokenPayload.uid;
      }
    }

    this.serverService
      .updateLang(uid, this.currentLang)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(
        (status: StatusResponse) => {
          this.translateService.use(
            this.currentLang.toLocaleLowerCase() === this.langs[0]
              ? getDefaultLanguaje()
              : this.currentLang
          );
          updateToken(status["token"]);
          this.toasterService.pop(
            "success",
            this.translateService.instant("UPDATE_LANGUAGE_SUCCESS")
          );
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("UPDATE_LANGUAGE_ERROR")
          );
          console.log(error);
        }
      );
  }

  changePassword() {
    // Validación de contraseña actual
    if (this.currentPassword === null || this.currentPassword.length === 0) {
      this.toasterService.pop(
        "error",
        this.translateService.instant(
          "CHANGE_PASSWORD_INVALID_CURRENT_PASSWORD"
        )
      );
      return;
    }

    // Validación de nueva contraseña
    if (this.newPassword === null || this.newPassword.length === 0) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("CHANGE_PASSWORD_INVALID_NEW_PASSWORD")
      );
      return;
    }

    // Si la nueva contraseña es igual a la actual, permitimos la actualización sin más validaciones
    if (this.newPassword === this.currentPassword) {
      return;
    }

    // Solo validamos confirmación si la nueva contraseña es diferente
    if (
      this.confirmationPassword === null ||
      this.confirmationPassword.length === 0 ||
      this.confirmationPassword !== this.newPassword
    ) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("CHANGE_PASSWORD_INVALID_CONFIRMATION")
      );
      return;
    }

    this.loading = true;

    this.serverService
      .changePassword(this.currentPassword, this.newPassword)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(
        (status: StatusResponse) => {
          this.toasterService.pop(
            "success",
            this.translateService.instant("CHANGE_PASSWORD_SUCCESS")
          );
          this.currentPassword = null;
          this.newPassword = null;
          this.confirmationPassword = null;
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("CHANGE_PASSWORD_ERROR")
          );
        }
      );
  }

  editUser(user: UserPublicInfo) {
    var loadingEventEmitter = new EventEmitter<boolean>();
    loadingEventEmitter.subscribe((loading: boolean) => {
      this.loading = loading;
    });

    var acceptEventEmitter = new EventEmitter<UserPublicInfo>();
    acceptEventEmitter.subscribe((updatedUser: UserPublicInfo) => {
      user.is_admin = updatedUser.is_admin;
      user.can_publish = updatedUser.can_publish;
      user.can_edit = updatedUser.can_edit;
      user.can_see_statistics = updatedUser.can_see_statistics;
      user.can_validate_passwords = updatedUser.can_validate_passwords;
      user.can_access_ysmart = updatedUser.can_access_ysmart;
      user.can_delete = updatedUser.can_delete;
      user.enabled = updatedUser.enabled;
      user.login_type = updatedUser.login_type;
      this.toasterService.pop(
        "success",
        this.translateService.instant("USER_UPDATED_SUCCESS")
      );
    });

    this.modalService.init(
      UserPopupComponent,
      {
        user: user,
        validator: null,
      },
      {
        loading: loadingEventEmitter,
        acceptAction: acceptEventEmitter,
      }
    );
  }

  deleteUser(user: UserPublicInfo) {
    var loadingEventEmitter = new EventEmitter<boolean>();
    loadingEventEmitter.subscribe((loading: boolean) => {
      this.loading = loading;
    });

    var acceptEventEmitter = new EventEmitter<UserPublicInfo>();
    acceptEventEmitter.subscribe((deleteUser: UserPublicInfo) => {
      user.enabled = deleteUser.enabled;
      user.is_deleted = deleteUser.is_deleted;
      this.toasterService.pop(
        "success",
        this.translateService.instant("USER_DELETED_SUCCESS")
      );
    });

    this.modalService.init(
      DeleteUserPopupComponent,
      {
        user: user,
        validator: null,
      },
      {
        loading: loadingEventEmitter,
        acceptAction: acceptEventEmitter,
      }
    );
  }

  createUser() {
    if (!this.samlConfig.allowLocalUsers) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("LOCAL_USERS_NOT_ALLOWED")
      );
      return;
    }

    var loadingEventEmitter = new EventEmitter<boolean>();
    loadingEventEmitter.subscribe((loading: boolean) => {
      this.loading = loading;
    });

    var acceptEventEmitter = new EventEmitter<UserPublicInfo>();
    acceptEventEmitter.subscribe((user: UserPublicInfo) => {
      this.users.push(user);
      this.toasterService.pop(
        "success",
        this.translateService.instant("USER_CREATED_SUCCESS")
      );
    });

    this.modalService.init(
      UserPopupComponent,
      {
        user: null,
        validator: (name: string) => {
          var existingUser = this.users.find(
            (u) => u.name.toLowerCase() === name.toLowerCase()
          );
          if (typeof existingUser === "undefined" || existingUser === null) {
            return true;
          }
          return false;
        },
      },
      {
        loading: loadingEventEmitter,
        acceptAction: acceptEventEmitter,
      }
    );
  }

  calcPasswordStrength($event: Event) {
    if (this.newPassword === null || this.newPassword.length === 0) {
      this.passwordStrengthValue = 0;
      return;
    }

    // @ts-ignore
    if (typeof zxcvbn !== "undefined") {
      // @ts-ignore
      let result = zxcvbn(this.newPassword, [this.userName]);
      this.passwordStrengthValue = result.score;
    }
  }

  showUserModal(user) {
    return (
      this.userId != user.id &&
      user.name.toLowerCase() !== "administrador" &&
      user.name.toLowerCase() !== "ysocial" &&
      (this.userCanValidatePasswords || this.showUsersPanel || this.isAdmin) &&
      user.id !== 1 &&
      !(!this.userCanValidatePasswords && user.is_admin)
    );
  }

  showDeleteModal(user) {
    return (
      this.userId != user.id &&
      user.name.toLowerCase() !== "administrador" &&
      user.name.toLowerCase() !== "ysocial" &&
      (this.showDeletePanel || this.isAdmin) &&
      user.id !== 1
    );
  }

  saveAutoReportConfig() {
    if (this.ftpConfig.isValid()) {
      this.serverService.setAutomaticReportConfig(this.ftpConfig).subscribe(
        (result) => {
          this.toasterService.pop(
            "success",
            this.translateService.instant("CONFIG_UPDATED")
          );
          console.log("AutoReportConfig: ", result);
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("COULD_NOT_UPDATE_CONFIG")
          );
          console.log(error);
        }
      );
    } else {
      this.toasterService.pop(
        "error",
        this.translateService.instant("INVALID_FTP")
      );
    }
  }

  openFTPModal() {
    var ftpEventEmitter = new EventEmitter<FTPData>();
    ftpEventEmitter.subscribe((ftpData: FTPData) => {
      this.ftpConfig.FTPData = TypedJSON.parse<FTPData>(
        JSON.stringify(ftpData),
        FTPData
      );
    });
    let data: FTPData = clone(this.ftpConfig.FTPData);
    this.modalService.init(
      FtpConfigComponent,
      { ftpData: data, isAdmin: this.isAdmin },
      { saveFtpAction: ftpEventEmitter }
    );
  }

  openSFTPModal() {
    var sftpEventEmitter = new EventEmitter<SFTPData>();
    sftpEventEmitter.subscribe((sftpData: SFTPData) => {
      this.ftpConfig.SFTPData = TypedJSON.parse<SFTPData>(
        JSON.stringify(sftpData),
        SFTPData
      );
    });
    let data: SFTPData = clone(this.ftpConfig.SFTPData);
    this.modalService.init(
      SftpConfigComponent,
      { sftpData: data, isAdmin: this.isAdmin },
      { saveSftpAction: sftpEventEmitter }
    );
  }

  saveSmtpConfig() {
    if (!this.smtpConfig.isValid()) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("SMTP_CONFIG_ERROR")
      );
      return;
    }

    this.serverService.setSmtpConfig(this.smtpConfig).subscribe(
      (result) => {
        this.toasterService.pop(
          "success",
          this.translateService.instant("CONFIG_UPDATED")
        );
        console.log("SmtpConfig: ", result);
      },
      (error) => {
        this.toasterService.pop(
          "error",
          this.translateService.instant("COULD_NOT_UPDATE_CONFIG")
        );
        console.log(error);
      }
    );
  }

  testSmtpConfig() {
    if (!this.smtpConfig.isValid()) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("SMTP_CONFIG_ERROR")
      );
      return;
    }

    this.loading = true;
    this.serverService.testSmtpConfig(this.smtpConfig).subscribe(
      (result) => {
        this.loading = false;
        this.toasterService.pop(
          "success",
          this.translateService.instant("SMTP_CONFIG_TEST_SUCCESS")
        );
      },
      ({ error }) => {
        this.loading = false;
        this.toasterService.pop(
          "error",
          this.translateService.instant("SMTP_CONFIG_TEST_FAILED")
        );
        console.log(error.message);
      }
    );
  }

  loadSamlConfig() {
    this.samlConfig = new SAMLConfiguration();
    this.serverService.getSamlConfig().subscribe(
      (status: StatusResponse) => {
        let { data } = status;
        if (data) {
          this.samlConfig = TypedJSON.parse<SAMLConfiguration>(
            JSON.stringify(data.content),
            SAMLConfiguration
          );
        }
      },
      (error) => {
        this.toasterService.pop(
          "error",
          this.translateService.instant("COULD_NOT_FETCH_SAML_CONFIG")
        );
        console.log("ConfigurationsComponenets.loadSamlConfig: " + error);
        this.samlConfig = new SAMLConfiguration();
      }
    );
  }

  loadStatisticsConfig() {
    this.serverService.getStatisticsConfig().subscribe(
      (status: StatusResponse) => {
        let { data } = status;
        if (data && data.content) {
          this.statisticsConfig = {
            consolidatedEnabled:
              data.content.consolidatedEnabled !== undefined
                ? data.content.consolidatedEnabled
                : true,
            detailedEnabled:
              data.content.detailedEnabled !== undefined
                ? data.content.detailedEnabled
                : true,
          };
        }
      },
      (error) => {
        this.toasterService.pop(
          "error",
          this.translateService.instant("COULD_NOT_FETCH_STATISTICS_CONFIG")
        );
        console.log("ConfigurationsComponent.loadStatisticsConfig: " + error);
        // Keep default values if error
      }
    );
  }

  saveSamlConfig() {
    if (!this.samlConfig.isValid()) {
      this.toasterService.pop(
        "error",
        this.translateService.instant("SAML_CONFIG_ERROR")
      );
      return;
    }

    this.serverService.setSamlConfig(this.samlConfig).subscribe(
      (status: StatusResponse) => {
        this.toasterService.pop(
          "success",
          this.translateService.instant("CONFIG_UPDATED")
        );
      },
      (error) => {
        this.toasterService.pop(
          "error",
          this.translateService.instant("COULD_NOT_UPDATE_CONFIG")
        );
        console.log(error);
      }
    );
  }

  saveStatisticsConfig() {
    this.loading = true;
    this.serverService
      .setStatisticsConfig(this.statisticsConfig)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(
        (status: StatusResponse) => {
          this.toasterService.pop(
            "success",
            this.translateService.instant("CONFIG_UPDATED")
          );
        },
        (error) => {
          this.toasterService.pop(
            "error",
            this.translateService.instant("COULD_NOT_UPDATE_CONFIG")
          );
          console.log(error);
        }
      );
  }

  private readonly SPECIAL_USERS = ["administrador", "ysocial"];

  get filteredUsers(): UserPublicInfo[] {
    if (this.SPECIAL_USERS.includes(this.userName.toLowerCase())) {
      return this.users;
    }

    return this.users.filter(
      (user) => !this.SPECIAL_USERS.includes(user.name.toLowerCase())
    );
  }
}
