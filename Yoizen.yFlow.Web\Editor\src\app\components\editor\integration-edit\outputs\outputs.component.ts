import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { IntegrationOutput } from '../../../../models/integration/IntegrationOutput';
import { TypeDefinition } from '../../../../models/TypeDefinition';
import { VariableDefinition } from '../../../../models/VariableDefinition';

interface BooleanOptions {
  name: string;
  value: boolean
}

@Component({
  selector: 'app-outputs',
  templateUrl: './outputs.component.html',
  styleUrls: ['./outputs.component.scss']
})
export class OutputsComponent implements OnInit {
  getVariablesTypes() {
    if (this.isBinary) {
      return VariableDefinition.variableType.filter(value => value.value === TypeDefinition.ByteArray);
    }
    else {
      return VariableDefinition.variableType.filter(value => value.value !== TypeDefinition.ByteArray);
    }
  }


  @Input() output: IntegrationOutput;
  @Input() isBinary: boolean = false;
  @Input() isInUse: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() pending: boolean = false;
  @Output() onDelete = new EventEmitter();
  @Output() onFinish = new EventEmitter();

  booleanOptions: BooleanOptions[] = [{ name: "INTEGRATION_INPUTFIELDS_YES", value: true }, { name: "INTEGRATION_INPUTFIELDS_NO", value: false }]

  constructor() {
  }

  finish() {
    if (!this.empty(this.output.name) && this.output.type && !this.empty(this.output.path)) {
      this.pending = false;
      this.onFinish.emit();
    }
  }                             

  empty(str: string): Boolean {
    return str == null || str.length == 0;
  }

  booleanOpts() {
    return this.booleanOptions;
  }

  ngOnInit() {
  }

  delete() {
    this.onDelete.emit();
  }
}
