import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { OperatorDefinitions } from '../../../../../models/OperatorType';
import { ConditionPiece } from '../../../../../models/pieces/ConditionPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';

@Component({
  selector: 'app-condition-piece',
  templateUrl: './condition-piece.component.html',
  styleUrls: ['./condition-piece.component.scss']
})
export class ConditionPieceComponent extends BasePieceVM implements OnInit {

  model: ConditionPiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

   ngOnInit() {
    this.model = this.context as ConditionPiece;
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  showOperand() : boolean {
    for (let i = 0; i < OperatorDefinitions.Operators.length; i++) {
      if (OperatorDefinitions.Operators[i].value === this.model.Operator) {
        return OperatorDefinitions.Operators[i].requiresOperand;
      }
    }

    return true;
  }

  getFirstInputValidator() {
    return str => { return this.model.isFirstValueValid(this.editorService);};
  }

  getSecondInputValidator() {
    return str => { return this.model.isSecondValueValid(this.editorService);};
  }
}
