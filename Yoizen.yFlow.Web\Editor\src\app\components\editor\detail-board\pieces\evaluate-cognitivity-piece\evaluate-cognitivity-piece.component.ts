import { ModalService } from 'src/app/services/Tools/ModalService';
import { EditorService } from 'src/app/services/editor.service';
import { BasePieceVM } from './../BasePieceVM';
import { EvaluateCognitivityPiece } from './../../../../../models/pieces/EvaluateCognitivityPiece';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-evaluate-cognitivity-piece',
  templateUrl: './evaluate-cognitivity-piece.component.html',
  styleUrls: ['./evaluate-cognitivity-piece.component.scss']
})
export class EvaluateCognitivityPieceComponent extends BasePieceVM implements OnInit {
  model: EvaluateCognitivityPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as EvaluateCognitivityPiece;
    this.readOnly = this.readOnly || !this.editorService.isCognitivityEnabled();
  }

}
