@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;
  .controls {
    .input-group {
      .label {
        margin-right: 10px;
      }
      .info {
        margin-left: 5px;
        display: inline-block;
        vertical-align: middle;
        color: #767676;

        &:hover {
          color: #555;
        }
      }
    }
  }

  .menu-listcontainer {
    display: flex;
    flex-direction: row;
    width: 100%;
    align-content: flex-start;
    margin-top: 10px;

    .menu-entry {
      width: 30%;
      margin-left: 10px;
      margin-right: 10px;
      background-color: #fbfafa;
      border: solid 1px #e7e7e7;
      border-top: solid 2px #e7e7e7;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
