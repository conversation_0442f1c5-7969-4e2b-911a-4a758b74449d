{"version": "2.0.0", "tasks": [{"label": "Start server", "type": "npm", "script": "startNode18", "isBackground": true, "presentation": {"focus": true, "panel": "dedicated"}, "group": {"kind": "build", "isDefault": true}, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${cwd}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "Compiled |Failed to compile."}}}}, {"label": "Terminate All Tasks", "command": "echo ${input:terminate}", "type": "shell", "problemMatcher": []}], "inputs": [{"id": "terminate", "type": "command", "command": "workbench.action.tasks.terminate", "args": "terminateAll"}]}