@import "_variables";
@import "_mixins";

.configurable-item {
    @include configurable-item;
    /*min-height: 420px;*/
    .cognitivity-configuration {
        .title {
            font-family: $fontFamilyTitles;
            font-weight: bold;
            margin-right: 10px;
            justify-self: center;
            align-self: center;
            text-align: center;
            flex-grow: 0;
            flex-shrink: 0;
        }
    }
}

.overlay {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #00000044;
  z-index: 100000;

  .spinner {
    display: inline-block;
    position: absolute;
    left: calc(50% - 32px);
    top: calc(50% - 32px);
  }
}
