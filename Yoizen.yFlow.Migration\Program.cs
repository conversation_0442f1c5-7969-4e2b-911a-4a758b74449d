﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;

namespace Yoizen.yFlow.Migration
{
	class Program
	{
        static void Main(string[] args)
        {

            var builder = new ConfigurationBuilder()
                                .SetBasePath(Directory.GetCurrentDirectory())
                                .AddJsonFile("appsettings.json");

            var configuration = builder.Build();
            var json = configuration["tables"];
            dynamic jsonDynamic = JsonConvert.DeserializeObject<ExpandoObject>(json, new ExpandoObjectConverter());
            using (var db = new SqliteContext(configuration["folderSqlite"], configuration["dbSqlite"]))
            {
                Console.WriteLine("Total de tablas {0}", (jsonDynamic.tablas as List<dynamic>).Count);
                db.Database.OpenConnection();
                

                (jsonDynamic.tablas as List<dynamic>).ForEach(table =>
                {
                    string tableName = table.name;
                    int rowProcess = (int)table.split_column_total;
                    int rowReader = (int)table.split_result;
                    int rowSplit = 0;
                    int totalRow = 0;
                    bool hasExpandColumn = false;
                    Console.WriteLine("procesando la tabla {0}", tableName);
                    using (var mssql = new MssqlContext(configuration["dbMssql"]))
					{
                        mssql.Database.OpenConnection();
                        using (var cmd = mssql.Database.GetDbConnection().CreateCommand())
                        {
                            cmd.CommandText = $"TRUNCATE TABLE {tableName}";
                            cmd.ExecuteNonQuery();
                        }
                    }
                    
                    Console.WriteLine("tabla {0} truncada", tableName);

                    Console.WriteLine("Obteniendo los datos de {0}", tableName);

                    using (var cmd = db.Database.GetDbConnection().CreateCommand())
					{
                        cmd.CommandText = $"SELECT COUNT(*) FROM {tableName}";
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                totalRow = reader.GetInt32(0);
                            }
                        }
                    }

                    if (rowReader == 0)
					{
                        rowReader = totalRow;
					}

                    if (rowReader != 0)
					{
                        for (int j = 0; j <= totalRow; j += rowReader)
                        {
                            List<List<string>> sqlInsertValues = new List<List<string>>();
                            List<string> sqlInsertColumns = new List<string>();

                            using (var cmd = db.Database.GetDbConnection().CreateCommand())
                            {
                                if (table.with_identity)
                                {
                                    cmd.CommandText = $"SELECT * FROM {tableName} ORDER BY ID DESC LIMIT {j},{rowReader}";
                                }
                                else
                                {
                                    cmd.CommandText = $"SELECT * FROM {tableName}";
                                }
                                using (var reader = cmd.ExecuteReader())
                                {
                                    if (reader.HasRows)
                                    {
                                        //guardo las columnas
                                        var columns = Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();
                                        List<string> values = new List<string>();

                                        for (int index = 0; index < columns.Count; index++)
                                        {
                                            var column = columns[index];

                                            try
                                            {
                                                if (table.rename != null)
                                                {
                                                    var rename = (table.rename as List<dynamic>).Find(item => item.old == column);
                                                    if (rename != null)
                                                    {
                                                        column = rename.newName;
                                                    }
                                                }
                                            }
                                            catch { }
                                            sqlInsertColumns.Add(column);
                                        }

                                        try
                                        {
                                            sqlInsertColumns.AddRange((table.expand_columns_from_json as List<dynamic>).Select(item => item.dest_column as string).ToList());
                                            hasExpandColumn = true;
                                        }
                                        catch { }

                                        while (reader.Read())
                                        {
                                            //hardcodeo rapido
                                            if (hasExpandColumn)
                                            {
                                                List<string> normalRow = Enumerable.Range(0, columns.Count).Select((item, index) => reader.GetValue(index).ToString()).ToList();
                                                dynamic normalRowJson = JsonConvert.DeserializeObject<ExpandoObject>(normalRow[columns.Count - 1], new ExpandoObjectConverter());
                                                var normalRowDictionary = (IDictionary<string, object>)normalRowJson;
                                                if (normalRowDictionary.ContainsKey("newCase")) normalRow.Add(normalRowJson.newCase.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("transferredToYSocial")) normalRow.Add(normalRowJson.transferredToYSocial.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("closedByYFlow")) normalRow.Add(normalRowJson.closedByYFlow.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("newMessage")) normalRow.Add(normalRowJson.newMessage.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("hsmCase")) normalRow.Add(normalRowJson.hsmCase.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("caseAbandoned")) normalRow.Add(normalRowJson.caseAbandoned.ToString()); else normalRow.Add("0");
                                                if (normalRowDictionary.ContainsKey("monthlyUsers")) normalRow.Add(normalRowJson.monthlyUsers.ToString()); else normalRow.Add("0");
                                                sqlInsertValues.Add(normalRow);
                                            }
                                            else
                                            {
                                                sqlInsertValues.Add(Enumerable.Range(0, columns.Count).Select((item, index) => reader.GetValue(index).ToString()).ToList());
                                            }
                                        }

                                        rowSplit = sqlInsertValues.Count / rowProcess;
                                    }
                                }
                            }
                            Console.WriteLine("Datos obtenidos {0}", tableName);

                            Console.WriteLine("Empiezo a copiar {0}", tableName);
                            if (sqlInsertColumns.Count != 0)
                            {
                                for (int i = 0; i <= rowSplit; i++)
                                {
                                    int totalRowProcess = sqlInsertValues.Skip(i * rowProcess).Take(rowProcess).Count();
                                    if (totalRowProcess != 0)
                                    {
                                        string sql = $"INSERT INTO {tableName} ({string.Join(',', sqlInsertColumns)}) VALUES (" +
                                    $"{string.Join("),(", sqlInsertValues.Skip(i * rowProcess).Take(rowProcess).Select(list => string.Join(',', list.Select(item => MssqlContext.formatingString(item, tableName)))))})";

                                        try
                                        {
                                            if (table.with_identity)
                                            {
                                                sql = $"SET IDENTITY_INSERT[{table.name}] ON; {sql}; SET IDENTITY_INSERT[{table.name}] OFF";
                                            }
                                        }
                                        catch { }

                                        try
                                        {
                                            using (var mssql = new MssqlContext(configuration["dbMssql"]))
                                            {
                                                mssql.Database.OpenConnection();
                                                using (var cmd = mssql.Database.GetDbConnection().CreateCommand())
                                                {
                                                    cmd.CommandText = sql;
                                                    cmd.ExecuteNonQuery();
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("Error {0}", ex);
                                        }
                                        Console.WriteLine("Procesando registro {0} de {1}. El total de registro es {2}", (i + 1) * totalRowProcess + j, j + sqlInsertValues.Count, totalRow);
                                    }
                                }
                            }
                            //por las dudas lo mato
                            sqlInsertValues = null;
                        }
                    }
                });
            }
        }


        public class SqliteContext : DbContext
        {
            public string DbPath { get; }

            public SqliteContext(string folder, string db)
            {
                DbPath = System.IO.Path.Join(folder, db);
            }

            // The following configures EF to create a Sqlite database file in the
            // special "local" folder for your platform.
            protected override void OnConfiguring(DbContextOptionsBuilder options)
                => options.UseSqlite($"Data Source={DbPath}").EnableSensitiveDataLogging();

        }

        public class MssqlContext : DbContext
        {
            public string conex { get; }

            public MssqlContext(string conex)
            {
                this.conex = conex;
            }

            // The following configures EF to create a Sqlite database file in the
            // special "local" folder for your platform.
            protected override void OnConfiguring(DbContextOptionsBuilder options)
                => options.UseSqlServer(conex).EnableSensitiveDataLogging();

            //fix rapido encontrado en telecentro
            public static string formatingString(string item, string tableName)
			{
                if (tableName.Equals("history_daily_by_derivation_key", StringComparison.InvariantCultureIgnoreCase))
				{
                    return string.IsNullOrEmpty(item) ? "''" : "N'" + item.Replace("'", "''") + "'";
                }
                return string.IsNullOrEmpty(item) ? "NULL" : "N'" + item.Replace("'", "''") + "'";
            }
        }
    }
}
