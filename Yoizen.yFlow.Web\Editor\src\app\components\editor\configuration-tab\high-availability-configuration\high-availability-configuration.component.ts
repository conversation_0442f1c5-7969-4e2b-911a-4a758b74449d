import { Component, Input, OnInit } from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { getTokenPayload } from 'src/app/Utils/window';

@Component({
  selector: 'app-high-availability-configuration',
  templateUrl: './high-availability-configuration.component.html',
  styleUrls: ['./high-availability-configuration.component.scss']
})
export class HighAvailabilityConfigurationComponent implements OnInit {
  @Input() readOnly: boolean = false;
  isYoizenAdmin: boolean = false;

  constructor(private editorService: EditorService) {}

  ngOnInit(): void {
    this.isYoizenAdmin = getTokenPayload().name === 'Administrador' ||
                             getTokenPayload().name === 'administrador';
  }

  get isHighAvailabilityEnabled(): boolean {
    return this.editorService.getHighAvailability();
  }

  set isHighAvailabilityEnabled(value: boolean) {
    this.editorService.setHighAvailability(value);
  }
}
