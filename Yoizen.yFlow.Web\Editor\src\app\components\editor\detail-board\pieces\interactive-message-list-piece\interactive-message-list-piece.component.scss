@import "_variables";
@import "_mixins";

.interactive-list {
  min-width: 300px;
  background: #fff;
  position: relative;
  max-width: 850px;

  .more-buttons-than-allowed {
    display: none;
  }

  &.buttons-warning {
    .more-buttons-than-allowed {
      display: block;
    }
  }

  .max-length {
    display: none;

    .messenger, .twitter, .whatsapp, .telegram {
      display: none;
    }
  }

  .messages {
    display: flex;
    flex-direction: row;
    border-bottom: solid 1px #ebebeb;
    margin-bottom: 5px;

    app-text-list-message {
      width: 218px;
    }

    .addText {
      @include addButton;
      width: 40px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  &.whatsapp {
    .max-length {
      display: block;

      .whatsapp {
        display: block;
      }
    }

    .messages {
      border-bottom-style: none;
    }
  }

  textarea {
    width: 100%;
    background: transparent;
    white-space: pre-wrap;
    height: 33px;
    font-weight: normal;
    border-radius: 10px;
    border: 1px solid #9a9a9a;
    padding-left: 10px;
  }

  .option {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    div, .input {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .sections {
    border-top: 1px solid $gray;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin: 5px 0;
    }

    .info {
      font-style: italic;
      color: lightgrey;
    }

    .sections-container {
      display: flex;
      flex-direction: row;
      max-width: 100%;
      overflow-x: auto;
      padding-top: 15px;

      @include scrollbar;

      .section {
        display: flex;
        flex-direction: column;
        border: solid 1px darken(#ebebeb, 5%);
        padding: 10px;
        position: relative;

        & > .trash, & > .clone {
          @include trash;
          position: absolute;
          right: -13px;
          top: -13px;
          cursor: pointer;
          z-index: 2;

          &:hover {
            color: #555;
          }
        }

        & > .clone {
          right: 20px;
        }

        &:hover {
          & > .trash, & > .clone {
            @include trashOver;
          }
        }

        &:not(:first-child) {
          margin-left: 10px;
        }

        & > .option:last-child {
          margin-bottom: 0;
        }

        .section-rows {
          padding: 10px;

          .section-row {
            max-width: 300px;
            min-width: 300px;
            border: solid 1px #ebebeb;
            padding: 10px;
            display: flex;
            flex-direction: row;
            position: relative;

            .trash, .clone {
              @include trash;
              position: absolute;
              right: -13px;
              top: -13px;
              cursor: pointer;
              z-index: 2;

              &:hover {
                color: #555;
              }
            }

            .clone {
              right: 20px;
            }

            &:hover {
              .trash, .clone {
                @include trashOver;
              }
            }

            &:first-child {
              border-bottom-style: none;
            }

            .icon {
              width: 30px;
              flex-grow: 0;
              flex-shrink: 0;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: flex-start;
            }

            .data {
              flex-grow: 1;
              flex-shrink: 1;
              display: flex;
              flex-direction: column;
            }
          }

          .section-row-add {
            @include addPieceButton;
            border: solid 1px #ebebeb;
            border-top-style: none;
          }
        }
      }

      .section-add {
        margin-left: 10px;
        @include addButton;
        width: 40px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }
}

.section-drag-handle, .button-drag-handle {
  cursor: grab;
  padding: 5px;
  margin: -5px;

  &:active {
    cursor: grabbing;
  }
}

.gu-mirror {
  cursor: grabbing;
  opacity: 0.8;
}

.gu-transit {
  opacity: 0.2;
}
