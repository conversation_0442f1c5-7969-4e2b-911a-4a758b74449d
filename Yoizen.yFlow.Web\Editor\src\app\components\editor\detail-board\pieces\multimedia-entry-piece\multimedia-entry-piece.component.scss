@import '_variables';
@import '_mixins';

.multimediaEntry {
	background-color: #fff;
	padding: 10px;
	width: 830px;

  .destvariable, .destvariableparseformat, .regex, .errormessage, .retries, .next, .command {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  .validationerror {
    padding: 10px 0 10px 0;

    .messages {
      display: flex;
      flex-direction: row;

      app-text-list-error-message {
        width: 218px;
      }

      .addText {
        @include addButton;
        width: 40px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }

    .errormessage {
      .inputerrormessage {
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .retries {
      .tries {
        width: 80px;
      }
    }
  }

  .validationerror, .commands {
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;
  }

  .mime-type {
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    column-gap: 15px;

    .mime-type-available, .mime-type-enabled {
      border: 1px solid $gray;
      border-radius: 5px;
      flex: 0 1 50%;
      padding: 5px;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
        margin-bottom: 5px;
      }

      .pickers {
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: 0 15px;
      }
    }

    .mime-type-enabled {
      border-color: green;
    }
  }

  .add-condition {
    text-align: center;
    text-transform: uppercase;
    padding: 10px 0;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    font-weight: normal;
    color: $linkActionColor;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;

    &:hover {
      color: lighten($linkActionColor, 10%);
    }

    span {
      display: inline-block;
      margin-right: 10px;
      position: absolute;
      left: -10px;
      top: 13px;
      margin-left: 30px;
    }
  }

  .condition {
    position: relative;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    padding-bottom: 10px;
    padding-top: 10px;

    .info {
      padding: 5px 0;
      justify-content: flex-start;
      align-content: center;
      display: flex;

      .value {
        flex-grow: 1;
        flex-shrink: 1;
        padding-left: 10px;
        padding-right: 10px;
      }
    }

    .trash {
      @include trash;
      position: absolute;
      right: -13px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        opacity: 1;
      }
    }
  }
}
