import moment from "moment";
import { intervalDateTime } from "../../../../Yoizen.yFlow.Web/models/historical/interval";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { ISystemStatusPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/ISystemStatusPort';
import { IExternalApiPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IExternalApiPort';
import fs from "fs";
import { ILogPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/ILogPort";
import { serviceLogsFolder } from "../../../../Yoizen.yFlow.Web/helpers/folders";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

type LastDailyInterval = {
  [key in SystemStatusType.LastIntervalLogs]: SystemStatus | null;
};

export class LogsService {
  systemStatusPort: ISystemStatusPort;
  externalApiPort: IExternalApiPort;
  lastDailyInterval: LastDailyInterval;
  logPort: ILogPort;

  constructor(
    externalApiPort: IExternalApiPort,
    systemStatusPort: ISystemStatusPort,
    logPort: ILogPort
  ) {
    this.systemStatusPort = systemStatusPort;
    this.externalApiPort = externalApiPort;
    this.logPort = logPort;
    // Inicializo los intervalos
    this.lastDailyInterval = {
      [SystemStatusType.LastIntervalLogs]: null
    };

  }

  async Consolidate(lastInterval: SystemStatus, currentDateInterval: moment.Moment): Promise<SystemStatus> {
    // Verifico cual fue el ultimo intervalo en consolidarse, si falto uno o mas, voy consolidando hasta 
    while (lastInterval.Date.valueOf() <= currentDateInterval.valueOf()) {
      try {
        logger.info(`Voy a consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type}`);
        const initInterval = lastInterval.Date.clone().add(-30, 'minutes');
        const endInterval = lastInterval.Date.clone().add(-30, 'minutes');
        endInterval.add(30, 'minutes');

        const results = await this.logPort.GetLogsByDateRange(initInterval.toDate(), endInterval.toDate());

        let folder = `${initInterval.format("YYYYMMDDHHmm")} - stderr`;
        let filePath = `${serviceLogsFolder}/${folder}.txt`;

        if (results.length > 0) {

          if (!fs.existsSync(filePath)) {
            fs.mkdirSync(serviceLogsFolder, { recursive: true });
            fs.writeFileSync(filePath, ''); // Crear el archivo si no existe
          }

          let logEntries: string[] = [];
          results.forEach(row => {
            let logEntry = `[${moment(row.date).format('YYYY-MM-DD HH:mm:ss')}] - [${row.messageId}] - [${row.message}]\n`; 
            if (row.stack) {
              logEntry += `StackTrace: ${row.stack}\n`;
            }
            logEntries.push(logEntry);
          });

          const logContent = logEntries.join('');
          fs.writeFileSync(filePath, logContent);
        }

        await this.systemStatusPort.Save(lastInterval);

      } catch (error) {
        logger.error(`Ocurrió un error al intentar consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type} Error: ${error}`);
      }

      lastInterval.Date = lastInterval.Date.add(30, 'minute');
    }

    return lastInterval;
  }

  async Process() {
    const now = moment();
    const currentDateInterval = intervalDateTime(now).add(-30, "minutes");

    for (let key in this.lastDailyInterval) {
      let value: SystemStatus = this.lastDailyInterval[key];
      logger.info(`voy a procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
      await this.Consolidate(value, currentDateInterval);
      logger.info(`terminé de procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
    }

    setTimeout(this.Process.bind(this), 60000 * 5);
  }

  async Init() {
    const now = moment();
    for (let key in this.lastDailyInterval) {
      await this.GetIntervalSystemStatus(now, key as SystemStatusType);
    }
  }

  private async GetIntervalSystemStatus(now: moment.Moment, systemStatusType: SystemStatusType) {
    // Obtengo la última fecha del intervalo ejecutado correctamente si no la tengo.
    if (!this.lastDailyInterval[systemStatusType]) {
      let status = await this.systemStatusPort.Get(systemStatusType);
      if (!status) {
        status = new SystemStatus();
        status.type = systemStatusType;
        let date = moment(now);
        date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        status.Date = date;
        logger.info(`No tengo registros en la bd, creo hoy el último intervalo ${this.lastDailyInterval[systemStatusType]} del tipo dailyByFlow`);
        this.lastDailyInterval[systemStatusType] = status;

        status = await this.systemStatusPort.Save(status);
      }

      logger.info(`Ultimo intervalo obtenido ${JSON.stringify(status)} del tipo ${systemStatusType}`);
      status.Date = status.Date.add(30, 'minute');
      this.lastDailyInterval[systemStatusType] = status;
    }
  }
}