import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { OperatorDefinitions } from '../../../../../models/OperatorType';
import { ShortenUrlPiece } from '../../../../../models/pieces/ShortenUrlPiece';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import {TypeDefinition} from "../../../../../models/TypeDefinition";

@Component({
  selector: 'app-shorten-url-piece',
  templateUrl: './shorten-url-piece.component.html',
  styleUrls: ['./shorten-url-piece.component.scss']
})
export class ShortenUrlComponent extends BasePieceVM implements OnInit {
  variableFilter: TypeDefinition[] = [ TypeDefinition.Text];
  model : ShortenUrlPiece;

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId( this.model.VariableId);
  }
  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

   ngOnInit() {
    this.model = this.context as ShortenUrlPiece;
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  setVariableOnOutput(variable : VariableDefinition) {
    if( variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = null;
    }
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }
}

