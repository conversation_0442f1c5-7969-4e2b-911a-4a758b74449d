import { IFlowPort } from '../../ports/IFlowPort';
import { CaseContext } from '../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/CaseContext';
import { logger } from '../../../../Yoizen.yFlow.Helpers/src/Logger';

export class FlowMock implements IFlowPort {
    constructor() {
        logger.info(`No se utiliza la base CaseContext`);
    }

    async GetAllProductiveMaster() {
        throw new Error('Method not implemented.')
    }

    async connect(): Promise<void> {
        return;
    }

    private getKey(caseId: string): string {
        throw new Error('Method not implemented.')
    }

    async SaveContext(caseContext: CaseContext): Promise<CaseContext> {
        throw new Error('Method not implemented.')
    }

    async UpdateContext(caseContext: CaseContext): Promise<CaseContext> {
        throw new Error('Method not implemented.')
    }

    async GetContextByCaseId(caseId: string | number): Promise<CaseContext> {
        throw new Error('Method not implemented.')
    }
}