import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { CloseCasePiece } from "../../../../../models/pieces/CloseCasePiece";
import {MarkAsPendingReplyFromCustomerTypes} from "../../../../../models/pieces/DataEntry";
import {BlockDefinition} from "../../../../../models/BlockDefinition";
import {ChannelTypes} from "../../../../../models/ChannelType";
import { FlowTypes } from 'src/app/models/FlowType';
import { FlowDefinition } from 'src/app/models/FlowDefinition';

@Component({
  selector: 'app-close-case-piece',
  templateUrl: './close-case-piece.component.html',
  styleUrls: ['./close-case-piece.component.scss']
})
export class CloseCasePieceComponent extends BasePieceVM implements OnInit {
  model: CloseCasePiece;
  channelTypes = ChannelTypes;
  flowTypes = FlowTypes;
  markAsPendingReplyFromCustomerTypes = MarkAsPendingReplyFromCustomerTypes;
  flow: FlowDefinition;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.flow = this.editorService.getCurrentFlow();
    this.model = this.context as CloseCasePiece;
  }

  onSelectPendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.model.pendingReplyFromCustomerBlockId = blockData.Id;
  }

  onDeletePendingReplyFromCustomerBlock(blockData : BlockDefinition) {
    this.model.pendingReplyFromCustomerBlockId = "-1";
  }

  isCustomMessageForPendingReplyValid() {
    return str => { return this.model.isCustomMessageForPendingReplyValid(this.editorService) };
  }
}
