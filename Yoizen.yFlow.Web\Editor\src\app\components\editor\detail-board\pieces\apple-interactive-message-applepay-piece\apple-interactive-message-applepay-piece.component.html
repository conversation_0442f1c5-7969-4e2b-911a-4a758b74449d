<div class="apple-pay card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fab fa-apple-pay"></span> {{ 'PIECE_APPLE_INTERATIVE_MESSAGE_APPLE_PAY' | translate }}
  </div>
  <div *ngIf="applePayMerchantsConfig === null || applePayMerchantsConfig.length === 0">
    <div class="alert alert-danger">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_MERCHANT_EMPTY' | translate}}
    </div>
  </div>
  <div *ngIf="applePayMerchantsConfig !== null && applePayMerchantsConfig.length > 0">
    <div class="merchant">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_MERCHANT_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_MERCHANT_INFO' | translate }}</div>
      <div class="options">
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_MERCHANT' | translate}}:</span>
          <select class="select" [(ngModel)]="model.merchantId" [disabled]="readOnly">
            <option *ngFor="let merchantConfig of applePayMerchantsConfig" [ngValue]="merchantConfig.merchantId">{{ merchantConfig.merchantId }} - {{ merchantConfig.merchantName }}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="endpoints">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_INFO' | translate }}</div>
      <div class="options">
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_PAYMENTGATEWAYURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.paymentGatewayUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isPaymentGatewayUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_PAYMENTGATEWAYURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_FALLBACKURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.fallbackUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isFallbackUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_FALLBACKURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_ORDERTRACKINGURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.orderTrackingUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isOrderTrackingUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_ORDERTRACKINGURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_PAYMENTMETHODUPDATEURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.paymentMethodUpdateUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isPaymentMethodUpdateUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_PAYMENTMETHODUPDATEURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_SHIPPINGCONTACTUPDATEURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.shippingContactUpdateUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isShippingContactUpdateUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_SHIPPINGCONTACTUPDATEURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_SHIPPINGMETHODUPDATEURL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'INPUT_URL' | translate"
            [(value)]="model.endpoints.shippingMethodUpdateUrl"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.endpoints.isShippingMethodUpdateUrlValid.bind(model.endpoints)"
            [disabled]="readOnly">
          </app-input-with-variables>
          <div class="info" ngbTooltip="{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ENDPOINTS_SHIPPINGMETHODUPDATEURL_TIP' | translate }}"
               placement="right" container="body" tooltipClass="tooltip-persistent">
            <span class="fa fa-question-circle"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="applepay">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_INFO' | translate }}</div>
      <div class="options">
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_MERCHANTCAPABILITIES' | translate}}:</span>
          <div class="items"
               [ngClass]="{'invalid': !model.applepay.isMerchantCapabilitiesValid()}">
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isMerchantCapabilitySelected(merchantCapabilities.supports3DS)"
                         [disabled]="true"
                         switchColor="#ffffff" (valueChange)="refreshMerchantCapabilities($event, merchantCapabilities.supports3DS)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_MERCHANTCAPABILITIES_3DS' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isMerchantCapabilitySelected(merchantCapabilities.supportsDebit)"
                         [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshMerchantCapabilities($event, merchantCapabilities.supportsDebit)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_MERCHANTCAPABILITIES_DEBIT' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isMerchantCapabilitySelected(merchantCapabilities.supportsCredit)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshMerchantCapabilities($event, merchantCapabilities.supportsCredit)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_MERCHANTCAPABILITIES_CREDIT' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isMerchantCapabilitySelected(merchantCapabilities.supportsEMV)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshMerchantCapabilities($event, merchantCapabilities.supportsEMV)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_MERCHANTCAPABILITIES_EMV' | translate }}</span>
            </div>
          </div>
        </div>
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS' | translate}}:</span>
          <div class="items"
               [ngClass]="{'invalid': !model.applepay.isSupportedNetworksValid()}">
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.amex)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.amex)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_AMEX' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.discover)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.discover)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_DISCOVER' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.jcb)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.jcb)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_JCB' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.masterCard)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.masterCard)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_MASTERCARD' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.privateLabel)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.privateLabel)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_PRIVATELABEL' | translate }}</span>
            </div>
            <div class="item">
              <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedNetworkSelected(applePaySupportedNetworks.visa)"
                         [disabled]="readOnly"
                         switchColor="#ffffff" (valueChange)="refreshSupportedNetwork($event, applePaySupportedNetworks.visa)"></ui-switch>
              <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_APPLEPAY_SUPPORTEDNETWORKS_VISA' | translate }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="options">
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_COUNTRYCODE' | translate}}:</span>
        <select class="select" [(ngModel)]="model.countryCode" [disabled]="readOnly">
          <option *ngFor="let country of countryCodesIso" [ngValue]="country.code">{{ country.name }}</option>
        </select>
      </div>
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CURRENCYCODE' | translate}}:</span>
        <select class="select" [(ngModel)]="model.currencyCode" [disabled]="readOnly">
          <option *ngFor="let currency of currencies" [ngValue]="currency.code">{{ currency.name }}</option>
        </select>
      </div>
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_REQUIREDBILLINGCONTACTFIELDS' | translate}}:</span>
        <div class="items"
             [ngClass]="{'invalid': !model.isRequiredBillingContactFieldsValid()}">
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredBillingContactFields, applePayContactField.name)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredBillingContactFields, applePayContactField.name)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_NAME' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredBillingContactFields, applePayContactField.phone)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredBillingContactFields, applePayContactField.phone)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_PHONE' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredBillingContactFields, applePayContactField.email)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredBillingContactFields, applePayContactField.email)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_EMAIL' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredBillingContactFields, applePayContactField.phoneticName)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredBillingContactFields, applePayContactField.phoneticName)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_PHONETICNAME' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredBillingContactFields, applePayContactField.postalAddress)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredBillingContactFields, applePayContactField.postalAddress)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_POSTALADDRESS' | translate }}</span>
          </div>
        </div>
      </div>
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_REQUIREDSHIPPINGCONTACTFIELDS' | translate}}:</span>
        <div class="items"
             [ngClass]="{'invalid': !model.isRequiredShippingContactFieldsValid()}">
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredShippingContactFields, applePayContactField.name)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredShippingContactFields, applePayContactField.name)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_NAME' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredShippingContactFields, applePayContactField.phone)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredShippingContactFields, applePayContactField.phone)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_PHONE' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredShippingContactFields, applePayContactField.email)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredShippingContactFields, applePayContactField.email)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_EMAIL' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredShippingContactFields, applePayContactField.phoneticName)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredShippingContactFields, applePayContactField.phoneticName)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_PHONETICNAME' | translate }}</span>
          </div>
          <div class="item">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isContactFieldSelected(model.requiredShippingContactFields, applePayContactField.postalAddress)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshContactFields($event, model.requiredShippingContactFields, applePayContactField.postalAddress)"></ui-switch>
            <span class="item-text">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_CONTACTFIELDS_POSTALADDRESS' | translate }}</span>
          </div>
        </div>
      </div>
      <div class="option">
        <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SUPPORTEDCOUNTRIES' | translate}}:</span>
        <div class="items maxheight"
             [ngClass]="{'invalid': !model.isSupportedCountriesValid()}">
          <div class="item" *ngFor="let country of countryCodesIso">
            <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0" [checked]="isSupportedCountrySelected(country.code)"
                       [disabled]="readOnly"
                       switchColor="#ffffff" (valueChange)="refreshSupportedCountries($event, country.code)"></ui-switch>
            <span class="item-text">{{ country.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="shippingmethods">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_INFO' | translate }}</div>
      <div class="items"
        [ngClass]="{ 'empty': model.shippingMethods.length === null || model.shippingMethods.length === 0 }">
        <div class="item" *ngFor="let shippingMethod of model.shippingMethods let j = index">
          <div class="trash" (click)="deleteShippingMethod(j)" *ngIf="!readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
               tooltipClass="tooltip-trash">
            <span class="fa fa-trash-alt"></span>
          </div>
          <div class="data">
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_LABEL' | translate}}:</span>
              <app-input-with-variables
                [(value)]="shippingMethod.label"
                [validator]="shippingMethod.isLabelValid.bind(shippingMethod)"
                [wideInput]="true"
                [isTextArea]="false"
                [disabled]="readOnly"
                class="input">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_DETAIL' | translate}}:</span>
              <app-input-with-variables
                [(value)]="shippingMethod.detail"
                [validator]="shippingMethod.isDetailValid.bind(shippingMethod)"
                [wideInput]="true"
                [isTextArea]="false"
                [disabled]="readOnly"
                class="input">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_AMOUNT' | translate}}:</span>
              <app-input-with-variables
                [(value)]="shippingMethod.amount"
                [validator]="shippingMethod.isAmountValid.bind(shippingMethod)"
                [wideInput]="true"
                [isTextArea]="false"
                [disabled]="readOnly"
                class="input">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_IDENTIFIER' | translate}}:</span>
              <app-input-with-variables
                [(value)]="shippingMethod.identifier"
                [validator]="shippingMethod.isIdentifierValid.bind(shippingMethod)"
                [wideInput]="true"
                [isTextArea]="false"
                [disabled]="readOnly"
                class="input">
              </app-input-with-variables>
            </div>
          </div>
        </div>
        <div class="items-add" (click)="addNewShippingMethod(); $event.stopPropagation();" *ngIf="!readOnly">
          <span class="fa fa-plus"></span> {{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_SHIPPINGMETHODS_ADD' | translate }}
        </div>
      </div>
    </div>
    <div class="lineitems">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_LINEITEMS_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_LINEITEMS_INFO' | translate }}</div>
      <div class="items"
           [ngClass]="{ 'empty': model.lineItems.length === null || model.lineItems.length === 0 }">
        <div class="item" *ngFor="let lineItem of model.lineItems let j = index">
          <div class="trash" (click)="deleteLineItem(j)" *ngIf="!readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
               tooltipClass="tooltip-trash">
            <span class="fa fa-trash-alt"></span>
          </div>
          <div class="data">
            <div class="option with-info">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_TYPE' | translate}}:</span>
              <select class="select" [(ngModel)]="lineItem.type" [disabled]="readOnly">
                <option [ngValue]="applePayLineItemType.final">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ITEMTYPE_FINAL' | translate }}</option>
                <option [ngValue]="applePayLineItemType.pending">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ITEMTYPE_PENDING' | translate }}</option>
              </select>
            </div>
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_LABEL' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_LABEL' | translate"
                [(value)]="lineItem.label"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="lineItem.isLabelValid.bind(lineItem)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_AMOUNT' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_AMOUNT' | translate"
                [(value)]="lineItem.amount"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="lineItem.isAmountValid.bind(lineItem)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
          </div>
        </div>
        <div class="items-add" (click)="addNewLineItem(); $event.stopPropagation();" *ngIf="!readOnly">
          <span class="fa fa-plus"></span> {{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_LINEITEMS_ADD' | translate }}
        </div>
      </div>
    </div>
    <div class="total">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_INFO' | translate }}</div>
      <div class="options">
        <div class="option with-info">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_TYPE' | translate}}:</span>
          <select class="select" [(ngModel)]="model.total.type" [disabled]="readOnly">
            <option [ngValue]="applePayLineItemType.final">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ITEMTYPE_FINAL' | translate }}</option>
            <option [ngValue]="applePayLineItemType.pending">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_ITEMTYPE_PENDING' | translate }}</option>
          </select>
        </div>
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_LABEL' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_LABEL' | translate"
            [(value)]="model.total.label"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.total.isLabelValid.bind(model.total)"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_AMOUNT' | translate}}:</span>
          <app-input-with-variables
            [placeholder]="'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_TOTAL_AMOUNT' | translate"
            [(value)]="model.total.amount"
            [isTextArea]="false"
            [wideInput]="true"
            [validator]="model.total.isAmountValid.bind(model.total)"
            [disabled]="readOnly">
          </app-input-with-variables>
        </div>
      </div>
    </div>
    <app-apple-interactive-received-and-reply
      [receivedMessage]="model.receivedMessage"
      [replyMessage]="model.replyMessage"
      [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
    <div class="navigation">
      <div class="title">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_BLOCKS_TITLE' | translate }}</div>
      <div class="info">{{ 'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_BLOCKS_INFO' | translate }}</div>
      <div class="options">
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_BLOCKS_BLOCK_SUCCESS' | translate}}:</span>
          <app-block-picker class="input"
                            [blockId]="model.paymentSuccessBlockId"
                            (onSelectNewBlock)="selectPaymentSuccessBlock($event)"
                            (onDeleteBlock)="deletePaymentSuccessBlock()"
                            [readOnly]="readOnly"
                            [isInvalid]="!model.isPaymentSuccessBlockValid(editorService)"></app-block-picker>
        </div>
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_BLOCKS_BLOCK_FAILED' | translate}}:</span>
          <app-block-picker class="input"
                            [blockId]="model.paymentErrorBlockId"
                            (onSelectNewBlock)="selectPaymentErrorBlock($event)"
                            (onDeleteBlock)="deletePaymentErrorBlock()"
                            [readOnly]="readOnly"
                            [isInvalid]="!model.isPaymentErrorBlockValid(editorService)"></app-block-picker>
        </div>
        <div class="option">
          <span class="title">{{'APPLE_INTERATIVE_MESSAGE_APPLE_PAY_BLOCKS_BLOCK_SESSIONFAILED' | translate}}:</span>
          <app-block-picker class="input"
                          [blockId]="model.errorBlockId"
                          (onSelectNewBlock)="selectBlock($event)"
                          (onDeleteBlock)="deleteBlock()"
                          [readOnly]="readOnly"
                          [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
        </div>
      </div>
    </div>
  </div>
</div>
