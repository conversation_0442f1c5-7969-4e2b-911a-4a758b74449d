import {Component, OnInit, Input, OnDestroy} from '@angular/core';
import { MessagePieceType, Text } from '../../../../../models/pieces/MessagePieceType';
import {ButtonPiece, ButtonType} from '../../../../../models/pieces/ButtonPiece';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { ButtonElementComponent } from '../message-piece/button-element/button-element.component'
import {ChannelTypes} from "../../../../../models/ChannelType";
import { BasePiece } from 'src/app/models/pieces/BasePiece';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';
import {PieceType} from "../../../../../models/PieceType";
import {YSocialSettings} from "../../../../../models/YSocialSettings";
import {
  InteractiveMessageListPiece,
  InteractiveMessageListSection, InteractiveMessageListSectionRow
} from "../../../../../models/pieces/InteractiveMessageListPiece";
import {BlockDefinition} from "../../../../../models/BlockDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {
  InteractiveMessageProductListPiece,
  InteractiveMessageProductListSection
} from "../../../../../models/pieces/InteractiveMessageProductListPiece";
import {FacebookCatalog, FacebookCatalogProduct, WhatsappCatalog} from "../../../../../models/WhatsappCatalog";
import {InteractiveMessageButtonsHeaderTypes} from "../../../../../models/pieces/InteractiveMessageButtonsPiece";
import {SourceTypes} from "../../../../../models/pieces/AttachmentPiece";
import {isUrlValid, isStringValid} from "../../../../../urlutils.module";
import {OutputVariableMap} from "../../../../../models/pieces/IntegrationPiece";
import {environment} from "../../../../../../environments/environment";


@Component({
  selector: 'app-interactive-message-productlist-piece',
  templateUrl: './interactive-message-productlist-piece.component.html',
  styleUrls: ['./interactive-message-productlist-piece.component.scss']
})
export class InteractiveMessageProductlistPieceComponent extends BasePieceVM implements OnInit {
  constructor( editorService : EditorService, public modalService : ModalService, private singleOverlay: SingleOverlayService, private dragulaService : DragulaService ) {
    super(editorService, modalService);
  }

  ready: boolean = false;
  model: InteractiveMessageProductListPiece;
  catalogsInfo: WhatsappCatalog[];
  HeaderTypes = InteractiveMessageButtonsHeaderTypes;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];
  ActiveIdString: string;
  showIsPublicToggle: boolean = true;

  get variableDefinition(): VariableDefinition {
    return this.editorService.findVariableWithId(this.model.Header.FileDataStorageId);
  }

  ngOnInit() {
    this.model = this.context as InteractiveMessageProductListPiece;
    this.flow = this.editorService.getCurrentFlow();

    this.catalogsInfo = this.editorService.getWhatsappCatalogs();
    if (this.catalogsInfo !== null) {
      this.ready = true;
    }
    else {
      this.editorService.onWhatsappCatalogsLoaded.subscribe(() => {
        this.catalogsInfo = this.editorService.getWhatsappCatalogs();
        this.ready = true;
      });
    }
    
    // @ts-ignore
    if (typeof(environment.onPremise) !== 'boolean' ||
    // @ts-ignore
    !environment.onPremise) {
    if (this.model.IsPublicUrl) {
      this.showIsPublicToggle = false;
    }
  } 
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Header.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  addNewText() {
  	this.model.TextList.push(new Text());
  }

  getCatalogProducts(): FacebookCatalogProduct[] {
    if (this.model.CatalogId !== null && this.model.CatalogId.length > 0) {
      let whatsappCatalog: WhatsappCatalog = this.catalogsInfo.find(c => c.Catalog.Id === this.model.CatalogId);
      if (typeof(whatsappCatalog) !== 'undefined' && whatsappCatalog !== null) {
        return whatsappCatalog.Catalog.Products;
      }
    }

    return null;
  }

  getCatalogProduct(productId: string): FacebookCatalogProduct {
    let products = this.getCatalogProducts();
    if (products !== null) {
      return products.find(p => p.RetailerId === productId);
    }

    return null;
  }

  canAddTextOptions() : boolean {
  	var value = this.model.TextList.length < 3;
  	return value;
  }

  isTextValid(index: number) {
    let text = this.model.TextList[index];
    if (typeof(text.text) === 'undefined') {
      return false;
    }

    return str => { return this.model.isTextValid(text.text, 1024, true, this.editorService); };
  }

  isSectionTitleValid(section: InteractiveMessageProductListSection) {
    return str => { return section.isTitleValid(this.editorService); };
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }

  canAddSection() : boolean {
    return this.model.Sections.length < 10;
  }

  addNewSection() {
    this.model.Sections.push(new InteractiveMessageProductListSection());
  }

  addNewSectionRow(section: InteractiveMessageProductListSection) {
    section.Items.push("");
  }

  canAddSectionRow(section: InteractiveMessageProductListSection) : boolean {
    return true;
  }

  deleteSection(index: number) {
    this.model.Sections.splice(index, 1);
  }

  deleteRow(section: InteractiveMessageProductListSection, index: number) {
    section.Items.splice(index, 1);
  }

  isHeaderTextValid() {
    return str => { return this.model.Header.isTextValid(this.editorService) }
  }

  isFooterTextValid() {
    return str => { return this.model.isFooterTextValid(this.editorService) }
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  setVariableOnOutput(output: OutputVariableMap, variable: VariableDefinition) {
    if (variable != null) {
      this.model.Header.FileDataStorageId = variable.Id;
    }
    else {
      this.model.Header.FileDataStorageId = null;
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case "tab-url":
        this.model.Header.Source = SourceTypes.Url;
        return;
      case "tab-variable":
        this.model.Header.Source = SourceTypes.Variable;
        return
    }
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.Header.isFileDataValid(this.editorService);
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  isNameValid(str) {
    return str => { return this.model.Header.isNameValid(this.editorService); };
  }
}
