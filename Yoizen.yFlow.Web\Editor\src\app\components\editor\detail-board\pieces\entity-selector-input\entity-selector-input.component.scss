@import '_variables.scss';
@import "_mixins";

.app-entity {
  width: 100%;
  vertical-align: top;
  position: relative;
  display: flex;

  select {
    height: 30px;
    font-weight: normal;
    width: 10%;
  }

  .next {
    width: 100%;

    input {
      width: 100%;
    }
  }

  .invalid-state {
    border-color: $error-color;
  }

  .label {
    margin-right: 10px;
    margin-top: auto;
    margin-bottom: auto;
  }

  .entity {
    padding: 0px;
    max-width: 100%;
  }

  .entity-no-overflow {
    overflow: hidden;
  }

  .addName input,
  .entity input {
    width: 100%;
  }

  .entity-selected {
    width: fit-content;
    align-content: center;
    justify-content: center;
    white-space: nowrap;
    max-width: 100%;
    position: relative;
    display: inline-block;
    vertical-align: top;
    height: 28px;
    border-radius: 7px;
    background-color: #e0e0e0;
    border: solid 1px rgba(0, 0, 0, 0.21);
    padding: 0;
    margin: 0 8px 0 0;
    user-select: none !important;
    cursor: pointer;

    .entity-display {
      padding: 2px 10px;
      flex-wrap: nowrap;
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .trash {
      @include trashSmall;
      z-index: auto !important;
      right: -10px;
      top: -10px;
      position: absolute;

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        @include trashOver;
      }
    }
  }

  .selector-container {
    position: absolute;
    overflow: hidden;
    border-radius: 5px;
    background: white;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.13);
    display: flex;
    flex-direction: column;
    width: 100%;
    height: fit-content;
    padding: 5px;
    z-index: 100;

    .scroll-area {
      max-height: 100px;
      overflow-y: scroll;
      text-align: left;

      .entity-container {
        width: 100%;
        height: fit-content;

        .entity-name {
            @include variable-name;
            margin-bottom: 3px;
            padding: 3px;
            border: 1px solid transparent;
        }

        .empty-entity-set {
          font-weight: 600;
          margin-bottom: 3px;
          padding: 3px;
          border: 1px solid transparent;

          &:hover {
            background: $block-selector-hover-color;
            border: 1px solid darken($block-selector-hover-color, 5%);
          }
        }

        .entity-name {
          cursor: pointer;
        }
      }
    }
  }
}
