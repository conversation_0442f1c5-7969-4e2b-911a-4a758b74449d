<div class="biometric-pad card"
     [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-camera icon"></span> {{ 'CARD_BIOMETRIC_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_BIOMETRIC_INFO' | translate"></span>
  </div>

  <p>Expiracion del token: {{model.TokenExpiration}} minutos</p>
  <input class="slider" type="range" min="1" max="1440" [(ngModel)]="model.TokenExpiration" [disabled]="readOnly" />

  <p>Expiracion del token: {{model.VerificationExpiration}} minutos</p>
  <input class="slider" type="range" min="1" max="1440" [(ngModel)]="model.VerificationExpiration" [disabled]="readOnly" />

  <div class="biometric-list">
    <div class="next biometric">
      <div [ngClass]="{'hide': hasMetamap()}" *ngIf="!readOnly">
        <input class="input" [ngClass]="{'invalid-state': !isValid()}" type="text"
               autocomplete="nomecompletesnadaaca"
               placeholder="{{'METAMAP' | translate}}" [(ngModel)]="searchMetamapString" LimitLength
               (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
        <div #metamapPicker class="hide biometric-selection-anchor">
          <div class="selector-container">
            <div class="scroll-area">
              <div class="biometric-container" *ngFor="let metamap of Metamaps">
                <div class="biometric-name" (click)="selectMetamap(metamap); $event.stopPropagation();">{{metamap.name}}</div>
              </div>
              <div class="biometric-container" *ngIf="EmptyMetamapSet">
                <div class="empty-biometric-set">{{'EMPTY_BIOMETRIC_SET' | translate}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="biometric-selected" [ngClass]="{'hide': !hasMetamap()}">
        <div class="biometric-display">{{model.Metamap?.name}}</div>
        <div class="fa fa-unlink trash" (click)="deleteMetamap()" *ngIf="!readOnly"
             data-toggle="tooltip" ngbTooltip="{{ 'BIOMETRIC_REMOVESELECTED' | translate }}"
             placement="top" container="body" tooltipClass="tooltip-trash"></div>
      </div>
    </div>
  </div>

  <div class="name" *ngIf="model.Metamap != null">
    <div class="card-info">
      <span [innerHTML]="'CARD_BIOMETRIC_METAMAP_INFO' | translate"></span>
      <ol>
        <li *ngFor="let merit of model.Metamap.metaMerits">{{ merit.type | translate}}</li>
      </ol>
    </div>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'BIOMETRIC_BLOCK_SUCCESS' | translate }}</div>
    <div [ngClass]="{'hide': SuccessBlockData !== null}" *ngIf="!readOnly">
      <input class="input" 
              type="text" 
              placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
              [ngClass]="{'invalid-piece': SuccessBlockData === null}"
              [(ngModel)]="searchSuccessBlockString" LimitLength (focusin)="onSuccessInputFocusIn()" (focusout)="onSuccessInputFocusOut()">
      <div #successBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchSuccessBlockString
                            (onSelectBlock)="onSuccessBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': SuccessBlockData === null}">
        <span class="block-display">{{SuccessBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteSuccessBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'BIOMETRIC_BLOCK_REVIEWNEEDED' | translate }}</div>
    <div [ngClass]="{'hide': ReviewNeededBlockData !== null}" *ngIf="!readOnly">
      <input class="input" [ngClass]="{'invalid-piece': ReviewNeededBlockData === null}" type="text" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchReviewNeededBlockString" LimitLength (focusin)="onReviewNeededInputFocusIn()" (focusout)="onReviewNeededInputFocusOut()">
      <div #reviewNeededBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchReviewNeededBlockString
                            (onSelectBlock)="onReviewNeededBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': ReviewNeededBlockData === null}">
        <span class="block-display">{{ReviewNeededBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteReviewNeededBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'BIOMETRIC_BLOCK_REJECTED' | translate }}</div>
    <div [ngClass]="{'hide': RejectedBlockData !== null}" *ngIf="!readOnly">
      <input class="input" type="text" [ngClass]="{'invalid-piece': RejectedBlockData === null}" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchRejectedBlockString" LimitLength (focusin)="onRejectedInputFocusIn()" (focusout)="onRejectedInputFocusOut()">
      <div #rejectedBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchRejectedBlockString
                            (onSelectBlock)="onRejectedBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': RejectedBlockData === null}">
        <span class="block-display">{{RejectedBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteRejectedBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'BIOMETRIC_BLOCK_STARTED' | translate }}</div>
    <div [ngClass]="{'hide': StartedBlockData !== null}" *ngIf="!readOnly">
      <input class="input" type="text" [ngClass]="{'invalid-piece': StartedBlockData === null}" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchStartedBlockString" LimitLength (focusin)="onStartedInputFocusIn()" (focusout)="onStartedInputFocusOut()">
      <div #startedBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchStartedBlockString
                            (onSelectBlock)="onStartedBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': StartedBlockData === null}">
        <span class="block-display">{{StartedBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteStartedBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'BIOMETRIC_BLOCK_OPENED_PROCESS' | translate }}</div>
    <div [ngClass]="{'hide': OpenedProcessBlockData !== null}" *ngIf="!readOnly">
      <input class="input" type="text" [ngClass]="{'invalid-piece': OpenedProcessBlockData === null}" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchOpenedProcessBlockString" LimitLength (focusin)="onOpenedProcessInputFocusIn()" (focusout)="onOpenedProcessInputFocusOut()">
      <div #openedProcessBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchOpenedProcessBlockString
                            (onSelectBlock)="onOpenedProcessBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': OpenedProcessBlockData === null}">
        <span class="block-display">{{OpenedProcessBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteOpenedProcessBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="name">
    <span class="title">{{ 'BIOMETRIC_ICO_URL' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'BIOMETRIC_ICO_URL' | translate"
      [(value)]="model.IcoUrl"
      [validator]="model.isIcoUrlValid.bind(model)"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  
  <div class="name">
    <span class="title">{{ 'BIOMETRIC_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'BIOMETRIC_TITLE' | translate"
      [(value)]="model.BiometricTitle"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'BIOMETRIC_PAGE_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'BIOMETRIC_PAGE_TITLE' | translate"
      [(value)]="model.PageTitle"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'BIOMETRIC_COMPANY_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'BIOMETRIC_COMPANY_TITLE' | translate"
      [(value)]="model.Company"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>
</div>
