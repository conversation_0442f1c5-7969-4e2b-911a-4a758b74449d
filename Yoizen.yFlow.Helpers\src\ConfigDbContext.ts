import { DbConfigBase } from "./ConfigDbBase";
import { isEnvironmentVariableValid, parseToInt, parseToBoolean } from "./Helpers";
import { logger } from "./Logger";

export class DbContext extends DbConfigBase {
}

export const dbContext = new DbContext();


const validateAndFormatConfig = () => {
    dbContext.dialect = process.env.dbcontextdialect;
    dbContext.connectionString = process.env.dbcontextConnectionString;
    dbContext.ssl = parseToBoolean(process.env.dbcontextssl, false);
    dbContext.enable = true;
    dbContext.poolMaxSize = parseToInt(process.env.dbcontextMaxPoolSize, 5);

    if (!isEnvironmentVariableValid(dbContext.dialect)) {
        logger.error(`Faltan datos de conexión al Sql de dbContext - dialect`);
        process.exit(9);
    }
    dbContext.dialect = dbContext.dialect.toLowerCase();

    if (dbContext.dialect !== 'disabled') {
        if (dbContext.dialect !== 'redis') {
            dbContext.host = process.env.dbcontexthost;
            dbContext.port = parseToInt(process.env.dbcontextport);
            dbContext.name = process.env.dbcontextname;
            dbContext.username = process.env.dbcontextusername;
            dbContext.password = process.env.dbcontextpassword;
            dbContext.dbtimeout = parseToInt(process.env.dbcontextdbtimeout, 30000);
            dbContext.dbcanceltimeout = parseToInt(process.env.dbcontextdbcanceltimeout, 5000);
            dbContext.parseConnectionString();

            if (!(isEnvironmentVariableValid(dbContext.host) &&
                isEnvironmentVariableValid(dbContext.port) &&
                isEnvironmentVariableValid(dbContext.name) &&
                isEnvironmentVariableValid(dbContext.username) &&
                isEnvironmentVariableValid(dbContext.password) &&
                isEnvironmentVariableValid(dbContext.dialect))) {
                logger.error(`Faltan datos de conexión al Sql de dbContext`);
                process.exit(9);
            }
        } else {
            dbContext.parseConnectionString();
        }
    }
    else {
        dbContext.enable = false;
    }
}

validateAndFormatConfig();