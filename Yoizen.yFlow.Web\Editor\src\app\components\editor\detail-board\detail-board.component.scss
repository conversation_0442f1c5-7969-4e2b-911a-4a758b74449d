@import '_variables';
@import "_mixins";

.contents {
  padding: 20px 10px;

  & > .title {
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;

    & > .status {
      color: #555;
    }

    & > .status:hover {
      color: #222;
      cursor: pointer;
    }

    span.title {
      padding: 0 26px 0 4px;
      line-height: 36px;
      font-size: 20px;
      flex-grow: 1;
    }

    .display-conections, .display-stats {
      @include actionButton();
      white-space: nowrap;
    }

    .display-stats {
      margin-right: 5px;
    }

    input {
      border: solid 1px transparent;
      border-radius: 10px;
      background-color: transparent;
      padding: 0 26px 0 4px;
      line-height: 36px;
      font-size: 20px;
      width: 90%;

      &:hover {
        border: solid 1px rgba(0, 0, 0, 0.09);
      }

      &:focus {
        color: #000000;
        background-color: #fafafa;
        box-shadow: inset 0 1px 3px 0 rgba(0, 0, 0, 0.07);
        border: solid 1px rgba(0, 0, 0, 0.09);
        margin-right: 6px;
      }
    }
  }

  .empty, .doesnt-return-message {
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .stats {
    width: 100%;
    padding: 10px 20px;
    margin-top: 10px;
    background-color: $sidebarBackgroundColor;
    border: 1px solid $sidebarBorderColor;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
    }

    .stats-empty {

    }

    .stats-info {
      ul {
        li {
          .title {
            text-decoration: underline;
          }

          .value {
            margin-left: 5px;
          }
        }
      }
    }
  }

  .piece {
    padding-left: 20px;
    padding-right: 20px;
  }

  .insert {
    height: 30px;
    padding-top: 5px;
    padding-bottom: 5px;

    .add {
      display: none;
      cursor: pointer;
      color: $default-icon-color;

      &:hover {
        color: $selection-icon-color;
      }
    }

    &:hover {
      .add {
        display: inline-block;
      }
    }
  }

  .cont {
    display: flex;
    flex-direction: row;
    width: 100%;

    .component {
      width: calc(100% - 20px);
      flex-grow: 1;
      flex-shrink: 1;
    }

    .drag-holder {
      background: transparent;
      display: flex;
      flex-direction: column;
      margin-right: 10px;
      width: 10px;
      flex-grow: 0;
      flex-shrink: 0;

      .drag-span {
        flex-grow: 0;
      }

      .drag-icon {
        opacity: 0;
        color: $default-icon-color;
        cursor: pointer;

        &:hover {
          color: $selection-icon-color;
        }
      }
    }

    &:hover {
      .drag-holder {
        .drag-icon {
          opacity: 1;
        }
      }
    }
  }

  .addpiece {
    margin-top: 10px;
  }
}
