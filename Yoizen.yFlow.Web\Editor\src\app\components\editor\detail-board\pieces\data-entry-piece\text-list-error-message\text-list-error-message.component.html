<div class="addName">
  <div class="trash" (click)="deleteElement()" *ngIf="CanDelete && !readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_DELETE_TEXT' | translate }}" placement="top" container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <app-input-with-variables [placeholder]="'VALUE' | translate"
                            [(value)]="value"
                            [wideInput]="true"
                            [isTextArea]="true"
                            [minRows]="3"
                            [validator]="Validator"
                            [spellCheck]="true"
                            [disabled]="readOnly"
                            [extendedStyles]="{ 'height': '100px'}"
                            (valueChange)="TextChange.emit($event)"></app-input-with-variables>
</div>
