import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import { SmartFormPiece, OutputVariableMap } from 'src/app/models/pieces/SmartFormPiece';
import { FormDefinition } from 'src/app/models/cognitivity/FormDefinition';


@Component({
  selector: 'app-smart-form-piece',
  templateUrl: './smart-form-piece.component.html',
  styleUrls: ['./smart-form-piece.component.scss']
})
export class SmartFormPieceComponent extends BasePieceVM implements OnInit {

  @ViewChild('formDropdown', { static: false }) formDropdown : ElementRef;

  getVariablesTypes() {
    return VariableDefinition.variableType;
  }

  model : SmartFormPiece;
  formDetail : FormDefinition;
  availableForms : FormDefinition[] = [];

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as SmartFormPiece;
    this.availableForms = this.editorService.getForms();
  }

  OutputValidator(){
    return false;
  }

  onSelectForm(_id : number | string) {
    const formIdStr = this.model.FormId.toString();

    if (this.formDetail != null && formIdStr === this.formDetail.Id.toString()) {
      return;
    }

    const forms = this.availableForms;
    this.formDetail = null;
    forms.forEach(form => {
      if (form.Id.toString() === formIdStr) {
        this.formDetail = form;
      }
    });

    this.regeneratePieceInfo();
  }

  regeneratePieceInfo() {
    this.model.Outputs = [];
    if (this.formDetail != null) {

      this.formDetail.EntitiesForms.forEach(output => {
        let outputMap = new OutputVariableMap();
        outputMap.formEntityName = output.entity.name;
        outputMap.formEntityId = output.entityId;
        outputMap.type = TypeDefinition.Text;
        this.model.Outputs.push(outputMap);
      });
    }
  }

  getVariableRef(id: number) {
    return this.editorService.getVariableWithId(id);
  }

  setVariableOnOutput(output : OutputVariableMap, variable : VariableDefinition) {
    if(variable == null) {
      output.variableRefId = null;
    }
    else {
      output.variableRefId = variable.Id;
    }
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  onSelectExitBlock(blockData : BlockDefinition) {
    this.model.ExitBlockId = blockData.Id;
  }

  onDeleteExitBlock(blockData : BlockDefinition) {
    this.model.ExitBlockId = null;
  }

  getVariableType(variable: TypeDefinition) : string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }
}
