<div class="item"
     [ngClass]="getClassObject()"
     [ngStyle]="extendedStyles">
  <span *ngIf="!highlightSearch" class="name">{{ name }}</span>
  <div class="name highlighted" *ngIf="highlightSearch" [innerHTML]="textWithHighlight"></div>
  <div class="status-indicator" *ngIf="getStatusClass() !== ''">
    <span class="fas" [ngClass]="{'fa-circle': !isStatusYellow(), 'fa-ban': isStatusYellow()}"></span>
  </div>
  <div class="trash" (click)="onDelete(); $event.stopPropagation()"
       *ngIf="showActions"
       data-toggle="tooltip" ngbTooltip="{{ deleteTooltip | translate }}" placement="right" container="body"
       tooltipClass="tooltip-trash-right">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="onClone(); $event.stopPropagation()"
       *ngIf="showActions && showCloneAction"
       data-toggle="tooltip" ngbTooltip="{{ cloneTooltip | translate }}" placement="right" container="body"
       tooltipClass="tooltip-trash-right">
    <span class="fa fa-clone"></span>
  </div>
  <div class="download" (click)="onDownload(); $event.stopPropagation()"
       *ngIf="showActions && showDownloadButton"
       data-toggle="tooltip" ngbTooltip="{{ downloadTooltip | translate }}" placement="right" container="body"
       tooltipClass="tooltip-trash-right">
    <span class="fa fa-download"></span>
  </div>
</div>
