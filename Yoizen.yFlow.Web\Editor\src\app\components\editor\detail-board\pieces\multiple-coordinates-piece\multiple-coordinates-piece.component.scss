@import "_variables";
@import "_mixins";

.multiple-coordinates {
  background-color: #fff;
  min-width: 500px;

  .source {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid $gray;
    display: flex;
    flex-direction: row;
    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  .latitude-prop, .longitude-prop, .key-prop, .label-prop,
  .title-prop, .include-my-coordinates-prop, .my-longitude-prop, 
  .my-latitude-prop, .my-icon-prop, .next, .dest, .label-group-marker-prop, .icon-prop, .zoom-prop {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 10px 10px 10px;
    width: 100%;

    &.my-latitude-prop, &.my-longitude-prop, .my-icon-prop, &.dest{
      padding-left: 20px;
    }

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
    .input-variable{
      width: 100%;
      .input {
        flex-grow: 1;
      }
    }
  }

  .implicit-variables {
    padding: 0 10px;
    margin-bottom: 10px;

    .variables-info {
      font-family: $fontFamily;
      margin-bottom: 5px;
      color: #767676;
    }

    .variables-table {
      display: table;
      width: 100%;

      .variables-header, .variable-row {
        height: 30px;
      }

      .variables-header {
        display: table-row;
        font-family: $fontFamilyTitles;

        div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }
      }

      .variable-row {
        display: table-row;

        .variable-cell {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;

          .variable-name {
            @include variable-name;
          }

          .variable-type {
            @include variable-type;
          }
        }

        &:last-child {
          .variable-cell {
            border-bottom-style: none;
          }
        }
      }
    }
  }
}
