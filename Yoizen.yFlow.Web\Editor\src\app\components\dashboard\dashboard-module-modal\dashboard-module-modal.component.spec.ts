import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardModuleModalComponent } from './dashboard-module-modal.component';

describe('DashboardModuleModalComponent', () => {
  let component: DashboardModuleModalComponent;
  let fixture: ComponentFixture<DashboardModuleModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DashboardModuleModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardModuleModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
