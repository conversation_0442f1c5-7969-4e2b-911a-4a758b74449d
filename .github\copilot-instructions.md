# yFlow AI Coding Instructions

## 🏗️ Sistema de Arquitectura Conversacional de Múltiples Canales

yFlow es un **sistema conversacional de microservicios** que ejecuta flujos de bots en múltiples plataformas de mensajería (WhatsApp, Telegram, Facebook, Instagram, etc.) con una arquitectura de componentes modularizados ("pieces").

### 🔧 Componentes Principales

#### **WebExecutor** (Motor de Ejecución Principal)
- **Propósito**: Procesa mensajes entrantes y ejecuta flujos conversacionales
- **Patrón**: Factory de ejecutores por canal (`FlowExecutorWhatsapp`, `FlowExecutorTelegram`, etc.)
- **Puerto Debug**: `9230` (diferente del Web que usa `9229`)
- **Estructura**: `application/executors/` → `domain/models/executor/pieces/implementations/` (150+ tipos de pieces)

#### **Web** (Interface de Gestión)
- **Backend**: Express.js con Angular 8 embebido en `Editor/`
- **Puerto Debug**: `9229`
- **Frontend**: Angular 8.2.14 con Node 18.20.3 (`--openssl-legacy-provider` requerido)

#### **IntervalServices** (Tareas Programadas)
- **Funciones**: Consolidación de métricas, generación de reportes, limpieza de logs
- **Dependencia crítica**: Debe ejecutarse ANTES que WebExecutor para crear tablas de estadísticas

#### **Worker** (Gestión de Contexto)
- **Propósito**: Limpia contexto de casos cuando ySocial cierra casos
- **Tecnología**: Service Bus + Redis

## ⚙️ Patrones de Desarrollo Críticos

### 1. **Sistema de Cross-Project Dependencies**
```typescript
// OBLIGATORIO: Usar rutas relativas entre proyectos hermanos
import { logger } from '../../../../Yoizen.yFlow.Helpers/src/Logger';
import { config } from '../../../../Yoizen.yFlow.Helpers/src/Config';
import Flow from '../../../../Yoizen.yFlow.Web/models/flow';
```

### 2. **Patrón de Pieces Ejecutables**
```typescript
export class IntegrationPieceExecutor extends BasePieceExecutor {
    async execute(piece: IntegrationPiece, control: Control, executor: FlowExecutor): Promise<void>
}
```
- Todas las funcionalidades del flujo se implementan como "pieces"
- Factory pattern en `PieceExecutorFactory` mapea tipos a ejecutores

### 3. **Logging Estructurado (OBLIGATORIO)**
```typescript
logger.info({ 
    caseId: control.body.case.id, 
    messageId: control.body.message.id, 
    userId: control.body.user.id 
}, `[${control.body.message.id}] Message content`);
```

### 4. **Port-Based Architecture**
- Inyección de dependencias con interfaces: `IIntervalPort`, `ILogPort`, `IFilePort`
- Adaptadores en `Yoizen.yFlow.Infrastructure/src/adapters/`

## 🛠️ Flujos de Desarrollo

### **Comandos de Build & Debug Esenciales**
```bash
# WebExecutor (TypeScript ES modules)
npm run dev          # Development con ts-node y preload.mjs
npm run debug        # Debug en puerto 9230
npm run buildDocker  # Build para containers

# Web (Express + Angular híbrido)
npm run dev          # Backend development (puerto 9229)
cd Editor && npm run startNode18  # Frontend Angular 8

# Orden de instalación de dependencias
npm install --legacy-peer-deps  # En Yoizen.yFlow.Web (problemas de deps)
```

### **Multi-Stage Build Pattern**
- TypeScript compilation con 3 configuraciones: `tsconfig.json`, `tsconfig.web.json`, `tsconfig.docker.json`
- ES modules en WebExecutor (`"type": "module"`) vs CommonJS en Web
- Dockerfile quita `"type": "module"` para compatibilidad VM

## 🗄️ Gestión de Base de Datos

### **Arquitectura Multi-Database**
- **yFlow DB** (MSSQL): Configuraciones, flujos, usuarios
- **Context DB** (MySQL/Redis): Contexto de conversaciones activas
- **Intervals DB** (MySQL): Métricas temporales y estadísticas
- **Integration Audit DB** (MySQL): Monitoreo de integraciones

### **Configuración por Variables de Entorno**
```bash
# Ejemplo WebExecutor
DBCONTEXTDIALECT=redis|mysql|disabled
DBINTERVALSDIALECT=mysql|mssql
REDISCACHEHOSTNAME=<redis-host>
```

## 📐 Restricciones de Versión (CRÍTICO)

### **Stack Fijo - NO ACTUALIZAR**
- **Node.js**: `18.20.3`
- **TypeScript**: `3.5.3` (sin opcional chaining `?.`, nullish coalescing `??`)
- **Angular**: `8.2.14` (sin Ivy, Standalone Components, Signals)

### **Patrones Prohibidos (TypeScript 3.5.3)**
```typescript
// ❌ PROHIBIDO - Funcionalidades post-3.5.3
obj?.property                    // Optional chaining
value ?? 'default'              // Nullish coalescing
#privateField                   // Private fields

// ✅ PERMITIDO - Alternativas compatibles
obj && obj.property
value !== null && value !== undefined ? value : 'default'
private privateField
```

## 🔧 Integración y Deployment

### **Container Strategy**
- **Kubernetes**: Usa Azure Blob Storage (`useAzureBlobStorage=true`)
- **VM**: Copia builds desde containers Docker Hub
- **Multi-arch**: Diferentes Dockerfiles para VM vs K8s

### **CI/CD Pattern**
- Branch strategy: `feature/*` → `test` → `release/*`
- Tag format: `release/9.0/0`, `release/9.0/1`
- Pipeline separation: K8s vs VM builds

### **Redis Pub/Sub**
- Comunicación entre componentes para cambios de flujo
- Cache de contexto + notificaciones de updates

## 💡 Debugging y Troubleshooting

### **Puertos de Debug**
- Web: `9229` (`--inspect=9229`)
- WebExecutor: `9230` (`--inspect=9230`) 
- VS Code: "Attach to Process" para debugging

### **Logging Context**
- Siempre incluir `caseId`, `messageId`, `userId` en logs
- Pino logger con structured logging
- Error tracking en base dedicada

**Cuando trabajes en yFlow:**
1. **Respeta ABSOLUTAMENTE las restricciones de versión** - evita funcionalidades modernas de TS/Angular
2. **Usa el patrón de cross-project dependencies** con rutas relativas
3. **Implementa nuevas funcionalidades como pieces** en el sistema modular
4. **Mantén logging estructurado** con contexto de caso/mensaje/usuario
5. **Considera el orden de startup** (IntervalServices → WebExecutor → Web)

## 3. Principios Generales de Código y Calidad

*   **SOLID:** Adhiérete estrictamente a los principios SOLID.
*   **Pequeñas Funciones:** Escribe funciones pequeñas, bien definidas y con una única responsabilidad. Evita funciones monolíticas.
*   **Rendimiento:** Escribe código performante, considerando que las aplicaciones pueden operar bajo carga.
*   **Consistencia:** Mantén la consistencia con el estilo de código existente en el proyecto. Si se te proporciona código existente, imita su estilo.
*   **Modularidad (Node.js):** Organiza el código backend en capas modulares y separa responsabilidades (ej: controladores, servicios, repositorios).
*   **Manejo de Errores:** Implementa un manejo de errores robusto y logging (ej: con Winston en Node.js). Valida todas las entradas.

## 4. Estándares de Código y Formato

*   **Linters/Formatters:** Asume que el proyecto utiliza Prettier y ESLint con configuraciones estrictas. El código generado DEBE cumplir estas reglas implícitas.
*   **Indentación:** Usa 2 o 4 espacios para la indentación (sé consistente con el código existente si lo hay), **NUNCA tabs**.
*   **Tipado Estricto (TypeScript):** Asume `strict: true` en `tsconfig.json`.
    *   **Evita `any` obsesivamente.** Usa tipos explícitos, interfaces o type aliases. Si `any` es la única opción viable debido a las limitaciones de versión o complejidad extrema, justifícalo.
    *   Usa `interfaces` para definir la forma de los objetos públicos y `type aliases` para tipos más simples o uniones/intersecciones.
    *   Prefiere `const` sobre `let`. Usa `let` solo cuando la reasignación sea necesaria. No uses `var`.
*   **Asincronía (Node.js):** Utiliza `async/await` para operaciones asíncronas. Evita callbacks anidados (callback hell).

## 5. Estándares Específicos de Angular (Frontend)

### 5.1. Estructura de Componentes
Sigue **exactamente** esta estructura al generar o modificar componentes:
```typescript
// 0. Angular Core & RxJS Imports (primero los de @angular/core, luego rxjs, luego operadores)
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild, ElementRef, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Subscription, Subject, Observable } from 'rxjs';
import { takeUntil, map, filter } from 'rxjs/operators';

// 1. Local Imports (Servicios, Interfaces, etc. del proyecto)
import { MiServicio } from './ruta/a/mi.servicio';
import { MiInterfaz } from './ruta/a/mi.interfaz';

@Component({
  selector: 'app-mi-componente',
  templateUrl: './mi-componente.component.html',
  styleUrls: ['./mi-componente.component.scss'], // o .css
  // changeDetection: ChangeDetectionStrategy.OnPush // Considerar para optimización si aplica
})
export class MiComponente implements OnInit, OnDestroy { // Implementar interfaces relevantes

  // 2. Decoradores de Propiedad (@Input, @Output, @ViewChild, @ContentChild, etc.) - Públicos primero, luego privados si es estrictamente necesario
  @Input() public datoEntrada: string;
  @Output() public eventoSalida = new EventEmitter<boolean>();
  @ViewChild('miElemento') private miElementoRef: ElementRef<HTMLInputElement>; // Tipar ElementRef si es posible

  // 3. Propiedades Públicas (para binding en template o acceso externo limitado)
  public titulo: string = 'Título por Defecto';
  public isLoading: boolean = false;
  public datosMostrados: MiInterfaz[] = []; // ¡SIEMPRE preferir tipo específico en lugar de any[]!

  // 4. Propiedades Privadas (lógica interna del componente)
  private readonly destroy$ = new Subject<void>(); // Para desuscripción de observables. `readonly` si no se reasigna.
  private _miPropiedadPrivada: number = 0;
  private datosInternos: MiInterfaz | null = null; // ¡SIEMPRE preferir tipo específico!

  // 5. Constructor (SOLO para Inyección de Dependencias. Mantenerlo lo más ligero posible)
  constructor(
    private miServicio: MiServicio,
    // private cdRef: ChangeDetectorRef, // Inyectar si se usa OnPush y se necesita cd manual
    /* otros servicios */
  ) {}

  // 6. Lifecycle Hooks (ngOnInit, ngOnDestroy, ngAfterViewInit, etc. en orden de ejecución)
  public ngOnInit(): void { // `public` es opcional para lifecycle hooks, pero por consistencia
    this._cargarDatosIniciales();
  }

  public ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 7. Métodos Públicos (handlers de eventos del template, o API pública del componente)
  public onButtonClick(): void {
    // Lógica del handler
    this.eventoSalida.emit(true);
  }

  public refrescarDatos(): void {
    this._cargarDatos();
  }

  // 8. Métodos Privados (lógica interna del componente, prefijo `_`)
  private _cargarDatosIniciales(): void {
    this.isLoading = true;
    this.miServicio.obtenerDatosIniciales().pipe(
      takeUntil(this.destroy$)
    ).subscribe(
      (datos: MiInterfaz[]) => { // Tipar la respuesta
        this.datosMostrados = datos;
        this.isLoading = false;
        // this.cdRef.detectChanges(); // Si se usa OnPush
      },
      (error: any) => { // Tipar el error si se conoce su estructura
        console.error('Error al cargar datos iniciales:', error); // Implementar logging/manejo de error adecuado
        this.isLoading = false;
        // this.cdRef.detectChanges(); // Si se usa OnPush
      }
    );
  }

  private _cargarDatos(): void {
     // Lógica similar usando this.miServicio
  }

  private _transformarDato(dato: MiInterfaz): string { // ¡SIEMPRE preferir tipo específico!
    // Lógica de transformación
    return dato.algunCampo || '';
  }
}
```

### 5.2. Módulos Angular
*   Sigue la estructura recomendada: `feature modules`, `core module`, `shared module`.
*   Implementa **Lazy Loading** para los `feature modules` siempre que sea apropiado.

### 5.3. Observables (RxJS)
*   **Desuscripción OBLIGATORIA:** SIEMPRE desuscríbete de los observables para prevenir memory leaks. Usa el patrón `takeUntil(this.destroy$)` como se muestra en la estructura del componente y en el patrón recomendado abajo. **No hacerlo es un error crítico.**
*   **Async Pipe:** Prefiere usar el `async` pipe en los templates (`miObservable$ | async`) en lugar de suscripciones manuales en el componente cuando sea posible y el ciclo de vida del observable esté ligado al template.

### 5.4. Formularios
*   Prefiere **Reactive Forms** (`FormBuilder`, `FormGroup`, `FormControl`) sobre Template-Driven Forms, especialmente para formularios de complejidad media o alta.
*   Incluye validaciones robustas (ej: `Validators.required`, `Validators.email`, validadores personalizados).

### 5.5. Servicios
*   Usa servicios para encapsular la lógica de negocio, llamadas a API y manejo del estado compartido. Inyéctalos en los componentes usando el constructor. Los servicios deben ser lo más cohesivos posible.

### 5.6. Documentación de Métodos
*   Documenta los métodos públicos y cualquier lógica compleja usando comentarios JSDoc/TSDoc (en inglés). Explica el *por qué*, no el *qué*.

## 6. Patrones Recomendados

### 6.1. Manejo de Observables con `takeUntil` (Obligatorio)
```typescript
// Dentro de un componente o servicio que implemente OnDestroy
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
// Asume que MiServicio y sus métodos están definidos e importados
// import { MiServicio } from './ruta/a/mi.servicio';
// import { OnInit, OnDestroy } from '@angular/core'; // Si es un componente

// export class MiClase implements OnInit, OnDestroy { // O solo OnDestroy si es un servicio que necesita limpiar
export class MiClase { // Simplificado para el ejemplo
  private readonly destroy$ = new Subject<void>(); // `readonly` es buena práctica aquí

  constructor(private miServicio: /* MiServicio */ any) {} // Reemplazar `any` con el tipo real

  // ngOnInit() { // Si es un componente
  public metodoQueUsaObservable(): void {
    this.miServicio.getDatos() // Asume que getDatos() devuelve un Observable
      .pipe(
        // otros operadores como map, filter, etc.
        takeUntil(this.destroy$) // Asegura la desuscripción
      )
      .subscribe((datos: any) => { // Tipar `datos`
        // Lógica con los datos
      }, (error: any) => { // Tipar `error`
        // Manejo de errores
      });
  }

  // ngOnDestroy() { // Si es un componente
  public limpiarSuscripciones(): void { // Método genérico si no es un componente
    this.destroy$.next();
    this.destroy$.complete(); // Libera recursos del Subject
  }
}
```

### 6.2. Manejo Básico de Formularios Reactivos
```typescript
import { FormBuilder, FormGroup, Validators } from '@angular/forms'; // Asegúrate que FormGroup se importa si se usa explícitamente

export class MiFormularioComponent {
  public miFormulario: FormGroup; // Tipar explícitamente

  constructor(private fb: FormBuilder) {
    this.miFormulario = this.fb.group({
      nombre: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      // otros campos
    });
  }

  public onSubmit(): void { // `public` para acceso desde template
    if (this.miFormulario.valid) {
      console.log('Formulario válido:', this.miFormulario.value);
      // Lógica de envío: this.algunServicio.enviar(this.miFormulario.value)
    } else {
      console.warn('Formulario inválido. Por favor, revisa los campos.');
      // Marcar campos como tocados para mostrar errores si es necesario
      this.miFormulario.markAllAsTouched();
    }
  }
}
```

## 7. Testing

*   Escribe **pruebas unitarias** usando Jasmine/Karma (típico en Angular 8) o Jest si está configurado.
*   Asegura una **cobertura de pruebas alta**, especialmente para la lógica de negocio crítica, servicios y componentes complejos.
*   Usa **mocks y spies** (ej: `jasmine.createSpyObj`, `jest.fn()`) para aislar unidades y probar interacciones con dependencias (servicios, APIs).

## 8. Version Control (Git)

*   **Mensajes de Commit:** En **inglés**, siguiendo **Conventional Commits** (`feat:`, `fix:`, `docs:`, etc.).
*   **Ramas:** Usa ramas de feature (`feature/nombre-feature`). No hagas commit directamente a `main` o `develop`.
*   **Commits Pequeños y Atómicos:** Mantén los commits pequeños y enfocados en un único cambio lógico.
*   **Code Reviews:** Asume que todo el código pasará por revisiones antes de mergearse a ramas principales. Escribe código claro y mantenible.

## 9. Seguridad

*   **Credenciales:** **NUNCA JAMÁS** comites credenciales, tokens API, u otros secretos al repositorio. Usa variables de entorno (ej: con `dotenv` para desarrollo local) y `.gitignore` adecuadamente.
*   **Validación de Entradas:** Valida y sanitiza **TODAS** las entradas del usuario (frontend y backend) para prevenir vulnerabilidades (XSS, SQL Injection, etc.). Sé paranoico.
*   **Auditorías:** Ejecuta regularmente auditorías de seguridad (ej: `npm audit`) e actualiza dependencias con vulnerabilidades conocidas, **siempre y cuando las actualizaciones sean compatibles con las versiones de stack definidas (Sección 2)**.

## 10. Comportamiento ante Conflictos o Dudas

*   Si una solicitud del usuario entra en conflicto directo con estas reglas (especialmente las Restricciones de Versión o el uso de funcionalidades prohibidas):
    1.  **NO generes el código solicitado que viola las reglas.**
    2.  Indica claramente cuál es la restricción específica que impide cumplir la solicitud.
    3.  Ofrece una alternativa compatible con las reglas del proyecto, si existe.
    4.  Si no estás seguro o la solicitud es ambigua respecto a estas reglas, pide clarificación.

## 11. Formato de Tus Respuestas (GitHub Copilot)

Cuando proporciones soluciones o explicaciones, sigue **estrictamente** este formato:

1.  **Explicación Breve (Español):**
    *   Describe concisamente el problema que se aborda y la solución propuesta.
    *   **Si la solicitud original del usuario requería una funcionalidad no permitida por las restricciones de versión, menciónalo aquí y explica cómo tu solución se adapta.**

2.  **Código Fuente (Inglés):**
    *   Proporciona el código relevante, adhiriéndote a TODAS las guías anteriores (idioma inglés para código y comentarios, formato, **estricto cumplimiento de versiones (Sección 2)**, patrones, etc.).
    *   Usa bloques de código markdown con el tipo de lenguaje especificado (ej: ```typescript).
        ```typescript
        // Your code here (in English, following all guidelines)
        // Ensure NO features beyond TypeScript 3.5.3 or Angular 8.2.14 are used.
        ```

3.  **Explicación Detallada (Opcional/Si es complejo - Español):**
    *   Si el código no es trivial, explica los pasos clave, decisiones de diseño o la lógica importante.
    *   Justifica las decisiones si se desvían de una práctica "moderna" común debido a las restricciones de versión.

4.  **Consideraciones y Advertencias (Español):**
    *   Menciona puntos importantes, posibles efectos secundarios, o alternativas si son relevantes.
    *   **IMPRESCINDIBLE: Advierte explícitamente si una solución que sería común en versiones más nuevas de Angular/TypeScript NO es aplicable aquí debido a las restricciones de versión (Sección 2), y por qué.** Por ejemplo: "Normalmente usaríamos Optional Chaining aquí, pero como estamos en TypeScript 3.5.3, debemos verificar la existencia de propiedades anidadas manualmente."