{"id": 3, "name": "Teams COMPLEJO", "channel": "16402", "type": "1", "def": {"DefaultBlocks": [{"Id": "1", "Name": "Bienvenida", "SystemProtected": true, "Pieces": [{"type": "message-piece", "Id": 4, "Uid": "a3358401-8afe-4668-9cae-4c68f631014e", "MenuUuid": null, "TextList": [{"text": "<PERSON><PERSON>, esto es un bot de teams. Que hacemos? "}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "jump-to-block-piece", "Id": 15, "Uid": "8d5fb7e9-055a-4bda-bb65-08ce04f4c36b", "BlockId": "dbec7bd6-2995-4142-82d5-2a51d357aedd", "__type": "JumpToBlockP<PERSON>ce"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "9", "Name": "Transferidos", "SystemProtected": true, "Pieces": [], "Visible": false, "ModuleId": 3, "IsPublic": true}, {"Id": "10", "Name": "<PERSON><PERSON><PERSON>", "SystemProtected": true, "Pieces": [], "Visible": false, "ModuleId": 3, "IsPublic": true}, {"Id": "11", "Name": "Finalizados por Inactividad", "SystemProtected": true, "Pieces": [], "Visible": false, "ModuleId": 3, "IsPublic": true}, {"Id": "2", "Name": "Vinculación", "SystemProtected": true, "Pieces": [{"type": "comment-piece", "Id": 5, "Uid": "a0c0b329-2292-4b72-8f81-aaf3b4b7cc7b", "Text": "Comentario de bloque vinculacion\n", "__type": "CommentPiece"}, {"type": "message-piece", "Id": 6, "Uid": "7e90e50f-f01a-44f0-b07d-ac8222e59dde", "MenuUuid": null, "TextList": [{"text": "Vinculacion"}], "Buttons": [], "__type": "MessagePieceType"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "3", "Name": "Desvinculación", "SystemProtected": true, "Pieces": [{"type": "message-piece", "Id": 7, "Uid": "b7d31fbe-48a7-4a14-a9e7-c41c3314541a", "MenuUuid": null, "TextList": [{"text": "Desvinculacion"}], "Buttons": [], "__type": "MessagePieceType"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "4", "Name": "Respuesta por defecto", "SystemProtected": true, "Pieces": [{"type": "message-piece", "Id": 8, "Uid": "8cfaa559-aff5-47b0-bb65-d6bed2023170", "MenuUuid": null, "TextList": [{"text": "Respuesta por defecto"}], "Buttons": [], "__type": "MessagePieceType"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "15", "Name": "Cierre por Inactividad", "SystemProtected": true, "Pieces": [{"type": "message-piece", "Id": 9, "Uid": "67c00d3d-78b1-4405-81c5-************", "MenuUuid": null, "TextList": [{"text": "Se cierra por inact"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "close-case-piece", "Id": 1, "Uid": "afd2e489-2513-4d9f-8a50-3e3ce4917f72", "closeCase": true, "markAsPendingReplyFromCustomer": 1, "customMessageForPendingReply": null, "pendingReplyFromCustomerBlockId": "-1", "pendingReplyFromCustomerMinutes": 60, "__type": "CloseCasePiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "7", "Name": "Respuesta en caso de error", "SystemProtected": true, "Pieces": [{"type": "message-piece", "Id": 2, "Uid": "5b1ec349-2f27-4a32-b5c2-b4259e6ae650", "MenuUuid": null, "TextList": [{"text": "Ha ocurrido un error. <PERSON><PERSON> derivado con un operador."}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "derive-piece", "Id": 3, "Uid": "fe8bac85-4cd4-4538-94a1-23738fef8d21", "Message": null, "MarkAsVim": false, "AlertMessage": null, "Context": null, "RequiresVoiceCall": false, "RequiresSummary": false, "SummaryType": "brief", "__type": "DeriveOperator<PERSON><PERSON>ce"}], "Visible": true, "ModuleId": 3, "IsPublic": true}], "BlockList": [{"Id": "8ec37bea-58e6-4242-bf9c-2a1e95fd2244", "Name": "Derivar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 13, "Uid": "6270716d-ae4f-402d-ac50-77483a8d885c", "MenuUuid": null, "TextList": [{"text": "Hola llegaste a derivar, te comunico con el agente.\nGrupo Acciones\n"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "derive-piece", "Id": 14, "Uid": "956e5f30-39a0-4e59-86b6-ebf47c2742c5", "Message": null, "MarkAsVim": false, "AlertMessage": null, "Context": null, "RequiresVoiceCall": false, "RequiresSummary": false, "SummaryType": "brief", "__type": "DeriveOperator<PERSON><PERSON>ce"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "dbec7bd6-2995-4142-82d5-2a51d357aedd", "Name": "Encriptar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 17, "Uid": "97df94c5-5026-485f-827a-e2e0eda663db", "MenuUuid": null, "TextList": [{"text": "Hola dejanos tu contraseña para que la encriptemos\n\n{{caseId}}"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "data-entry-piece", "Id": 19, "Uid": "82d911db-f2b3-4bf2-8102-9fb96deb8050", "MenuUuid": null, "VariableId": 1000, "Regex": "Ninguna", "ErrorMessage": null, "ErrorMessages": [{"text": "Error en la pieza ingreso de datos"}], "TryLimit": 0, "ErrorBlockId": "d097b06b-ee53-450d-8357-1842d0d37c3d", "HasCondition": false, "FirstValue": null, "Operator": "=", "SecondValue": null, "ParseFormat": null, "CheckCommands": true, "CheckCommandsAlways": false, "CheckCognitivity": false, "CheckCognitivityAlways": false, "MarkAsPendingReplyFromCustomer": 1, "CustomMessageForPendingReply": null, "PendingReplyFromCustomerBlockId": "-1", "PendingReplyFromCustomerMinutes": 60, "JumpToOtherBlock": true, "HideNextData": false, "__type": "DataEntry"}, {"type": "message-piece", "Id": 40, "Uid": "00bcd5db-f268-49ca-8cfd-22c751fa3ed9", "MenuUuid": null, "TextList": [{"text": "se ingresó este dato: {{VariableA}}"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "encrypt-piece", "Id": 16, "Uid": "edfac06e-649e-449f-831f-cbabcf2e38fe", "VariableToEncryptId": 1000, "VariableEncryptedId": 1001, "EncryptModeType": 0, "EncryptPaddingType": 0, "EncryptKey": "1234567890123456", "ErrorMessage": null, "ErrorBlockId": "2a49b295-d59b-4434-8213-7240b5eebd6a", "EncryptType": 0, "CustomMethod": false, "Delimiter": null, "IvBytesLength": 16, "ReplaceCharacters": false, "CharactersToReplace": [], "__type": "EncryptPiece"}, {"type": "message-piece", "Id": 41, "Uid": "f7eb8b1d-4440-45a9-a393-ad17303dc966", "MenuUuid": null, "TextList": [{"text": "Se encriptó correctamente, mando al otro bloque"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "jump-to-block-piece", "Id": 25, "Uid": "31d5aada-1eda-4e0e-81fc-1a8e395afe46", "BlockId": "a40ce353-855a-4507-8675-8170fbb8a63c", "__type": "JumpToBlockP<PERSON>ce"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "a40ce353-855a-4507-8675-8170fbb8a63c", "Name": "Desencriptar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 26, "Uid": "d8053f12-559d-4956-80e9-6b8f43af3288", "MenuUuid": null, "TextList": [{"text": "Llegaste a pieza desencriptar"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "decrypt-piece", "Id": 28, "Uid": "e465615b-a44e-4a97-bcf5-196cc285b54b", "VariableToDecryptId": 1001, "VariableDecryptedId": 1002, "DecryptModeType": 0, "DecryptPaddingType": 0, "DecryptKey": "1234567890123456", "ErrorMessage": null, "ErrorBlockId": "15daa723-7b76-4a47-875c-77c097e8b01b", "DecryptType": 0, "CustomMethod": false, "Delimiter": null, "IvBytesLength": 16, "ReplaceCharacters": false, "CharactersToReplace": [], "__type": "DecryptPiece"}, {"type": "message-piece", "Id": 27, "Uid": "8aba0db4-e1ea-4425-99aa-4a2cc3980bd0", "MenuUuid": null, "TextList": [{"text": "Esto es lo que pusiste\n{{VariableA}}\n\nEsto es lo que pusiste pero encriptado :\n{{VariableB}}\n\nEsto es lo que pusiste pero desencriptado\n{{VariableC}}"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "call-block-as-procedure-piece", "Id": 38, "Uid": "96882834-95bb-4d42-8098-604645c1c4b1", "BlockId": "f17e63dd-1352-432c-9d19-010be79bdb36", "__type": "CallBlockAsProcedurePiece"}, {"type": "jump-to-block-piece", "Id": 32, "Uid": "059ebe4f-c902-44ca-995f-7226376763a8", "BlockId": "e364e9a2-4bdb-48c6-aab8-b8b4711687c7", "__type": "JumpToBlockP<PERSON>ce"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "d097b06b-ee53-450d-8357-1842d0d37c3d", "Name": "Error ingreso de datos encriptar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 20, "Uid": "2868fb3a-b612-4f3f-b165-7eed9939f7e4", "MenuUuid": null, "TextList": [{"text": "Error ingreso de datos encriptar, se cierra caso."}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "close-case-piece", "Id": 21, "Uid": "8245c7fd-21e9-41a4-a745-de81574cbe26", "closeCase": true, "markAsPendingReplyFromCustomer": 1, "customMessageForPendingReply": null, "pendingReplyFromCustomerBlockId": "-1", "pendingReplyFromCustomerMinutes": 60, "__type": "CloseCasePiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "2a49b295-d59b-4434-8213-7240b5eebd6a", "Name": "Error pieza encriptar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 22, "Uid": "a74c3667-d2d9-480e-b748-bda65018cfb0", "MenuUuid": null, "TextList": [{"text": "Error pieza encriptar, se cierra caso"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "close-case-piece", "Id": 23, "Uid": "706f96d0-4313-4d9b-9f40-dd6ce1c72deb", "closeCase": true, "markAsPendingReplyFromCustomer": 1, "customMessageForPendingReply": null, "pendingReplyFromCustomerBlockId": "-1", "pendingReplyFromCustomerMinutes": 60, "__type": "CloseCasePiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "15daa723-7b76-4a47-875c-77c097e8b01b", "Name": "Error pieza desencriptar", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 29, "Uid": "48fb27ea-9b9a-4f58-b02a-beb6cf417d7f", "MenuUuid": null, "TextList": [{"text": "Error pieza desencriptar, se cierra caso."}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "close-case-piece", "Id": 30, "Uid": "220730bc-644b-4be9-a5c1-a1c9cd060c98", "closeCase": true, "markAsPendingReplyFromCustomer": 1, "customMessageForPendingReply": null, "pendingReplyFromCustomerBlockId": "-1", "pendingReplyFromCustomerMinutes": 60, "__type": "CloseCasePiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "e364e9a2-4bdb-48c6-aab8-b8b4711687c7", "Name": "<PERSON><PERSON><PERSON>", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 33, "Uid": "544c59c1-0e5d-4862-9ffe-e87ae1cbf649", "MenuUuid": null, "TextList": [{"text": "Se cierra el caso. Grupo acciones.\n"}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "close-case-piece", "Id": 34, "Uid": "abc78442-1c9c-4686-9c3c-4e6a2f98d194", "closeCase": true, "markAsPendingReplyFromCustomer": 1, "customMessageForPendingReply": null, "pendingReplyFromCustomerBlockId": "-1", "pendingReplyFromCustomerMinutes": 60, "__type": "CloseCasePiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}, {"Id": "f17e63dd-1352-432c-9d19-010be79bdb36", "Name": "Etiquetar.", "SystemProtected": false, "Pieces": [{"type": "message-piece", "Id": 39, "Uid": "ba8b14aa-8b63-4a65-920f-cf53e98d8130", "MenuUuid": null, "TextList": [{"text": "Te etiqueto loco \"Honda\""}], "Buttons": [], "__type": "MessagePieceType"}, {"type": "tag-piece", "Id": 37, "Uid": "a040cdef-7cb9-4c2e-8e8e-7492b2e57c4f", "Key": "ethonda", "importantTag": false, "__type": "TagPiece"}], "Visible": true, "ModuleId": 3, "IsPublic": true}], "StatisticEventList": [], "BlockDeletedList": [], "BlockGroups": [{"Blocks": [], "Name": "<PERSON><PERSON><PERSON> encriptar", "Collapsed": false, "Id": "bdead571-1c67-407f-a304-5b132ba571b7", "SystemProtected": false, "BlockIDs": ["dbec7bd6-2995-4142-82d5-2a51d357aedd", "a40ce353-855a-4507-8675-8170fbb8a63c", "d097b06b-ee53-450d-8357-1842d0d37c3d", "2a49b295-d59b-4434-8213-7240b5eebd6a", "15daa723-7b76-4a47-875c-77c097e8b01b"], "ModuleId": 3, "Module": null}, {"Blocks": [], "Name": "Grupo Acciones", "Collapsed": false, "Id": "cc170d00-1248-4dcf-a7ee-460931900c52", "SystemProtected": false, "BlockIDs": ["8ec37bea-58e6-4242-bf9c-2a1e95fd2244", "e364e9a2-4bdb-48c6-aab8-b8b4711687c7"], "ModuleId": 3, "Module": null}, {"Blocks": [], "Name": "Nuevo grupo", "Collapsed": false, "Id": "a9f30c97-d97c-4a93-b1bd-56c7413acf8e", "SystemProtected": false, "BlockIDs": ["f17e63dd-1352-432c-9d19-010be79bdb36"], "ModuleId": 3, "Module": null}], "GroupDeletedList": [], "VariableList": [{"Id": 1000, "Name": "VariableA", "DefaultValue": null, "Type": "Text", "IsMasked": false, "Constant": false, "Description": null, "Private": false, "SendYSmart": false, "Persist": false}, {"Id": 1001, "Name": "VariableB", "DefaultValue": null, "Type": "Text", "IsMasked": false, "Constant": false, "Description": null, "Private": false, "SendYSmart": false, "Persist": false}, {"Id": 1002, "Name": "VariableC", "DefaultValue": null, "Type": "Text", "IsMasked": false, "Constant": false, "Description": null, "Private": false, "SendYSmart": false, "Persist": false}], "FormatDefinitions": [{"key": "datetime", "name": "FORMAT_DATETIME", "inputTypes": ["Timestamp", "StringDate", "Text"], "format": "DD/MM/YYYY HH:mm:ss", "formatType": 1}, {"key": "date", "name": "FORMAT_DATE", "inputTypes": ["Timestamp", "StringDate", "Text"], "format": "DD/MM/YYYY", "formatType": 1}, {"key": "time", "name": "FORMAT_TIME", "inputTypes": ["Timestamp", "StringDate", "Text"], "format": "HH:mm:ss", "formatType": 1}, {"key": "integer", "name": "FORMAT_INTEGER", "inputTypes": ["Text", "Number", "Decimal"], "format": "0,0", "formatType": 0}, {"key": "decimal", "name": "FORMAT_DECIMAL", "inputTypes": ["Text", "Number", "Decimal"], "format": "0,0[.]00", "formatType": 0}, {"key": "currency", "name": "FORMAT_CURRENCY", "inputTypes": ["Text", "Number", "Decimal"], "format": "$ 0,0[.]00", "formatType": 0}], "UserFormatDefinitions": [], "CommandDefinitions": [{"id": 0, "name": "Comand<PERSON>", "rule": {"type": 1, "comparisonOperator": "AND", "conditionList": [{"type": 0, "value": "<PERSON><PERSON><PERSON>", "operation": "=", "__type": "SingleCondition"}]}, "destinationBlockId": "e364e9a2-4bdb-48c6-aab8-b8b4711687c7"}, {"id": 1, "name": "Comando 1", "rule": {"type": 1, "comparisonOperator": "AND", "conditionList": [{"type": 0, "value": "Derivar", "operation": "=", "__type": "SingleCondition"}]}, "destinationBlockId": "8ec37bea-58e6-4242-bf9c-2a1e95fd2244"}], "CommandDeletedDefinitions": [], "CognitivityDefinitions": [], "BusinessAvailability": {"daysDefinition": [{"day": 1, "workType": 2, "fromTime": 0, "toTime": 2359}, {"day": 2, "workType": 2, "fromTime": 0, "toTime": 2359}, {"day": 3, "workType": 2, "fromTime": 0, "toTime": 2359}, {"day": 4, "workType": 2, "fromTime": 0, "toTime": 2359}, {"day": 5, "workType": 2, "fromTime": 0, "toTime": 2359}, {"day": 6, "workType": 1, "fromTime": 0, "toTime": 2359}, {"day": 7, "workType": 1, "fromTime": 0, "toTime": 2359}], "nonWorkingDays": [], "workingDates": {"monday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "tuesday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "wednesday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "thursday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "friday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "saturday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "sunday": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}}, "NextVarId": 1003, "NextPieceId": 42, "NextPersistentMenuId": 2, "IntegrationDefinitions": [], "IntegrationDeletedDefinitions": [{"id": 0, "name": "Integración", "inputs": [], "method": "GET", "url": null, "isYSocialService": false, "timeout": 5, "headers": [], "body": null, "outputFields": [], "outputType": 0, "fileOutputFields": null, "ySocialMethod": null, "createObjectForText": false, "enabled": true}], "NextIntegrationId": 1, "NextCommandId": 2, "NextCognitivityId": 0, "PersistentMenu": {"allowUserInput": true, "root": 1}, "PersistentMenuEntries": [{"id": 1, "buttons": []}], "Greetings": {"Locales": [{"locale": "default", "text": ""}]}, "AccountLinking": {"Url": null}, "YSocialSettings": {"Url": "https://dev.ysocial.net/social/", "AccessToken": null, "AccessTokenSecret": null, "ReturnsFromAgent": [], "WhatsappHSMTemplatesServices": [{"ID": 4, "Name": "Linea Tomas", "FullPhoneNumber": "***********", "Description": "actualizacion cita encabezado - es_AR", "Namespace": "d968a4b2_8420_4c8a_92cc_d72bd351153c", "ElementName": "actualizacion_cita_encabezado", "Language": "es_AR", "HeaderType": 1, "HeaderText": "Estimado {{header_1}}", "HeaderTextParameter": {"Name": "header_1", "Description": "Encabezado"}, "HeaderMediaType": 0, "HeaderMediaUrl": null, "Template": "Le recordamos que su próxima cita es dentro de {{1}} días", "FooterType": 0, "FooterText": null, "ButtonsType": 0, "Buttons": null, "TemplateParameters": [{"Name": "1", "Description": "<PERSON><PERSON>"}]}, {"ID": 4, "Name": "Linea Tomas", "FullPhoneNumber": "***********", "Description": "header text sin parametros - es_AR", "Namespace": "d968a4b2_8420_4c8a_92cc_d72bd351153c", "ElementName": "header_text_sin_parametros", "Language": "es_AR", "HeaderType": 1, "HeaderText": "Hola usuario", "HeaderTextParameter": null, "HeaderMediaType": 0, "HeaderMediaUrl": null, "Template": "Esta es una plantilla con header texto sin parámetros.\nY en el body tampoco tenemos parámetros,", "FooterType": 0, "FooterText": null, "ButtonsType": 0, "Buttons": null}, {"ID": 3, "Name": "WhatsApp 669", "FullPhoneNumber": "5491158453669", "Description": "actualizacion cita encabezado - es_AR", "Namespace": "d968a4b2_8420_4c8a_92cc_d72bd351153c", "ElementName": "actualizacion_cita_encabezado", "Language": "es_AR", "HeaderType": 1, "HeaderText": "Estimado {{header_1}}", "HeaderTextParameter": {"Name": "header_1", "Description": "Encabezado"}, "HeaderMediaType": 0, "HeaderMediaUrl": null, "Template": "Le recordamos que su próxima cita es dentro de {{1}} días", "FooterType": 0, "FooterText": null, "ButtonsType": 0, "Buttons": null, "TemplateParameters": [{"Name": "1", "Description": "<PERSON><PERSON>"}]}, {"ID": 3, "Name": "WhatsApp 669", "FullPhoneNumber": "5491158453669", "Description": "header text sin parametros - es_AR", "Namespace": "d968a4b2_8420_4c8a_92cc_d72bd351153c", "ElementName": "header_text_sin_parametros", "Language": "es_AR", "HeaderType": 1, "HeaderText": "Hola usuario", "HeaderTextParameter": null, "HeaderMediaType": 0, "HeaderMediaUrl": null, "Template": "Esta es una plantilla con header texto sin parámetros.\nY en el body tampoco tenemos parámetros,", "FooterType": 0, "FooterText": null, "ButtonsType": 0, "Buttons": null}], "WhatsappUseInteractive": true, "WhatsappUseInteractiveCatalog": true, "WhatsappFlowsByService": []}, "GoogleConfiguration": {"ApiKey": null}, "CognitivityEnabled": false, "CognitivityProject": null, "PrioritizeCognitivity": true, "IceBreakers": null, "HsmJumpEnabled": false, "ApplePayMerchantsConfig": [], "DefaultSettings": {"DefaultDecimalSeparator": ".", "DefaultThousandsSeparator": ",", "DefaultCurrencySymbol": "$"}, "HighAvailability": false}}