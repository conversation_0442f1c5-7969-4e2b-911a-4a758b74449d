import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ConcatenateComponent } from './concatenate.component';

describe('ConcatenateComponent', () => {
  let component: ConcatenateComponent;
  let fixture: ComponentFixture<ConcatenateComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ConcatenateComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConcatenateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
