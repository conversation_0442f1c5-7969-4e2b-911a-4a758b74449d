CREATE PROCEDURE daily_createtable
    @the_interval NVARCHAR(4),
    @the_interval_day NVARCHAR(10)
AS
BEGIN
    DECLARE @create_table_statement NVARCHAR(MAX);

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_' + @the_interval_day + '_' + @the_interval)
    BEGIN
		SET @create_table_statement = N'CREATE TABLE daily_' + @the_interval_day + '_' + @the_interval + ' (
		[date] DATETIME2(3) NOT NULL,
		[interval] INT NOT NULL,
		[interval_datetime] DATETIME2(3) NOT NULL,
		[data] NVARCHAR(MAX),
		[new_cases] INT,
		[transferred] INT,
		[closed_by_yflow] INT,
		[new_messages] INT,
		[hsm_case] INT,
		[case_abandoned] INT,
		[monthly_users] INT
		);';

		EXEC sp_executesql @create_table_statement;
	END
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_abandoned_cases_' + @the_interval_day + '_' + @the_interval)
    BEGIN
		SET @create_table_statement = N'CREATE TABLE daily_abandoned_cases_' + @the_interval_day + '_' + @the_interval + ' (
		[date] DATETIME2(3) NOT NULL,
		[interval] INT NOT NULL,
		[interval_datetime] DATETIME2(3) NOT NULL,
		[flow_id] INT NOT NULL,
		[channel] NVARCHAR(10) NOT NULL,
		[block_id] NVARCHAR(100),
		[data] NVARCHAR(MAX),
		[total] INT,
		[version] INT
		);';

		EXEC sp_executesql @create_table_statement;
	END

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_blocks_' + @the_interval_day + '_' + @the_interval)
    BEGIN
		SET @create_table_statement = N'CREATE TABLE daily_blocks_' + @the_interval_day + '_' + @the_interval + ' (
		[date] DATETIME2(3) NOT NULL,
		[interval] INT NOT NULL,
		[interval_datetime] DATETIME2(3) NOT NULL,
		[flow_id] INT NOT NULL,
		[block_id] NVARCHAR(100) NOT NULL,
		[channel] NVARCHAR(255) NOT NULL,
		[total] INT,
		[version] INT
		);';
	END 

    EXEC sp_executesql @create_table_statement;

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_blocks_sequence_' + @the_interval_day + '_' + @the_interval)
    BEGIN
		SET @create_table_statement = N'CREATE TABLE daily_blocks_sequence_' + @the_interval_day + '_' + @the_interval + ' (
		[date] DATETIME2(3) NOT NULL,
		[interval] INT NOT NULL,
		[interval_datetime] DATETIME2(3) NOT NULL,
		[flow_id] INT NOT NULL,
		[source_block_id] NVARCHAR(100),
		[dest_block_id] NVARCHAR(100),
		[type] INT NOT NULL,
		[channel] NVARCHAR(10) NOT NULL,
		[total] INT,
		[version] INT
		);';

		EXEC sp_executesql @create_table_statement;
	END

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_commands_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_commands_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            command_id INT NOT NULL,
            channel NVARCHAR(255) NOT NULL,
            total INT,
            version INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_default_answers_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_default_answers_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            block_id INT NOT NULL,
            channel NVARCHAR(255) NOT NULL,
            total INT,
            version INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_derivation_key_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_derivation_key_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            derivation_key NVARCHAR(255) NOT NULL,
            channel NVARCHAR(255) NOT NULL,
            total INT,
            version INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_flow_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_flow_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            channel NVARCHAR(255) NOT NULL,
            [data] NVARCHAR(MAX),
            new_cases INT,
            transferred INT,
            closed_by_yflow INT,
            new_messages INT,
            hsm_case INT,
            case_abandoned INT,
            monthly_users INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_group_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_group_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            group_id NVARCHAR(50) NOT NULL,
            channel NVARCHAR(10) NOT NULL,
            total INT,
            version INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_group_sequence_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_group_sequence_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            source_group_id NVARCHAR(50),
            dest_group_id NVARCHAR(50),
            [type] INT NOT NULL,
            channel NVARCHAR(10) NOT NULL,
            total INT,
            version INT
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_integrations_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_integrations_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            integration_id INT NOT NULL,
            channel NVARCHAR(255) NOT NULL,
            total INT,
            error INT,
            version INT,
            total_response_time DECIMAL(18, 2) NOT NULL,
            error_response_time DECIMAL(18, 2) NOT NULL
        );';
        EXEC sp_executesql @create_table_statement;
    END

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'daily_statistic_event_' + @the_interval_day + '_' + @the_interval)
    BEGIN
        SET @create_table_statement = N'CREATE TABLE daily_statistic_event_' + @the_interval_day + '_' + @the_interval + ' (
            [date] DATETIME2(3) NOT NULL,
            [interval] INT NOT NULL,
            interval_datetime DATETIME2(3) NOT NULL,
            flow_id INT NOT NULL,
            statistic_event_id INT NOT NULL,
            channel NVARCHAR(10) NOT NULL,
            total INT,
            version INT,
            block_id NVARCHAR(100),
            block_group_id nvarchar(100) NULL
        );';
        EXEC sp_executesql @create_table_statement;
    END
END;
GO

CREATE PROCEDURE daily_createtables
    @the_interval_day NVARCHAR(10),
    @the_interval NVARCHAR(10) = '0'
AS
BEGIN
    DECLARE @the_interval_aux INT = CAST(@the_interval AS INT);
    DECLARE @the_interval_text NVARCHAR(4);
    DECLARE @the_interval_day_text NVARCHAR(10) = @the_interval_day;

    WHILE @the_interval_aux < 2400
    BEGIN
        SET @the_interval_text = RIGHT('0000' + CAST(@the_interval AS NVARCHAR(4)), 4);

        EXEC daily_createtable @the_interval_text, @the_interval_day_text;

        IF @the_interval_aux % 100 = 0
        BEGIN
            SET @the_interval_aux = @the_interval_aux + 30;
        END
        ELSE
        BEGIN
            SET @the_interval_aux = @the_interval_aux + 70;
        END
    END
END;
GO


CREATE PROCEDURE daily_droptable
    @the_interval NVARCHAR(4)
AS
BEGIN
    DECLARE @the_interval_text NVARCHAR(4);
    DECLARE @the_interval_day_text NVARCHAR(10);
    DECLARE @drop_table_statement NVARCHAR(MAX);

    SET @the_interval_text = @the_interval;

    IF @the_interval = 'day'
    BEGIN
        IF OBJECT_ID('daily_day', 'U') IS NOT NULL 
        BEGIN
            DROP TABLE daily_day;
        END
    END
    ELSE
    BEGIN
        SET @the_interval_day_text = 'your_interval_day_value'; -- Assign the appropriate value here

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS abandoned_cases_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_blocks_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_blocks_sequence_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_commands_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_default_answers_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_derivation_key_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_flow_group_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_group_sequence_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_integrations_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_statistic_event_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS detailed_statistic_event_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        SET @drop_table_statement = 'DROP TABLE IF EXISTS detailed_default_answer_' + @the_interval_day_text + '_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;
    END
END;
GO

CREATE PROCEDURE daily_droptables
AS
BEGIN
    DECLARE @the_interval INT = 0;
    DECLARE @the_interval_text NVARCHAR(4);
    DECLARE @drop_table_statement NVARCHAR(MAX);

    WHILE @the_interval < 2400
    BEGIN
        SET @the_interval_text = RIGHT('0000' + CAST(@the_interval AS NVARCHAR(4)), 4);

        SET @drop_table_statement = 'DROP TABLE IF EXISTS daily_' + @the_interval_text;
        EXEC sp_executesql @drop_table_statement;

        IF @the_interval % 100 = 0
        BEGIN
            SET @the_interval = @the_interval + 30;
        END
        ELSE
        BEGIN
            SET @the_interval = @the_interval + 70;
        END
    END

    IF OBJECT_ID('daily_day', 'U') IS NOT NULL
    BEGIN
        DROP TABLE daily_day;
    END
END;
GO


CREATE PROCEDURE daily_abandoned_cases_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, channel, version ' +
                 N'FROM daily_abandoned_cases_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, block_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_blocks_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, channel, version ' +
                 N'FROM daily_blocks_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, block_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_blocks_sequence_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, source_block_id AS sourceBlockId, ' +
                 N'dest_block_id AS destBlockId, channel, type, version ' +
                 N'FROM daily_blocks_sequence_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, source_block_id, dest_block_id, channel, type, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(new_cases) AS newCase, SUM(transferred) AS transferredToYSocial, ' +
                 N'SUM(closed_by_yflow) AS closedByYFlow, SUM(new_messages) AS newMessage, ' +
                 N'SUM(hsm_case) AS hsmCase, SUM(case_abandoned) AS caseAbandoned, ' +
                 N'SUM(monthly_users) AS monthlyUsers ' +
                 N'FROM daily_' + @intervalDatetime + ' WITH (NOLOCK)';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_commands_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, command_id AS commandId, ' +
                 N'channel, version ' +
                 N'FROM daily_commands_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, command_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_default_answers_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, block_id AS blockId, ' +
                 N'channel, version ' +
                 N'FROM daily_default_answers_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, block_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_derivation_key_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, derivation_key AS derivationKey, ' +
                 N'channel, version ' +
                 N'FROM daily_derivation_key_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, derivation_key, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_flow_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(new_cases) AS newCase, SUM(transferred) AS transferredToYSocial, ' +
                 N'SUM(closed_by_yflow) AS closedByYFlow, SUM(new_messages) AS newMessage, ' +
                 N'SUM(hsm_case) AS hsmCase, SUM(case_abandoned) AS caseAbandoned, ' +
                 N'SUM(monthly_users) AS monthlyUsers, MAX(channel) AS channel, flow_id AS flowId ' +
                 N'FROM daily_flow_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_group_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, group_id AS groupId, ' +
                 N'channel, version ' +
                 N'FROM daily_group_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, group_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_group_sequence_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, source_group_id AS sourceGroupId, ' +
                 N'dest_group_id AS destGroupId, channel, type, version ' +
                 N'FROM daily_group_sequence_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, source_group_id, dest_group_id, channel, type, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_integrations_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, SUM(error) AS error, ' +
                 N'SUM(total_response_time) AS totalResponseTime, SUM(error_response_time) AS errorResponseTime, ' +
                 N'flow_id AS flowId, integration_id AS integrationId, channel, version ' +
                 N'FROM daily_integrations_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, integration_id, channel, version';
    EXEC sp_executesql @query;
END;
GO

CREATE PROCEDURE daily_statistic_event_calculate
    @intervalDatetime VARCHAR(100)
AS
BEGIN
    DECLARE @query NVARCHAR(MAX);
    SET @query = N'SELECT SUM(total) AS total, flow_id AS flowId, statistic_event_id AS statisticEventId, ' +
                 N'block_id AS blockId, block_group_id AS blockGroupId, channel, version ' +
                 N'FROM daily_statistic_event_' + @intervalDatetime + ' WITH (NOLOCK)' +
                 N' GROUP BY flow_id, statistic_event_id, block_id, block_group_id, channel, version';
    EXEC sp_executesql @query;
END;
GO