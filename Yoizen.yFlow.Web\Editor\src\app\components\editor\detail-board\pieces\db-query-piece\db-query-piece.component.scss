@import '_variables';
@import '_mixins';

.db-query {
  background-color: $block-base;
	width: 500px;

  .source, .operator, .value, .next, .normalize, .table-columns, .table-name {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 6px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }

    .input-with-variable {
      flex-grow: 1;
    }

    select {
      width: 100%;
      height: 30px;
      font-weight: normal;
      border-radius: 5px;
      overflow: scroll;
    }

    .input ,.input-variable-area {
      flex-grow: 1;
    }

    .alert {
      width: 100%;
    }
  }

}
