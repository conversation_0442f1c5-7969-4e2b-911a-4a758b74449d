apiVersion: apps/v1
kind: Deployment
metadata:
  name: yflow-yoizen-qa-web
  namespace: yflow-yoizen-qa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yflow-yoizen-qa-web
  template:
    metadata:
      labels:
        app: yflow-yoizen-qa-web
    spec:
      imagePullSecrets:
      - name: dockerhub-secret
      containers:
      - name: yflow-yoizen-qa-web
        image: yoizensa/yflow-web:9.0.0-rcx
        imagePullPolicy: Always
        env:
        - name: dbusername
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_USERNAME
        - name: dbpassword
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_PASSWORD
        - name: dbintervalsusername
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_INTERVALS_USERNAME
        - name: dbintervalspassword
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_INTERVALS_PASSWORD
        - name: ySocialAccessToken
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: YSOCIAL_ACCESS_TOKEN
        - name: ySocialAccessTokenSecret
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: YSOCIAL_ACCESS_TOKEN_SECRET
        - name: googleApiKey
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: GOOGLE_API_KEY
        - name: tokenCognitiveService
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: TOKEN_COGNITIVE_SERVICE
        - name: REDISCACHEKEY
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: REDISCACHEKEY
        - name: storageConnectionString
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: STORAGE_CONNECTION_STRING
        - name: dbIntegrationsAuditusername
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_INTEGRATIONS_AUDIT_USERNAME
        - name: dbIntegrationsAuditpassword
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_INTEGRATIONS_AUDIT_PASSWORD
        - name: ENDPOINT_SB
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: ENDPOINT_SB
        - name: ConnectionString
          valueFrom:
            secretKeyRef:
              name: yflow-yoizen-qa-generic-secret
              key: DB_CONNECTION_STRING
        - name: client
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: CLIENT
        - name: cognitivityApiVersion
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: COGNITIVITY_API_VERSION
        - name: standAlone
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: STAND_ALONE
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: NODE_ENV
        - name: gmt
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: GMT
        - name: ySocialUrl
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: YSOCIAL_URL
        - name: executorUrl
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: EXECUTOR_URL
        - name: dniServiceUrl
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DNI_SERVICE_URL
        - name: dniServiceApiKey
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DNI_SERVICE_API_KEY
        - name: yflowUrl
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: YFLOW_URL
        - name: dbhost
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_HOST
        - name: dbport
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_PORT
        - name: dbname
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_NAME
        - name: urlApiCognitiveServices
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: URL_API_COGNITIVE_SERVICES
        - name: urlClientAppCognitiveServices
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: URL_CLIENT_APP_COGNITIVE_SERVICES
        - name: defaultLanguaje
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DEFAULT_LANGUAJE
        - name: dbintervalsdialect
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTERVALS_DIALECT
        - name: dbintervalshost
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTERVALS_HOST
        - name: dbintervalsname
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTERVALS_NAME
        - name: dbintervalsport
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTERVALS_PORT
        - name: REDISCACHEHOSTNAME
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: REDISCACHEHOSTNAME
        - name: REDISPORT
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: REDISPORT
        - name: storageYFlowPath
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: STORAGE_YFLOW_PATH
        - name: storageTempYFlowPath
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: STORAGE_YFLOW_TEMP_PATH
        - name: dbdialect
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_DIALECT
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: PORT
        - name: enabledLanguajes
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: ENABLED_LANGUAJES
        - name: urlCentralize
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: URL_CENTRALIZE
        - name: hourCentralize
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: HOUR_CENTRALIZE
        - name: minuteCentralize
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: MINUTE_CENTRALIZE
        - name: offset
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: OFFSET
        - name: useAzureBlobStorage
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: USE_AZURE_BLOB_STORAGE
        - name: dbIntegrationsAuditdialect
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTEGRATIONS_AUDIT_DIALECT
        - name: dbIntegrationsAuditname
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTEGRATIONS_AUDIT_NAME
        - name: dbIntegrationsAuditport
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTEGRATIONS_AUDIT_PORT
        - name: dbIntegrationsAudithost
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DB_INTEGRATIONS_AUDIT_HOST
        - name: multipleCores
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: MULTIPLE_CORES
        - name: hostInsideIIS
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: HOST_INSIDE_IIS
        - name: multiCompany
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: MULTI_COMPANY
        - name: useWinston
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: USE_WINSTON
        - name: flowType
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: FLOW_TYPE
        - name: ySmartEnabled
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: YSMART_ENABLED
        - name: socketIo
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: SOCKETIO
        - name: updateCasePieceEnabledInChat
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: UPDATE_CASE_PIECE_ENABLED_IN_CHAT
        - name: FILE_PATH
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: FILE_PATH
        - name: downloadReportTypes
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DOWNLOAD_REPORT_TYPES
        - name: MAX_CONCURRENT_CALLS
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: MAX_CONCURRENT_CALLS
        - name: QUEUE_NAME
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: QUEUE_NAME
        - name: DatabaseType
          valueFrom:
            configMapKeyRef:
              name: yflow-yoizen-qa-generic-configmap
              key: DATABASE_TYPE
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "250Mi"
            cpu: "50m"
          limits:
            memory: "300Mi"
            cpu: "300m"
        readinessProbe:
          httpGet:
            path: /api/health_check
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /api/health_check
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        volumeMounts:
        - name: persistent-storage
          mountPath: "/home/<USER>/StorageYflow/"
      volumes:
      - name: persistent-storage
        persistentVolumeClaim:
          claimName: pvc-yflow-yoizen-qa
      nodeSelector:
        kubernetes.azure.com/agentpool: development
