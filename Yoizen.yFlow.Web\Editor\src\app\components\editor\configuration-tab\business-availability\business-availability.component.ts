import { Component, Input, OnInit } from '@angular/core';
import { FormatDefinition, FormatDefinitionType } from 'src/app/models/FormatDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import * as moment from 'moment';
import * as numeral from 'numeral';
import { TranslateService } from '@ngx-translate/core';
import {
  BusinessAvailability,
  NonWorkingDate,
  NonWorkingDateTypes,
  WorkingDay
} from "../../../../models/BusinessAvailability";
import { NgbCalendar, NgbDate, NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import { getGMT } from 'src/app/Utils/window';

@Component({
  selector: 'app-business-availability',
  templateUrl: './business-availability.component.html',
  styleUrls: ['./business-availability.component.scss']
})
export class BusinessAvailabilityComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @Input() isInsidePiece = false;
  @Input() businessAvailability: BusinessAvailability = null;
  fromWorkingTimes: any = null;
  toWorkingTimes: any = null;
  minDate: NgbDate;
  intervals: number[] = [];
  datesHeaderSelected: boolean[] = [false, false, false, false, false, false, false];
  mouseDownCalendar: boolean;
  GMT: String = getGMT();

  constructor(private editorService: EditorService, private translateService: TranslateService) { }

  ngOnInit() {
    if (!this.isInsidePiece) {
      this.businessAvailability = this.editorService.getBusinessAvailability();
    }
    this.fromWorkingTimes = [];
    this.mouseDownCalendar = false;

    for (let i = 0; i < 48; i++) {
      this.intervals.push(i);
    }

    for (let i = 0; i < 24; i++) {
      this.fromWorkingTimes.push({
        value: i * 100,
        text: i.toString() + ':00'
      });
      this.fromWorkingTimes.push({
        value: i * 100 + 30,
        text: i.toString() + ':30'
      });
    }

    this.toWorkingTimes = [];
    for (let i = 0; i < 24; i++) {
      if (i > 0) {
        this.toWorkingTimes.push({
          value: i * 100,
          text: i.toString() + ':00'
        });
      }
      this.toWorkingTimes.push({
        value: i * 100 + 30,
        text: i.toString() + ':30'
      });
    }
    this.toWorkingTimes.push({
      value: 2359,
      text: '23:59'
    });

    var now = moment();
    this.minDate = new NgbDate(now.year(), now.month() + 1, now.date());
  }

  markDisabled(date: NgbDate, current: any): boolean {
    return date.before(this.minDate);
  }

  getDayName(day: number): string {
    switch (day) {
      case 1: return 'DAY_MONDAY';
      case 2: return 'DAY_TUESDAY';
      case 3: return 'DAY_WEDNESDAY';
      case 4: return 'DAY_THURSDAY';
      case 5: return 'DAY_FRIDAY';
      case 6: return 'DAY_SATURDAY';
      case 0:
      case 7:
        return 'DAY_SUNDAY';
    }
  }

  onSelect($event: NgbDate) {
    if (this.readOnly) {
      return;
    }

    let index = -1;
    for (let i = 0; i < this.businessAvailability.nonWorkingDays.length; i++) {
      let d = this.businessAvailability.nonWorkingDays[i];
      if ($event.day === d.day &&
        $event.month === d.month &&
        $event.year === d.year) {
        index = i;
        break;
      }
    }

    if (index === -1) {
      this.businessAvailability.nonWorkingDays.push(NonWorkingDate.fromNgbDate($event));
    }
    else {
      let day = this.businessAvailability.nonWorkingDays[index];
      if (day.type === NonWorkingDateTypes.Holiday) {
        day.type = NonWorkingDateTypes.Exception;
      }
      else {
        this.businessAvailability.nonWorkingDays.splice(index, 1);
      }
    }
  }

  isSelected(date: NgbDateStruct): boolean {
    for (let i = 0; i < this.businessAvailability.nonWorkingDays.length; i++) {
      let d = this.businessAvailability.nonWorkingDays[i];
      if (date.day === d.day &&
        date.month === d.month &&
        date.year === d.year) {
        return true;
      }
    }

    return false;
  }

  isWorkingDateIntervalSelected(day: number, time: number): boolean {
    switch (day) {
      case 1: return this.businessAvailability.workingDates.monday[time];
      case 2: return this.businessAvailability.workingDates.tuesday[time];
      case 3: return this.businessAvailability.workingDates.wednesday[time];
      case 4: return this.businessAvailability.workingDates.thursday[time];
      case 5: return this.businessAvailability.workingDates.friday[time];
      case 6: return this.businessAvailability.workingDates.saturday[time];
      case 0:
      case 7:
        return this.businessAvailability.workingDates.sunday[time];
    }

    return false;
  }

  toggleWorkingDate(day: number) {
    let dayToToggle: boolean[];
    switch (day) {
      case 1:
        dayToToggle = this.businessAvailability.workingDates.monday;
        break;
      case 2:
        dayToToggle = this.businessAvailability.workingDates.tuesday;
        break;
      case 3:
        dayToToggle = this.businessAvailability.workingDates.wednesday;
        break;
      case 4:
        dayToToggle = this.businessAvailability.workingDates.thursday;
        break;
      case 5:
        dayToToggle = this.businessAvailability.workingDates.friday;
        break;
      case 6:
        dayToToggle = this.businessAvailability.workingDates.saturday;
        break;
      case 0:
      case 7:
        dayToToggle = this.businessAvailability.workingDates.sunday;
        break;
    }

    let value: boolean = this.datesHeaderSelected[day];
    for (let i = 0; i < dayToToggle.length; i++) {
      dayToToggle[i] = !value;
    }

    this.datesHeaderSelected[day] = !value;
  }


  setMouseDownCalendar(state: boolean) {
    this.mouseDownCalendar = state;
  }

  toggleWorkingDateIntervalIfClicked(day: number, time: number) {
    if (this.mouseDownCalendar) {
      this.toggleWorkingDateInterval(day, time);
    }
  }

  toggleWorkingDateInterval(day: number, time: number) {
    if (this.readOnly) {
      return;
    }

    switch (day) {
      case 1:
        this.businessAvailability.workingDates.monday[time] = !this.businessAvailability.workingDates.monday[time];
        break;
      case 2:
        this.businessAvailability.workingDates.tuesday[time] = !this.businessAvailability.workingDates.tuesday[time];
        break;
      case 3:
        this.businessAvailability.workingDates.wednesday[time] = !this.businessAvailability.workingDates.wednesday[time];
        break;
      case 4:
        this.businessAvailability.workingDates.thursday[time] = !this.businessAvailability.workingDates.thursday[time];
        break;
      case 5:
        this.businessAvailability.workingDates.friday[time] = !this.businessAvailability.workingDates.friday[time];
        break;
      case 6:
        this.businessAvailability.workingDates.saturday[time] = !this.businessAvailability.workingDates.saturday[time];
        break;
      case 0:
      case 7:
        this.businessAvailability.workingDates.sunday[time] = !this.businessAvailability.workingDates.sunday[time];
        break;
    }
  }

  isException(date: NgbDateStruct): boolean {
    for (let i = 0; i < this.businessAvailability.nonWorkingDays.length; i++) {
      let d = this.businessAvailability.nonWorkingDays[i];
      if (date.day === d.day &&
        date.month === d.month &&
        date.year === d.year) {
        return d.type === NonWorkingDateTypes.Exception;
      }
    }

    return false;
  }

  getWorkTypes() {
    const workTypes = WorkingDay.workTypes;
    return workTypes;
  }

  getFromTimes() {
    return this.fromWorkingTimes;
  }

  getToTimes() {
    return this.toWorkingTimes;
  }
}
