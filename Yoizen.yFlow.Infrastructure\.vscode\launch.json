{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "runtimeArgs": [
                "--inspect-brk=5858",
                "--loader",
                "ts-node/esm"
            ],
            "program": "${workspaceFolder}\\src\\index.ts",
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ],
            "env": {
                "ySocialUrl": "https://dev.ysocial.net/Social",
                "ySocialAccessToken": "57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=",
                "ySocialAccessTokenSecret": "nHJS3qbsN2sDzFovZmPLsA==",
                "standAlone": "false",
                "NODE_ENV": "dev",
                "multiCompany": "false",
                "generateFilesForDefaultAnswerBlock": "true",
                "yflowUrl": "http://localhost:3000/",
                "defaultLanguaje": "en",
                "enabledLanguajes": "es,en,pt",
                "tokenCognitiveService": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IkFkbWluaXN0cmFkb3IiLCJQcm9qZWN0Ijoie1wiSWRcIjowLFwiTmFtZVwiOlwiQWRtaW5pc3RyYWRvclwiLFwiVG9rZW5cIjpudWxsLFwiRW5hYmxlZFwiOnRydWUsXCJDb25maWRlbmNlXCI6MC4wLFwiRGlzY2FyZFwiOjAuMCxcIkxhbmdcIjpudWxsLFwiTGFzdFVwZGF0ZVwiOlwiMjAyMi0wMS0wM1QwOTo0NToyMC4wNzI0MjEzLTAzOjAwXCIsXCJQcm9qZWN0U2VydmljZUlkXCI6MCxcIlNlcnZpY2VcIjpudWxsLFwiQ3VzdG9tZXJJZFwiOjAsXCJDdXN0b21lclwiOntcIklkXCI6MCxcIk5hbWVcIjpcIkFkbWluaXN0cmFkb3JcIn0sXCJTdGF0dXNcIjowLFwiTGFzdFVwZGF0ZWRTdGF0dXNPa1wiOm51bGx9IiwibmJmIjoxNjQxMjEzOTIwLCJleHAiOjIxMTQ1OTk1MjAsImlhdCI6MTY0MTIxMzkyMCwiaXNzIjoiaHR0cDovL2xvY2FsaG9zdDo0NDMzNSIsImF1ZCI6Imh0dHA6Ly9sb2NhbGhvc3Q6NDQzMzUifQ.ehJUB9wIhkWu0nXoDva73f2DA5ga3POq8S15WWRhmSo",
                "urlApiCognitiveServices": "http://dev.ysocial.net/ysmartcore/api/",
                "urlClientAppCognitiveServices": "https://dev.ysocial.net/ysmartcore/home/<USER>",
                //"tokenCognitiveService": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IkFkbWluaXN0cmFkb3IiLCJQcm9qZWN0Ijoie1wiSWRcIjowLFwiTmFtZVwiOlwiQWRtaW5pc3RyYWRvclwiLFwiVG9rZW5cIjpudWxsLFwiRW5hYmxlZFwiOnRydWUsXCJDb25maWRlbmNlXCI6MC4wLFwiRGlzY2FyZFwiOjAuMCxcIkxhbmdcIjpudWxsLFwiTGFzdFVwZGF0ZVwiOlwiMjAyMS0xMi0yMFQxNToxODoyNC4xNjYzNDQzLTAzOjAwXCIsXCJQcm9qZWN0U2VydmljZUlkXCI6MCxcIlNlcnZpY2VcIjpudWxsLFwiQ3VzdG9tZXJJZFwiOjAsXCJDdXN0b21lclwiOntcIklkXCI6MCxcIk5hbWVcIjpcIkFkbWluaXN0cmFkb3JcIn0sXCJTdGF0dXNcIjowLFwiTGFzdFVwZGF0ZWRTdGF0dXNPa1wiOm51bGx9IiwibmJmIjoxNjQwMDI0MzA0LCJleHAiOjIxMTM0MDk5MDQsImlhdCI6MTY0MDAyNDMwNCwiaXNzIjoiaHR0cHM6Ly95c21hcnRsaW51eC10ZXN0ZWE5Mi5henVyZXdlYnNpdGVzLm5ldCIsImF1ZCI6Imh0dHBzOi8veXNtYXJ0bGludXgtdGVzdGVhOTIuYXp1cmV3ZWJzaXRlcy5uZXQifQ.GMCMvfdugatkLu4s93dF1H714rTnGftUyeMwQ7cmz8E",
                //"urlApiCognitiveServices": "https://ysmartlinux-testea92.azurewebsites.net/api/",
                //"urlClientAppCognitiveServices": "https://ysmartlinux-testea92.azurewebsites.net/api/home/<USER>",
                "client": "Yoizen",
                "flowType": "Bot,Lite",
                "gmt": "-03:00",
                "yFlowUtilsUrl": "https://common.ysocial.net",
                //"yFlowUtilsUrl": "https://common.ysocial.net",
                "dbname": "YFlow",
                "dbusername": "sa",
                "dbpassword": "Maxi2615*",
                "dbport": "1433",
                "dbhost": "localhost",
                "yBiometricUrl": "https://localhost:7166",
                "hostInsideIIS": "false",
                "hostHttps": "false",
                "certificatePfx": "ssl/test.pfx",
                "certificatePfxPassphrase": "test",
                "socketIo": "true",
                "multipleCores": "false",
                "REDISCACHEKEY": "+SLICqbsKg0tJQwyeSe2IHb4tLds7ScCpYMByHjvTdg=",
                "REDISCACHEHOSTNAME": "yflow.redis.cache.windows.net",
                "dbdialect": "mssql",
                "dbintervalsdialect": "mysql",
                "dbintervalsname": "yFlowIntervals",
                "dbintervalsport": "3306",
                "dbintervalsusername": "root",
                "dbintervalspassword": "Maxi2615*",
                "dbintervalshost": "localhost",
            },
        }
    ]
}