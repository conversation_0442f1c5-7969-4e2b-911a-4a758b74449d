import { Component, OnInit, Input, Output , EventEmitter } from '@angular/core';


@Component({
  selector: 'app-text-list-message',
  templateUrl: './text-list-message.component.html',
  styleUrls: ['./text-list-message.component.scss']
})
export class TextListMessageComponent implements OnInit {


  @Input() set Text(value) {
    this.value = value;
  }
  @Input() Index : number;
  @Input() MinRows : number = 5;
  @Input() CanDelete : boolean;
  @Input() Validator : Function;
  @Input() readOnly : boolean = false;
  @Output() onChange = new EventEmitter();
  @Input() extendedStyles: {
    [key: string]: string;
  } = null;

  @Output() onDelete : EventEmitter<number> = new EventEmitter<number>();
  @Output() TextChange : EventEmitter<string> = new EventEmitter<string>();

  value : string;

  constructor() { }

  ngOnInit() {
  }

  deleteElement() {
  	this.onDelete.emit(this.Index);
  }
}
