@import "_variables";
@import "_mixins";

.coordinates {
  width: 382px;

  .latitude, .longitude, .icon-prop, .shorten-url-prop {
    display: flex;
    flex-direction: row;
    margin-top: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input {
      flex-grow: 1;
      flex-shrink: 1;

      &.validation-error {
        border-color: $error-color;
      }
    }

    .input-variable{
      width: 100%;
      .input {
        flex-grow: 1;
      }
    }
  }

  .map {
    overflow: hidden;
    margin-top: 10px;
    width: 360px;
    height: 360px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
