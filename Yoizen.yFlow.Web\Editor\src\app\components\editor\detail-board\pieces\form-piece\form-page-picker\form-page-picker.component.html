<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_PICKER_PICKERTITLE' | translate}}:</span>
  <app-input-with-variables
    [(value)]="page.pickerTitle"
    [validator]="page.isPickerTitleValid.bind(page)"
    [wideInput]="true"
    [isTextArea]="false"
    [disabled]="readOnly"
    class="input">
  </app-input-with-variables>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_PICKER_VARIABLE' | translate}}:</span>
  <app-variable-selector-input class="input"
                               [VariableData]="variableData"
                               (setVariable)="setVariable($event)"
                               [canSelectConstants]="false"
                               [readOnly]="readOnly"
                               [typeFilters]="getVariableTypes()"
                               [validator]="page.isVariableIdValid.bind(page, editorService)"></app-variable-selector-input>
</div>
<div class="items">
  <div class="title">{{ 'FORM_PAGE_TYPE_PICKER_ITEMS' | translate }}</div>
  <div class="item" *ngFor="let item of page.items let j = index">
    <div class="trash" (click)="deleteItem(j)" *ngIf="page.items.length > 1 && !readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'DELETE' | translate }}" placement="top" container="body"
         tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
    <div class="data">
      <div class="option">
        <span class="title">{{'FORM_PAGE_TYPE_PICKER_ITEM_TITLE' | translate}}:</span>
        <app-input-with-variables
          [(value)]="item.title"
          [validator]="item.isTitleValid.bind(item)"
          [wideInput]="true"
          [isTextArea]="false"
          [disabled]="readOnly"
          class="input">
        </app-input-with-variables>
      </div>
      <div class="option">
        <span class="title">{{'FORM_PAGE_TYPE_PICKER_ITEM_VALUE' | translate}}:</span>
        <app-input-with-variables
          [(value)]="item.value"
          [validator]="item.isValueValid.bind(item)"
          [wideInput]="true"
          [isTextArea]="false"
          [disabled]="readOnly"
          class="input">
        </app-input-with-variables>
      </div>
      <div class="option" *ngIf="false">
        <span class="title">{{'FORM_PAGE_TYPE_PICKER_ITEM_IDENTIFIER' | translate}}:</span>
        <app-input-with-variables
          [(value)]="item.identifier"
          [validator]="item.isIdentifierValid.bind(item)"
          [wideInput]="true"
          [isTextArea]="false"
          [disabled]="readOnly"
          class="input">
        </app-input-with-variables>
      </div>
    </div>
  </div>
  <div class="items-add" (click)="addNewItem(); $event.stopPropagation();" *ngIf="canAddItem() && !readOnly">
    <span class="fa fa-plus"></span> {{ 'FORM_PAGE_TYPE_PICKER_ITEMS_ADD' | translate }}
  </div>
</div>
