<div class="configurable-item">
  <div class="title">
    {{ 'CONFIGURATION_ACCOUNTLINKING_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_ACCOUNTLINKING_DESCRIPTION' | translate }}
  </div>
  <div class="implicit-variables" *ngIf="supportImplicitVariables">
    <div class="variables-info">{{'CONFIGURATION_ACCOUNTLINKING_VARIABLELIST' | translate}}</div>
    <div class="variables-table">
      <div class="variables-header">
        <div>{{'NAME' | translate}}</div>
        <div>{{'DESCRIPTION' | translate}}</div>
      </div>
      <div class="variable-row" *ngFor="let variable of implicitVariables">
        <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
        <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
      </div>
    </div>
  </div>
  <div class="accountLinking">
    <div class="accountLinking-info">{{'CONFIGURATION_ACCOUNTLINKING_INFO' | translate}}</div>
    <div class="accountLinking">
      <app-input-with-variables
        [placeholder]="'INPUT_URL'"
        [(value)]="model.Url"
        [disabled]="readOnly"
        [wideInput]="true"
        [variableFinder]="searchForVariable.bind(this)"
        [spellCheck]="false"
        [customVariableList]="implicitVariables"></app-input-with-variables>
    </div>
  </div>
</div>
