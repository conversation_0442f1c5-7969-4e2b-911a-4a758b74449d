<div class="flow {{getClass()}}" (click)="selectFlow($event, canEdit)" ngbTooltip="{{getTooltip()}}"
  data-toggle="tooltip" placement="right" tooltipClass="tooltip-comments">
  <div class="dropdown dropleft" *ngIf="showActions && (canEdit || canPublish) && !isFolder">
    <button class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      <span class="fas fa-ellipsis-h"></span>
    </button>
    <div class="dropdown-menu">
      <button *ngIf="canEdit && model.ActiveProductionVersion !== undefined && model.ActiveProductionVersion !== null"
        type="button" class="dropdown-item" (click)="selectFlow($event, false); $event.stopPropagation();">{{
        'SHOW_PROD_FLOW' | translate }}</button>
      <!--<button *ngIf="canEdit && showCloneButton" type="button" class="dropdown-item" (click)="onClone.emit(model); $event.stopPropagation();">{{ 'FLOW_CLONE' | translate }}</button>-->
      <button type="button" class="dropdown-item" (click)="onDownload.emit(model); $event.stopPropagation();">{{
        'FLOW_DOWNLOAD' | translate }}</button>
      <button *ngIf="model.ActiveProductionVersion !== undefined && model.ActiveProductionVersion !== null"
        type="button" class="dropdown-item" (click)="onDownloadProd.emit(model); $event.stopPropagation();">{{
        'FLOW_DOWNLOAD_PROD' | translate }}</button>
      <button *ngIf="canEdit" type="button" class="dropdown-item"
        (click)="onUpload.emit(model); $event.stopPropagation();">{{ 'FLOW_IMPORT' | translate }}</button>
      <button *ngIf="canEdit" type="button" class="dropdown-item"
        (click)="onDelete.emit(model); $event.stopPropagation();">{{ 'FLOW_DELETE' | translate }}</button>
      <button *ngIf="canPublish" type="button" class="dropdown-item"
        (click)="onPublish.emit(model); $event.stopPropagation();">{{ 'FLOW_PUBLISH' | translate }}</button>
    </div>
  </div>
  <div class="icon">
    <span class="{{getIcon()}}"></span>
  </div>
  <div class="module-icon" *ngIf="isFolder">
    <span class="fad fa-folders"></span>
  </div>
  <div class="contents">
    <div class="name">
      <p>{{model.name}}</p> <span class="id">({{model.id}})</span>
    </div>
    <div class="version">{{ 'FLOW_CURRENT_VERSION_DEV' | translate }}: {{model.ActiveStagingVersion?.number}}
      ({{model.ActiveStagingVersion?.createdAt | date}})</div>
    <div class="version" *ngIf="model.ActiveProductionVersion !== undefined && model.ActiveProductionVersion !== null">
      {{ 'FLOW_CURRENT_VERSION_PROD' | translate }}: {{model.ActiveProductionVersion?.number}}
      ({{model.ActiveProductionVersion?.createdAt | date}})</div>
    <div class="info" *ngIf="showFlowType">{{ 'NEW_FLOW_LITE' | translate }}: {{getFlowType()}}</div>
  </div>
</div>