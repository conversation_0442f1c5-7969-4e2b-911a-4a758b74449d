import { Component, OnInit, Input } from '@angular/core';
import {OperatorDefinitions, OperandTypeDefinition, OperatorType} from 'src/app/models/OperatorType';
import { SingleCondition } from 'src/app/models/commands/SingleCondition';
import { EditorService } from 'src/app/services/editor.service';

@Component({
  selector: 'app-single-condition',
  templateUrl: './single-condition.component.html',
  styleUrls: ['./single-condition.component.scss']
})
export class SingleConditionComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @Input() singleCondition: SingleCondition;
  constructor(private editorService: EditorService) { }

  ngOnInit() {
  }

  getOperators() {
    return OperatorDefinitions.CommandOperators.filter( op => {
      if (
        op.operatesOn.findIndex( operand => {
          return operand === OperandTypeDefinition.String || operand === OperandTypeDefinition.Any
        }) === -1) {
        return false;
      }

      if (op.value === 'null' || op.value === 'not_null') {
        return false;
      }

      return true;
    });
  }

  getInputValidator() {
    return str => { return this.singleCondition.isConditionValid(this.editorService); };
  }

  shouldDisplayOperand() : boolean {
    if (this.singleCondition.operation === OperatorType.IsNotNull ||
      this.singleCondition.operation === OperatorType.IsNull) {
      return false;
    }
    return true;
  }
}
