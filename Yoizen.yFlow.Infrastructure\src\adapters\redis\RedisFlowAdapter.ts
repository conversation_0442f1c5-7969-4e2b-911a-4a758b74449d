import { Op, TableHints } from 'sequelize';
import Flow from '../../../../Yoizen.yFlow.Web/models/flow';
import { IFlowPort } from '../../ports/IFlowPort';
import { CaseContext } from '../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/CaseContext';
import { config } from '../../../../Yoizen.yFlow.Helpers/src/Config';
import { dbRedis } from '../../../../Yoizen.yFlow.Helpers/src/ConfigRedis';
import { dbContext } from '../../../../Yoizen.yFlow.Helpers/src/ConfigDbContext';
import { createClient, RedisClientType } from 'redis';
import { logger } from '../../../../Yoizen.yFlow.Helpers/src/Logger';

export class RedisFlowAdapter implements IFlowPort {
    private client: RedisClientType;
    constructor() {
        const host = dbContext.host || dbRedis.host;
        const port = dbContext.port || dbRedis.port;
        const password = dbContext.password || dbRedis.password;
        this.client = createClient({
            socket: {
                host: host,
                port: port,
                passphrase: password,
                tls: true,
                keepAlive: 30000,  // Enviar keep-alive cada 30 segundos
                reconnectStrategy: (retries) => {
                    if (retries > 10) { // Máximo 10 reintentos
                        return new Error("Max retries reached");
                    }
                    const delay = Math.min(retries * 1000, 10000);
                    logger.info(`Intentando reconexión #${retries} en ${delay}ms`);
                    return delay;
                },
                connectTimeout: 5000,
            },
            password: password,
            name: config.client
        });
        /*} else {
            this.client = createClient({
                socket: {
                    host: dbRedis.host,
                    port: dbRedis.port,
                    passphrase: dbRedis.password,
                    tls: true,
                    //key: config.redisKey,
                },
                password: dbRedis.password,
                name: config.client
            });
        }*/

        this.onConnect();
        this.keepConnectionAlive();
    }

    private keepConnectionAlive() {
        setInterval(async () => {
            try {
                await this.client.ping();
            } catch (err) {
                logger.error({ error: err }, "Error en Keep-Alive Redis:");
            }
        }, 300000); // Cada 5 minutos
    }

    onConnect(): void {
        this.client.on("error", function (error) {
            logger.fatal({ error: error }, `CRITICAL - Error en el cliente de redis dbcontext`);
        });

        this.client.on('ready', function () {
            logger.info(`Cliente redis dbcontext ready`);
        });

        this.client.on('connect', function () {
            logger.info(`Cliente redis dbcontext connected`);
        });

        this.client.on('reconnecting', function () {
            logger.info(`Cliente redis dbcontext reconnecting`);
        });

        this.client.on('end', async function (error) {
            if (error.message.includes("Max retries reached")) { // Ejemplo
                logger.fatal({ error: error }, "CRITICAL - Reconexión fallida. Reiniciando cliente...");
                await this.client.disconnect(); // Limpia conexiones
                await this.client.connect(); // Reinicia
            }

            logger.fatal({ error: error }, `CRITICAL - Cliente redis dbcontext ended`);
        });
    }

    async GetAllProductiveMaster() {
        let flows = await Flow.findAll({
            attributes: ['id', 'name', 'ActiveProductionVersionId'],
            where: {
                ActiveStagingVersionId: {
                    [Op.ne]: null
                },
                master_flow_id: {
                    [Op.eq]: null
                }
            },
            tableHint: TableHints.NOLOCK
        });
        return flows;
    }

    async connect(): Promise<void> {
        try {
            await this.client.connect();
        } catch (error) {
            logger.error({ error: error }, `Error en el cliente de redis dbcontext`);
        }
    }

    private getKey(caseId: string): string {
        return `${config.client}:yFlow:caseContext:${caseId}`;
    }

    async SaveContext(caseContext: CaseContext): Promise<CaseContext> {
        if (typeof (caseContext.case_id) === 'number') {
            caseContext.case_id = caseContext.case_id.toString();
        }
        let session = await this.client.set(this.getKey(caseContext.case_id), JSON.stringify(caseContext.json), {
            EX: 60 * 60 * 24 * 7 //TODO: Dejar el *7 por variable de entorno
        });
        if (session == 'OK') {
            return caseContext;
        }
        return undefined;
    }

    async UpdateContext(caseContext: CaseContext): Promise<CaseContext> {
        return this.SaveContext(caseContext);
    }

    async GetContextByCaseId(caseId: string | number): Promise<CaseContext> {
        if (typeof (caseId) === 'number') {
            caseId = caseId.toString();
        }

        let context = await this.client.get(this.getKey(caseId));
        let caseContext: CaseContext;

        if (context) {
            caseContext = new CaseContext();
            caseContext.case_id = caseId;
            caseContext.json = JSON.parse(context);
        }
        return caseContext;
    }
}