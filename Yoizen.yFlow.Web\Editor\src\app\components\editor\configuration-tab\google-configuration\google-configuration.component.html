<div class="configurable-item" [ngClass]="{ 'invalid': !googleConfiguration.isValid(editorService) }">
  <div class="title">
    {{ 'CONFIGURATION_GOOGLE_CONFIGURATION_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_GOOGLE_CONFIGURATION_DESCRIPTION' | translate }}
  </div>
  <div class="data">
    <span class="title">{{ 'CONFIGURATION_GOOGLE_CONFIGURATION_API_KEY' | translate }}</span>
    <input type="text"
           class="input"
           [(ngModel)]="googleConfiguration.ApiKey"
           spellcheck="false"
           [placeholder]="'CONFIGURATION_GOOGLE_CONFIGURATION_API_KEY' | translate"
           [disabled]="readOnly" />
  </div>
</div>
