@import '_variables';
@import '_mixins';

:host > div {
  display: table-cell;
  vertical-align: middle;
  border-bottom: 1px solid $sidebarBorderColor;
  padding-left: 5px;
  padding-right: 5px;
}

:host:last-child {
  & > div {
    border-bottom: 1px none $sidebarBorderColor;
  }
}

.name {
  width: 200px;

  input {
    width: 100%;
    font-family: $fontFamilyMono;
  }
}

.trash {
  width: 30px;

  & > div {
    @include trash;
    cursor: pointer;

    &:hover {
      color: #555;
    }
  }
}

:host:hover {
  .trash {
    & > div {
      @include trashOver;
    }
  }
}
