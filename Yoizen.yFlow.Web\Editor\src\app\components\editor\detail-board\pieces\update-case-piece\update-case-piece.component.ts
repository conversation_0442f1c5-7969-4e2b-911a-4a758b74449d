import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {
  BusinessData,
  UpdateCaseActionTypes,
  UpdateCasePiece
} from "../../../../../models/pieces/UpdateCasePiece";
import { ChannelTypes } from 'src/app/models/ChannelType';

@Component({
  selector: 'app-update-case-piece',
  templateUrl: './update-case-piece.component.html',
  styleUrls: ['./update-case-piece.component.scss']
})
export class UpdateCasePieceComponent extends BasePieceVM implements OnInit {
  model: UpdateCasePiece;
  updateCaseActionTypes = UpdateCaseActionTypes;
  channelTypes = ChannelTypes;

  constructor(editorService: EditorService, public modalService: ModalService) { 
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as UpdateCasePiece;
  }

  deleteBussinessData(i: number) {
    this.model.businessData.splice(i, 1);
  }

  addBussinessData() {
    this.model.businessData.push(new BusinessData());
  }

}
