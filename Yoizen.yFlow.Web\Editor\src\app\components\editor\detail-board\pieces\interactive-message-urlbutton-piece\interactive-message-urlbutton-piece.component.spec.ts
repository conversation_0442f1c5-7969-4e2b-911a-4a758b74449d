import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InteractiveMessageUrlbuttonPieceComponent } from './interactive-message-urlbutton-piece.component';

describe('InteractiveMessageUrlbuttonPieceComponent', () => {
  let component: InteractiveMessageUrlbuttonPieceComponent;
  let fixture: ComponentFixture<InteractiveMessageUrlbuttonPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InteractiveMessageUrlbuttonPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InteractiveMessageUrlbuttonPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
